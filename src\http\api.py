import inspect
from flask import Flask, jsonify, request

class httpapi:

    _app: Flask = None
    _listen: str = None
    _port: int = None
    service = None

    _cache: dict = None

    def __init__(self, backend, listen: str, port: int):
        self._listen = listen
        self._port = port
        self._cache = {}
        self.service = backend
        self._app = Flask(__name__)
        self._app.add_url_rule('/<path:any_path>', view_func=self.__route_gateway, methods=['GET', 'POST', 'OPTIONS', 'PUT', 'DELETE', 'PATCH'])
        
        import logging
        logging.getLogger('werkzeug').disabled = True

    def start(self):
        self._app.run(port=self._port, host=self._listen, threaded=True)

    def __api_response(self, code: int, success: bool, message: str, result: None = None):

        _dict = {
            "code": 200 if success else 500,
            "message": message,
            "data": result
        }

        if _dict["message"] is None:
            del _dict["message"]
        if _dict["data"] is None:
            del _dict["data"]

        response = jsonify(_dict)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.code = code
        return response

    def __api_success(self, message: str = None, result: None = None):
        return self.__api_response(200, True, message, result)

    def __api_fail(self, message: str, result: None = None):
        return self.__api_response(200, False, message, result)

    def __route_gateway(self, any_path, **kwargs):

        if request.method == 'OPTIONS':
            def __response():
                return self.__api_success(message="CORS preflight response", result={
                    "Allow": "GET, POST, OPTIONS, PUT, DELETE, PATCH",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS, PUT, DELETE, PATCH",
                    "Access-Control-Allow-Headers": "Content-Type"
                })

            return __response()

        _split = any_path.split("/")
        _module_name = f"src.http.endpoint.{'.'.join(_split[:-1])}"
        _method_name = _split[-1]
        # print(f"load `{_module_name}`: {_method_name}({kwargs})")

        # filter invalid module names, like "favicon.ico"
        if("." in _method_name):
            return self.__api_fail(f"invalid route `{any_path}`")

        # search in cache
        # if not found, import the module
        _handler = None
        if self._cache.get(_module_name) is not None:
            _handler = self._cache[_module_name]

        if _handler is None:
            try:
                # import py file then invoke
                _handler = __import__(_module_name, fromlist=["*"])
                inspect.getmembers(_handler, inspect.isfunction)
                self.service.log_debug(f"load `{_module_name}`: {_method_name}({request.args.to_dict()})")

                # for debug, do not cache the module
                self._cache[_module_name] = _handler
            except ImportError as e:
                self.service.log_error(f"failed to import route `{any_path}`: {e}")
                return self.__api_fail(f"no such file `{any_path}`")

        # invoke
        _result = None
        try:
            _handler = getattr(_handler, _method_name)
            _result = _handler(self, request.args.to_dict(), request.get_json(silent=True))
            if not callable(_handler):
                return self.__api_fail(f"route `{any_path}` does not exist")

        except Exception as e:
            self.service.log_debug(f"failed to invoke route `{any_path}`: {e}")
            return self.__api_fail(e.__str__())

        if not isinstance(_result, tuple) \
                or len(_result) != 2 \
                or not isinstance(_result[0], int):
                # or not isinstance(_result[1], dict):
            return self.__api_fail(f"route `{any_path}` does not exist")

        if _result[0] >= 0:
            return self.__api_success(result=_result[1])

        return self.__api_fail(message=_result[1])

