import{d as M,A as c,e as n,o as t,v as l,s as d,g as e,F as o,B as p,D as S,q as x}from"../jse/index-index-Y3_OtjO-.js";/* empty css                                                              */import{_ as h}from"./bootstrap-MyT3sENS.js";const k=M({__name:"gcode",setup(v){const u=["G0 X10 Y10 Z0.3","M104 S200","M109 S200","M109 S200","M109 S200","M140 S60","M190 S60"],m=a=>a.match(/^[GgMmJj]/)?"command-char":"command-char-args";return(a,s)=>{const i=c("gcode-container"),_=c("gcode-input-container");return t(),n(o,null,[l(i,{class:""},{default:d(()=>[e("ul",null,[(t(),n(o,null,p(u,(g,f)=>e("li",{key:f},[(t(!0),n(o,null,p(g.split(" "),(r,C)=>(t(),n("span",{key:C,class:S(m(r))},x(r),3))),128))])),64))])]),_:1}),l(_,null,{default:d(()=>s[0]||(s[0]=[e("input",{type:"text",placeholder:"发送 G-code 指令"},null,-1),e("div",null,[e("span",null,"发送")],-1)])),_:1})],64)}}}),D=h(k,[["__scopeId","data-v-712d9cdb"]]);export{D as default};
