import{u as m,_ as l}from"./form-1Qpdj_eE.js";import{E as c,F as p}from"./bootstrap-MyT3sENS.js";import{a as u,b as g}from"./settings-BETc9HZJ.js";import{d,y as f,j as _,o as G,s as r,v as t,g as I,u as s}from"../jse/index-index-Y3_OtjO-.js";import"./index-DGcxnQ4T.js";const N=d({__name:"gpio",setup(O){const a=c(),[n,i]=m({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{u(e).then(()=>{a.success("设置已保存")}).catch(o=>{a.error(`保存失败: ${o.message}`)})},resetButtonOptions:{show:!1},schema:[{component:"InputNumber",fieldName:"cameraTriggerGpio",label:"相机快门 IO"}]});return f(()=>{g().then(e=>{console.log(e),i.setValues({cameraTriggerGpio:e.cameraTriggerGpio})})}),(e,o)=>(G(),_(s(l),{description:"本页面包含GPIO相关设定",title:"I/O 接口和触发"},{default:r(()=>[t(s(p),{title:"GPIO功能绑定"},{default:r(()=>[t(s(n))]),_:1}),o[0]||(o[0]=I("br",null,null,-1))]),_:1}))}});export{N as default};
