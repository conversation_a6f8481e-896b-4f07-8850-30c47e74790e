<template>
  <Page description="本页面用于配置物理硬件设备、初始化G代码、设置默认参数等" title="硬件">

    <NCard title="串口配置"><SerialDeviceForm/></NCard><br/>
    <NCard title="电机参数"><StepperForm/></NCard><br/>
    
    <NCard title="初始化G代码">
      <div class="form-is-required flex-row items-center pb-6 relative flex flex-shrink-0">
        
        <form
          :label-width="100"
          :label-placement="'left'"
          :label-align="'left'"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          class="w-full leading-6"
        >
          <span class="text-sm">本选项包含系统启动时对下位机初始化的 gcode 预编程</span><br/>
          <span class="text-sm">包含简单的表达式支持</span>
          <NDivider class="my-4" />

          <textarea
            ref="gcodeRef"
            vben-form="GCodeForm"
            v-model="GCodeForm.gcode"
            class="w-full h-96 p-2 border rounded-md"
          ></textarea>

        </form>
      </div>
      <NButton type="primary" @click="submitGcode">提交</NButton>
    </NCard>

  </Page>
</template>

<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { NButton, NCard, NDivider, useMessage } from 'naive-ui';
import { useVbenForm } from '#/adapter/form';
import { getInitialGCode, getLocalSerialDevices,
  getStepperArgs, setStepperArgs, setInitialGCode, getSerialConfig, setSerialConfig } from '#/api/index';
import { ref, onMounted } from 'vue';

const message = useMessage();
const [SerialDeviceForm, serialDeviceFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    setSerialConfig(values).then(() => {
      message.success('串口配置已保存');
    }).catch((error) => {
      console.error(`保存失败: ${error.message}`);
    });
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'serialPort',
      label: '串口',
      rules: "selectRequired",
      componentProps: {
        api: getLocalSerialDevices,
        afterFetch: (_:any) => _.devices.map((item: any) => ({
            label: item.device,
            value: item.device,
          })),
      },
    },
    {
      component: "InputNumber",
      fieldName: "baudRate",
      label: "波特率",
      componentProps:{
        showButtons: false,
      }
    },
  ],
});

const [StepperForm, stepperFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  wrapperClass: 'grid-cols-3 md:grid-cols-3 lg:grid-cols-3',
  handleSubmit: (values) => {
    setStepperArgs(values).then(() => {
      message.success('电机参数已保存');
    }).catch((error) => {
      message.error(`保存失败: ${error.message}`);
    });
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: "InputNumber",
      label: "X轴微步",
      fieldName: "xMicrostep",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Y轴微步",
      fieldName: "yMicrostep",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Z轴微步",
      fieldName: "zMicrostep",
      componentProps: {
        showButtons: false,
      },
    },

    // {
    //   component: "Divider",
    //   fieldName: "divider1",
    // },

    {
      component: "InputNumber",
      label: "X电机电流 (mA)",
      fieldName: "xStepperAmps",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Y电机电流 (mA)",
      fieldName: "yStepperAmps",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Z电机电流 (mA)",
      fieldName: "zStepperAmps",
      componentProps: {
        showButtons: false,
      },
    },

    // {
    //   component: "Divider",
    //   fieldName: "divider3",
    // },

    {
      component: "InputNumber",
      label: "X电机给进率",
      fieldName: "xStepperFeedrate",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Y电机给进率",
      fieldName: "yStepperFeedrate",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Z电机给进率",
      fieldName: "zStepperFeedrate",
      componentProps: {
        showButtons: false,
      },
    },

    //   {
    //   component: "Divider",
    //   fieldName: "divider2",
    // },

    {
      component: "InputNumber",
      label: "X电机比例 (step/mm)",
      fieldName: "xStepperRatio",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Y电机比例 (step/mm)",
      fieldName: "yStepperRatio",
      componentProps: {
        showButtons: false,
      },
    },
    {
      component: "InputNumber",
      label: "Z电机比例 (step/mm)",
      fieldName: "zStepperRatio",
      componentProps: {
        showButtons: false,
      },
    },

  ],
});

const [GCodeForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: "VbenInput",
      fieldName: "gcode",
      componentProps: {
        type: 'textarea',
        rows: 10,
        placeholder: '请输入初始化G代码',
        style: { width: '100%', height: '500px' },
      },
    }
  ],
});

function submitGcode() {
  setInitialGCode({"gcode": gcodeRef.value!.value}).then(() => {
    message.success('初始化G代码已保存');
  }).catch((error) => {
    message.error(`保存失败: ${error.message}`);
  });
}

const gcodeRef = ref<HTMLTextAreaElement | null>(null);
onMounted(() => {
    // 这里可以直接操作 textarea 元素
    getInitialGCode().then(res=>{
      gcodeRef!.value.value = res.gcode.join('\n');
    });

    getStepperArgs().then(res=>{
      console.log(res);
      stepperFormApi.setValues({
        xMicrostep: res.x.microsteps,
        yMicrostep: res.y.microsteps,
        zMicrostep: res.z.microsteps,
        xStepperAmps: res.x.current,
        yStepperAmps: res.y.current,
        zStepperAmps: res.z.current,
        xStepperFeedrate: res.x.feedrate,
        yStepperFeedrate: res.y.feedrate,
        zStepperFeedrate: res.z.feedrate,
        xStepperRatio: res.x.ratio,
        yStepperRatio: res.y.ratio,
        zStepperRatio: res.z.ratio,
      });
    });

    getSerialConfig().then(res => {
      serialDeviceFormApi.setValues({
        serialPort: res.serialPort,
        baudRate: res.baudRate,
      });
    });
});

</script>
