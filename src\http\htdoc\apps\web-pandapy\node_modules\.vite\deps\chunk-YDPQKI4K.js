import {
  dark_default as dark_default5,
  light_default as light_default5
} from "./chunk-I44DGCY4.js";
import {
  dark_default as dark_default3,
  light_default as light_default3
} from "./chunk-XKIBEKHX.js";
import {
  dark_default as dark_default4,
  light_default as light_default4
} from "./chunk-TPTSGLBB.js";
import {
  dark_default as dark_default2,
  light_default as light_default2
} from "./chunk-T4COAYJY.js";
import {
  dark_default,
  light_default
} from "./chunk-2AZKBT6I.js";
import {
  changeColor,
  composite,
  createTheme
} from "./chunk-HXOHBLE5.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/date-picker/styles/_common.mjs
var common_default = {
  itemSize: "24px",
  itemCellWidth: "38px",
  itemCellHeight: "32px",
  scrollItemWidth: "80px",
  scrollItemHeight: "40px",
  panelExtraFooterPadding: "8px 12px",
  panelActionPadding: "8px 12px",
  calendarTitlePadding: "0",
  calendarTitleHeight: "28px",
  arrowSize: "14px",
  panelHeaderPadding: "8px 12px",
  calendarDaysHeight: "32px",
  calendarTitleGridTempateColumns: "28px 28px 1fr 28px 28px",
  // type
  calendarLeftPaddingDate: "6px 12px 4px 12px",
  calendarLeftPaddingDatetime: "4px 12px",
  calendarLeftPaddingDaterange: "6px 12px 4px 12px",
  calendarLeftPaddingDatetimerange: "4px 12px",
  calendarLeftPaddingMonth: "0",
  // TODO: make it actually effective
  calendarLeftPaddingYear: "0",
  calendarLeftPaddingQuarter: "0",
  calendarLeftPaddingMonthrange: "0",
  calendarLeftPaddingQuarterrange: "0",
  calendarLeftPaddingYearrange: "0",
  calendarLeftPaddingWeek: "6px 12px 4px 12px",
  calendarRightPaddingDate: "6px 12px 4px 12px",
  calendarRightPaddingDatetime: "4px 12px",
  calendarRightPaddingDaterange: "6px 12px 4px 12px",
  calendarRightPaddingDatetimerange: "4px 12px",
  calendarRightPaddingMonth: "0",
  calendarRightPaddingYear: "0",
  calendarRightPaddingQuarter: "0",
  calendarRightPaddingMonthrange: "0",
  calendarRightPaddingQuarterrange: "0",
  calendarRightPaddingYearrange: "0",
  calendarRightPaddingWeek: "0"
};

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/date-picker/styles/light.mjs
function self(vars) {
  const {
    hoverColor,
    fontSize,
    textColor2,
    textColorDisabled,
    popoverColor,
    primaryColor,
    borderRadiusSmall,
    iconColor,
    iconColorDisabled,
    textColor1,
    dividerColor,
    boxShadow2,
    borderRadius,
    fontWeightStrong
  } = vars;
  return Object.assign(Object.assign({}, common_default), {
    itemFontSize: fontSize,
    calendarDaysFontSize: fontSize,
    calendarTitleFontSize: fontSize,
    itemTextColor: textColor2,
    itemTextColorDisabled: textColorDisabled,
    itemTextColorActive: popoverColor,
    itemTextColorCurrent: primaryColor,
    itemColorIncluded: changeColor(primaryColor, {
      alpha: 0.1
    }),
    itemColorHover: hoverColor,
    itemColorDisabled: hoverColor,
    itemColorActive: primaryColor,
    itemBorderRadius: borderRadiusSmall,
    panelColor: popoverColor,
    panelTextColor: textColor2,
    arrowColor: iconColor,
    calendarTitleTextColor: textColor1,
    calendarTitleColorHover: hoverColor,
    calendarDaysTextColor: textColor2,
    panelHeaderDividerColor: dividerColor,
    calendarDaysDividerColor: dividerColor,
    calendarDividerColor: dividerColor,
    panelActionDividerColor: dividerColor,
    panelBoxShadow: boxShadow2,
    panelBorderRadius: borderRadius,
    calendarTitleFontWeight: fontWeightStrong,
    scrollItemBorderRadius: borderRadius,
    iconColor,
    iconColorDisabled
  });
}
var datePickerLight = createTheme({
  name: "DatePicker",
  common: light_default,
  peers: {
    Input: light_default3,
    Button: light_default4,
    TimePicker: light_default5,
    Scrollbar: light_default2
  },
  self
});
var light_default6 = datePickerLight;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/date-picker/styles/dark.mjs
var datePickerDark = {
  name: "DatePicker",
  common: dark_default,
  peers: {
    Input: dark_default3,
    Button: dark_default4,
    TimePicker: dark_default5,
    Scrollbar: dark_default2
  },
  self(vars) {
    const {
      popoverColor,
      hoverColor,
      primaryColor
    } = vars;
    const commonSelf = self(vars);
    commonSelf.itemColorDisabled = composite(popoverColor, hoverColor);
    commonSelf.itemColorIncluded = changeColor(primaryColor, {
      alpha: 0.15
    });
    commonSelf.itemColorHover = composite(popoverColor, hoverColor);
    return commonSelf;
  }
};
var dark_default6 = datePickerDark;

export {
  light_default6 as light_default,
  dark_default6 as dark_default
};
//# sourceMappingURL=chunk-YDPQKI4K.js.map
