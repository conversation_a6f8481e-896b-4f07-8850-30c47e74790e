import {
  dark_default3 as dark_default2,
  dark_default6 as dark_default3,
  internalSelectMenuRtl,
  internalSelectionRtl,
  light_default3 as light_default2,
  light_default6 as light_default3,
  rtl_default,
  tagRtl
} from "./chunk-T4COAYJY.js";
import {
  dark_default,
  light_default
} from "./chunk-2AZKBT6I.js";
import {
  c,
  createTheme
} from "./chunk-HXOHBLE5.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/select/styles/light.mjs
function self(vars) {
  const {
    boxShadow2
  } = vars;
  return {
    menuBoxShadow: boxShadow2
  };
}
var selectLight = createTheme({
  name: "Select",
  common: light_default,
  peers: {
    InternalSelection: light_default3,
    InternalSelectMenu: light_default2
  },
  self
});
var light_default4 = selectLight;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/select/styles/dark.mjs
var selectDark = {
  name: "Select",
  common: dark_default,
  peers: {
    InternalSelection: dark_default3,
    InternalSelectMenu: dark_default2
  },
  self
};
var dark_default4 = selectDark;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/select/styles/rtl.mjs
var selectRtl = {
  name: "Select",
  style: c([]),
  peers: [internalSelectionRtl, internalSelectMenuRtl, tagRtl, rtl_default]
};

export {
  light_default4 as light_default,
  dark_default4 as dark_default,
  selectRtl
};
//# sourceMappingURL=chunk-LJFT4ETM.js.map
