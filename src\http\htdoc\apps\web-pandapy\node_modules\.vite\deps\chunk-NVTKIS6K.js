import {
  dark_default as dark_default2,
  inputRtl,
  light_default as light_default2
} from "./chunk-XKIBEKHX.js";
import {
  buttonRtl,
  dark_default as dark_default3,
  light_default as light_default3
} from "./chunk-TPTSGLBB.js";
import {
  dark_default,
  light_default
} from "./chunk-2AZKBT6I.js";
import {
  cB,
  cM,
  createTheme
} from "./chunk-HXOHBLE5.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/input-number/src/styles/rtl.cssr.mjs
var rtl_cssr_default = cB("input-number", [cM("rtl", `
 direction: rtl;
 `)]);

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/input-number/styles/rtl.mjs
var inputNumberRtl = {
  name: "InputNumber",
  style: rtl_cssr_default,
  peers: [inputRtl, buttonRtl]
};

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/input-number/styles/dark.mjs
var inputNumberDark = {
  name: "InputNumber",
  common: dark_default,
  peers: {
    Button: dark_default3,
    Input: dark_default2
  },
  self(vars) {
    const {
      textColorDisabled
    } = vars;
    return {
      iconColorDisabled: textColorDisabled
    };
  }
};
var dark_default4 = inputNumberDark;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/input-number/styles/light.mjs
function self(vars) {
  const {
    textColorDisabled
  } = vars;
  return {
    iconColorDisabled: textColorDisabled
  };
}
var inputNumberLight = createTheme({
  name: "InputNumber",
  common: light_default,
  peers: {
    Button: light_default3,
    Input: light_default2
  },
  self
});
var light_default4 = inputNumberLight;

export {
  inputNumberRtl,
  dark_default4 as dark_default,
  light_default4 as light_default
};
//# sourceMappingURL=chunk-NVTKIS6K.js.map
