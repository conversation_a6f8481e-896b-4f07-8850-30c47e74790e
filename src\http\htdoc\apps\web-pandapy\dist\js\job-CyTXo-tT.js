var n=(e,r,a)=>new Promise((g,b)=>{var u=t=>{try{s(a.next(t))}catch(c){b(c)}},i=t=>{try{s(a.throw(t))}catch(c){b(c)}},s=t=>t.done?g(t.value):Promise.resolve(t.value).then(u,i);s((a=a.apply(e,r)).next())});import{r as o}from"./bootstrap-MyT3sENS.js";function j(){return n(this,null,function*(){return o.get("/job/manage/getJobList")})}function m(e){return n(this,null,function*(){return o.get(`/job/manage/openJob?name=${e}`)})}function y(){return n(this,null,function*(){return o.get("/job/manage/closeJob")})}function l(){return n(this,null,function*(){return o.get("/job/manage/saveJob")})}function C(){return n(this,null,function*(){return o.get("/job/manage/getCurrentJob")})}function $(e){return n(this,null,function*(){return o.get(`/job/manage/createNewJob?name=${e}`)})}function p(e,r){return n(this,null,function*(){return o.post(`/job/manage/setJobPrefrences?category=${e}`,r)})}function P(e){return n(this,null,function*(){return o.get(`/job/manage/getJobPrefrences?category=${e}`)})}function h(){return n(this,null,function*(){return o.get("/job/manage/getJobStatus")})}function v(e){return n(this,null,function*(){return o.get(`/job/manage/jobControl?action=${e}`)})}function w(e){return n(this,null,function*(){return o.get(`/job/manage/jobMachineControl?action=${e}`)})}export{h as a,j as b,y as c,v as d,$ as e,P as f,C as g,p as h,w as j,m as o,l as s};
