import{L as H,a3 as q,a7 as se,k as D,B as W}from"./bootstrap-MyT3sENS.js";import{r as p,c as k,I as oe,d as J,i as re,h as T,L as ue,y as ae,M as de,N as ce,t as _}from"../jse/index-index-Y3_OtjO-.js";import{c as j,a as fe,b as he}from"./Follower-B0-DuUBY.js";function K(t){return t&-t}class Q{constructor(l,i){this.l=l,this.min=i;const o=new Array(l+1);for(let s=0;s<l+1;++s)o[s]=0;this.ft=o}add(l,i){if(i===0)return;const{l:o,ft:s}=this;for(l+=1;l<=o;)s[l]+=i,l+=K(l)}get(l){return this.sum(l+1)-this.sum(l)}sum(l){if(l===void 0&&(l=this.l),l<=0)return 0;const{ft:i,min:o,l:s}=this;if(l>s)throw new Error("[FinweckTree.sum]: `i` is larger than length.");let a=l*o;for(;l>0;)a+=i[l],l-=K(l);return a}getBound(l){let i=0,o=this.l;for(;o>i;){const s=Math.floor((i+o)/2),a=this.sum(s);if(a>l){o=s;continue}else if(a<l){if(i===s)return this.sum(i+1)<=l?i+1:s;i=s}else return s}return i}}let E;function me(){return typeof document=="undefined"?!1:(E===void 0&&("matchMedia"in window?E=window.matchMedia("(pointer:coarse)").matches:E=!1),E)}let P;function U(){return typeof document=="undefined"?1:(P===void 0&&(P="chrome"in window?window.devicePixelRatio:1),P)}const Z="VVirtualListXScroll";function ve({columnsRef:t,renderColRef:l,renderItemWithColsRef:i}){const o=p(0),s=p(0),a=k(()=>{const r=t.value;if(r.length===0)return null;const g=new Q(r.length,0);return r.forEach((R,y)=>{g.add(y,R.width)}),g}),w=H(()=>{const r=a.value;return r!==null?Math.max(r.getBound(s.value)-1,0):0}),I=r=>{const g=a.value;return g!==null?g.sum(r):0},d=H(()=>{const r=a.value;return r!==null?Math.min(r.getBound(s.value+o.value)+1,t.value.length-1):0});return oe(Z,{startIndexRef:w,endIndexRef:d,columnsRef:t,renderColRef:l,renderItemWithColsRef:i,getLeft:I}),{listWidthRef:o,scrollLeftRef:s}}const G=J({name:"VirtualListRow",props:{index:{type:Number,required:!0},item:{type:Object,required:!0}},setup(){const{startIndexRef:t,endIndexRef:l,columnsRef:i,getLeft:o,renderColRef:s,renderItemWithColsRef:a}=re(Z);return{startIndex:t,endIndex:l,columns:i,renderCol:s,renderItemWithCols:a,getLeft:o}},render(){const{startIndex:t,endIndex:l,columns:i,renderCol:o,renderItemWithCols:s,getLeft:a,item:w}=this;if(s!=null)return s({itemIndex:this.index,startColIndex:t,endColIndex:l,allColumns:i,item:w,getLeft:a});if(o!=null){const I=[];for(let d=t;d<=l;++d){const r=i[d];I.push(o({column:r,left:a(d),item:w}))}return I}return null}}),ge=j(".v-vl",{maxHeight:"inherit",height:"100%",overflow:"auto",minWidth:"1px"},[j("&:not(.v-vl--show-scrollbar)",{scrollbarWidth:"none"},[j("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",{width:0,height:0,display:"none"})])]),be=J({name:"VirtualList",inheritAttrs:!1,props:{showScrollbar:{type:Boolean,default:!0},columns:{type:Array,default:()=>[]},renderCol:Function,renderItemWithCols:Function,items:{type:Array,default:()=>[]},itemSize:{type:Number,required:!0},itemResizable:Boolean,itemsStyle:[String,Object],visibleItemsTag:{type:[String,Object],default:"div"},visibleItemsProps:Object,ignoreItemResize:Boolean,onScroll:Function,onWheel:Function,onResize:Function,defaultScrollKey:[Number,String],defaultScrollIndex:Number,keyField:{type:String,default:"key"},paddingTop:{type:[Number,String],default:0},paddingBottom:{type:[Number,String],default:0}},setup(t){const l=se();ge.mount({id:"vueuc/virtual-list",head:!0,anchorMetaName:fe,ssr:l}),ae(()=>{const{defaultScrollIndex:e,defaultScrollKey:n}=t;e!=null?M({index:e}):n!=null&&M({key:n})});let i=!1,o=!1;de(()=>{if(i=!1,!o){o=!0;return}M({top:x.value,left:w.value})}),ce(()=>{i=!0,o||(o=!0)});const s=H(()=>{if(t.renderCol==null&&t.renderItemWithCols==null||t.columns.length===0)return;let e=0;return t.columns.forEach(n=>{e+=n.width}),e}),a=k(()=>{const e=new Map,{keyField:n}=t;return t.items.forEach((u,f)=>{e.set(u[n],f)}),e}),{scrollLeftRef:w,listWidthRef:I}=ve({columnsRef:_(t,"columns"),renderColRef:_(t,"renderCol"),renderItemWithColsRef:_(t,"renderItemWithCols")}),d=p(null),r=p(void 0),g=new Map,R=k(()=>{const{items:e,itemSize:n,keyField:u}=t,f=new Q(e.length,n);return e.forEach((h,m)=>{const c=h[u],v=g.get(c);v!==void 0&&f.add(m,v)}),f}),y=p(0),x=p(0),L=H(()=>Math.max(R.value.getBound(x.value-D(t.paddingTop))-1,0)),ee=k(()=>{const{value:e}=r;if(e===void 0)return[];const{items:n,itemSize:u}=t,f=L.value,h=Math.min(f+Math.ceil(e/u+1),n.length-1),m=[];for(let c=f;c<=h;++c)m.push(n[c]);return m}),M=(e,n)=>{if(typeof e=="number"){F(e,n,"auto");return}const{left:u,top:f,index:h,key:m,position:c,behavior:v,debounce:b=!0}=e;if(u!==void 0||f!==void 0)F(u,f,v);else if(h!==void 0)X(h,v,b);else if(m!==void 0){const A=a.value.get(m);A!==void 0&&X(A,v,b)}else c==="bottom"?F(0,Number.MAX_SAFE_INTEGER,v):c==="top"&&F(0,0,v)};let C,B=null;function X(e,n,u){const{value:f}=R,h=f.sum(e)+D(t.paddingTop);if(!u)d.value.scrollTo({left:0,top:h,behavior:n});else{C=e,B!==null&&window.clearTimeout(B),B=window.setTimeout(()=>{C=void 0,B=null},16);const{scrollTop:m,offsetHeight:c}=d.value;if(h>m){const v=f.get(e);h+v<=m+c||d.value.scrollTo({left:0,top:h+v-c,behavior:n})}else d.value.scrollTo({left:0,top:h,behavior:n})}}function F(e,n,u){d.value.scrollTo({left:e,top:n,behavior:u})}function te(e,n){var u,f,h;if(i||t.ignoreItemResize||$(n.target))return;const{value:m}=R,c=a.value.get(e),v=m.get(c),b=(h=(f=(u=n.borderBoxSize)===null||u===void 0?void 0:u[0])===null||f===void 0?void 0:f.blockSize)!==null&&h!==void 0?h:n.contentRect.height;if(b===v)return;b-t.itemSize===0?g.delete(e):g.set(e,b-t.itemSize);const z=b-v;if(z===0)return;m.add(c,z);const S=d.value;if(S!=null){if(C===void 0){const O=m.sum(c);S.scrollTop>O&&S.scrollBy(0,z)}else if(c<C)S.scrollBy(0,z);else if(c===C){const O=m.sum(c);b+O>S.scrollTop+S.offsetHeight&&S.scrollBy(0,z)}V()}y.value++}const Y=!me();let N=!1;function ne(e){var n;(n=t.onScroll)===null||n===void 0||n.call(t,e),(!Y||!N)&&V()}function le(e){var n;if((n=t.onWheel)===null||n===void 0||n.call(t,e),Y){const u=d.value;if(u!=null){if(e.deltaX===0&&(u.scrollTop===0&&e.deltaY<=0||u.scrollTop+u.offsetHeight>=u.scrollHeight&&e.deltaY>=0))return;e.preventDefault(),u.scrollTop+=e.deltaY/U(),u.scrollLeft+=e.deltaX/U(),V(),N=!0,he(()=>{N=!1})}}}function ie(e){if(i||$(e.target))return;if(t.renderCol==null&&t.renderItemWithCols==null){if(e.contentRect.height===r.value)return}else if(e.contentRect.height===r.value&&e.contentRect.width===I.value)return;r.value=e.contentRect.height,I.value=e.contentRect.width;const{onResize:n}=t;n!==void 0&&n(e)}function V(){const{value:e}=d;e!=null&&(x.value=e.scrollTop,w.value=e.scrollLeft)}function $(e){let n=e;for(;n!==null;){if(n.style.display==="none")return!0;n=n.parentElement}return!1}return{listHeight:r,listStyle:{overflow:"auto"},keyToIndex:a,itemsStyle:k(()=>{const{itemResizable:e}=t,n=W(R.value.sum());return y.value,[t.itemsStyle,{boxSizing:"content-box",width:W(s.value),height:e?"":n,minHeight:e?n:"",paddingTop:W(t.paddingTop),paddingBottom:W(t.paddingBottom)}]}),visibleItemsStyle:k(()=>(y.value,{transform:`translateY(${W(R.value.sum(L.value))})`})),viewportItems:ee,listElRef:d,itemsElRef:p(null),scrollTo:M,handleListResize:ie,handleListScroll:ne,handleListWheel:le,handleItemResize:te}},render(){const{itemResizable:t,keyField:l,keyToIndex:i,visibleItemsTag:o}=this;return T(q,{onResize:this.handleListResize},{default:()=>{var s,a;return T("div",ue(this.$attrs,{class:["v-vl",this.showScrollbar&&"v-vl--show-scrollbar"],onScroll:this.handleListScroll,onWheel:this.handleListWheel,ref:"listElRef"}),[this.items.length!==0?T("div",{ref:"itemsElRef",class:"v-vl-items",style:this.itemsStyle},[T(o,Object.assign({class:"v-vl-visible-items",style:this.visibleItemsStyle},this.visibleItemsProps),{default:()=>{const{renderCol:w,renderItemWithCols:I}=this;return this.viewportItems.map(d=>{const r=d[l],g=i.get(r),R=w!=null?T(G,{index:g,item:d}):void 0,y=I!=null?T(G,{index:g,item:d}):void 0,x=this.$slots.default({item:d,renderedCols:R,renderedItemWithCols:y,index:g})[0];return t?T(q,{key:r,onResize:L=>this.handleItemResize(r,L)},{default:()=>x}):(x.key=r,x)})}})]):(a=(s=this.$slots).empty)===null||a===void 0?void 0:a.call(s)])}})}});export{be as V};
