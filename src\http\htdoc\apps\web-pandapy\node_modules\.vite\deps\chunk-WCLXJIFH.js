import {
  cB,
  cM
} from "./chunk-HXOHBLE5.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/_common.mjs
var common_default = {
  gapSmall: "4px 8px",
  gapMedium: "8px 12px",
  gapLarge: "12px 16px"
};

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/dark.mjs
var spaceDark = {
  name: "Space",
  self() {
    return common_default;
  }
};
var dark_default = spaceDark;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/src/styles/rtl.cssr.mjs
var rtl_cssr_default = cB("space", [cM("rtl", `
 direction: rtl;
 `)]);

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/rtl.mjs
var spaceRtl = {
  name: "Space",
  style: rtl_cssr_default
};

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/light.mjs
function self() {
  return common_default;
}
var spaceLight = {
  name: "Space",
  self
};
var light_default = spaceLight;

export {
  dark_default,
  light_default,
  spaceRtl
};
//# sourceMappingURL=chunk-WCLXJIFH.js.map
