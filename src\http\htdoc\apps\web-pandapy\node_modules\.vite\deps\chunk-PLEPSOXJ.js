import {
  getConfig,
  index_esm_default,
  require_xe_utils
} from "./chunk-TV7URO3H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/utils.js
var import_xe_utils = __toESM(require_xe_utils());
function isEnableConf(conf) {
  return conf && conf.enabled !== false;
}
function nextZIndex() {
  return index_esm_default.getNext();
}
function getLastZIndex() {
  return index_esm_default.getCurrent();
}
function getFuncText(content, args) {
  if (content) {
    const translate = getConfig().translate;
    return import_xe_utils.default.toValueString(translate ? translate("" + content, args) : content);
  }
  return "";
}
function eqEmptyValue(cellValue) {
  return cellValue === null || cellValue === void 0 || cellValue === "";
}
function handleBooleanDefaultValue(value) {
  return import_xe_utils.default.isBoolean(value) ? value : null;
}

export {
  isEnableConf,
  nextZIndex,
  getLastZIndex,
  getFuncText,
  eqEmptyValue,
  handleBooleanDefaultValue
};
//# sourceMappingURL=chunk-PLEPSOXJ.js.map
