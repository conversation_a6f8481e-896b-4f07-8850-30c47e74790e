import{u as r,_ as u}from"./form-1Qpdj_eE.js";import{E as d,F as m}from"./bootstrap-MyT3sENS.js";import{s as c,g as f}from"./settings-BETc9HZJ.js";import{d as p,y as g,j as b,o as h,s as a,v as l,g as _,u as s}from"../jse/index-index-Y3_OtjO-.js";import"./index-DGcxnQ4T.js";const C=p({__name:"general",setup(S){const t=d(),[n,i]=r({commonConfig:{},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{c(e).then(()=>{t.success("设置已保存")}).catch(o=>{console.log(`保存失败: ${o.message}`)})},resetButtonOptions:{show:!1},schema:[{component:"RadioGroup",fieldName:"finishSound",label:"完成提示音",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"disabled",label:"关闭"},{value:"dingdong",label:"叮咚"},{value:"bubble",label:"冒泡"}]}},{component:"RadioGroup",fieldName:"filmSound",label:"快门提示音",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"disabled",label:"关闭"},{value:"sound1",label:"音效一"},{value:"sound2",label:"音效二"}]}}]});return g(()=>{f().then(e=>{i.setValues({finishSound:e.finishSound,filmSound:e.filmSound})}).catch(e=>{console.log(`获取设置失败: ${e.message}`)})}),(e,o)=>(h(),b(s(u),{description:"本页面包含系统基本设定",title:"通用"},{default:a(()=>[l(s(m),{title:"通知"},{default:a(()=>[l(s(n))]),_:1}),o[0]||(o[0]=_("br",null,null,-1))]),_:1}))}});export{C as default};
