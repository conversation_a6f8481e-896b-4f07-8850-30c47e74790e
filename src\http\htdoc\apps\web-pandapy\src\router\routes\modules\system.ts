import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.system.title'),
    },
    name: 'System',
    path: '/system',
    children: [
      {
        name: 'General',
        path: '/system/general-settings',
        component: () => import('#/views/system/general.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.system.generalSettings'),
          ignoreAccess: true,
        },
      },
      {
        name: 'Joystick',
        path: '/system/joystick-settings',
        component: () => import('#/views/system/joystick.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.system.joystickSettings'),
          ignoreAccess: true,
        },
      },
      {
        name: 'Gpio',
        path: '/system/gpio-settings',
        component: () => import('#/views/system/gpio.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.system.gpioSettings'),
          ignoreAccess: true,
        },
      },
      {
        name: 'Machine',
        path: '/system/machine-settings',
        component: () => import('#/views/system/machine.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.system.machineSettings'),
          ignoreAccess: true,
        },
      }
    ],
  },
];

export default routes;
