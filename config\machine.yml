serial:
  device: /dev/ttyACM0
  baudrate: 115200
  use_simulate: true

axis:
  absolute: true       # false: relative positioning
  home: [0, 0, 0]      # homming position

  x:
    g-name: "X"        # gcode driver name
    driver:
      name: "tmc2209"  # driver name
      current: 2000    # stepper current
      microsteps: 8    # microstep
      ratio:      16   # 1mm to step
      feed:       100  # stepper feedrate
  
  y:
    g-name: "Y"
    driver:
      name: "tmc2209"  # driver name
      current: 2000    # stepper current
      microsteps: 8    # microstep
      ratio:      16   # 1mm to step
      feed:       100  # stepper feedrate

  z:
    g-name: "Z"
    driver:
      name: "tmc2209"  # driver name
      current: 2000    # stepper current
      microsteps: 256  # microstep
      ratio:      16   # 1mm to step
      feed:       100  # stepper feedrate

gpio:
  cam_trigger: 17

gcode:
  init:
    - G21
    - ${machine.axis.absolute?G90:G91}
    - M154 S1   # enable report
    - M92
    - M569 S1 X # StealthChop mode
    - M569 S1 Y # StealthChop mode
    - M569 S1 Z # StealthChop mode
    - M569 S1 E # StealthChop mode
    - M906 X${machine.axis.x.driver.current} # x current
    - M906 Y${machine.axis.y.driver.current} # y current
    - M906 Z${machine.axis.z.driver.current} # z current
    - M92 X0 Y0 Z0 # software home position
