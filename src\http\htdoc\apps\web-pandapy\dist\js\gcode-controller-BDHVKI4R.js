/* empty css                                                                */import e from"./gcode-Bu1x37e1.js";import{d as t,A as r,j as n,o as a,s,v as c,u as p}from"../jse/index-index-Y3_OtjO-.js";/* empty css                                                              */import"./bootstrap-MyT3sENS.js";const C=t({__name:"gcode-controller",setup(m){return(_,l)=>{const o=r("root");return a(),n(o,null,{default:s(()=>[c(p(e))]),_:1})}}});export{C as default};
