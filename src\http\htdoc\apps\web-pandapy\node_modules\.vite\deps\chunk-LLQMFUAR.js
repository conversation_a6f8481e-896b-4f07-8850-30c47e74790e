import {
  dark_default,
  light_default
} from "./chunk-2AZKBT6I.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/divider/styles/light.mjs
function self(vars) {
  const {
    textColor1,
    dividerColor,
    fontWeightStrong
  } = vars;
  return {
    textColor: textColor1,
    color: dividerColor,
    fontWeight: fontWeightStrong
  };
}
var dividerLight = {
  name: "Divider",
  common: light_default,
  self
};
var light_default2 = dividerLight;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/divider/styles/dark.mjs
var dividerDark = {
  name: "Divider",
  common: dark_default,
  self
};
var dark_default2 = dividerDark;

export {
  light_default2 as light_default,
  dark_default2 as dark_default
};
//# sourceMappingURL=chunk-LLQMFUAR.js.map
