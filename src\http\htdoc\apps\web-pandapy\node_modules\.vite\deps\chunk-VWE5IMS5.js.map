{"version": 3, "sources": ["../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/src/utils.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/src/Space.mjs"], "sourcesContent": ["import { isBrowser } from \"../../_utils/index.mjs\";\nlet supportFlexGap;\nexport function ensureSupportFlexGap() {\n  if (!isBrowser) return true;\n  if (supportFlexGap === undefined) {\n    // create flex container with row-gap set\n    const flex = document.createElement('div');\n    flex.style.display = 'flex';\n    flex.style.flexDirection = 'column';\n    flex.style.rowGap = '1px';\n    // create two, elements inside it\n    flex.appendChild(document.createElement('div'));\n    flex.appendChild(document.createElement('div'));\n    // append to the DOM (needed to obtain scrollHeight)\n    document.body.appendChild(flex);\n    const isSupported = flex.scrollHeight === 1; // flex container should be 1px high from the row-gap\n    document.body.removeChild(flex);\n    return supportFlexGap = isSupported;\n  }\n  return supportFlexGap;\n}", "import { depx, getGap } from 'seemly';\nimport { Comment, computed, defineComponent, h } from 'vue';\nimport { useConfig, useTheme } from \"../../_mixins/index.mjs\";\nimport { useRtl } from \"../../_mixins/use-rtl.mjs\";\nimport { createKey, flatten, getSlot } from \"../../_utils/index.mjs\";\nimport { spaceLight } from \"../styles/index.mjs\";\nimport { ensureSupportFlexGap } from \"./utils.mjs\";\nexport const spaceProps = Object.assign(Object.assign({}, useTheme.props), {\n  align: String,\n  justify: {\n    type: String,\n    default: 'start'\n  },\n  inline: Boolean,\n  vertical: Boolean,\n  reverse: Boolean,\n  size: {\n    type: [String, Number, Array],\n    default: 'medium'\n  },\n  wrapItem: {\n    type: Boolean,\n    default: true\n  },\n  itemClass: String,\n  itemStyle: [String, Object],\n  wrap: {\n    type: Boolean,\n    default: true\n  },\n  // internal\n  internalUseGap: {\n    type: Boolean,\n    default: undefined\n  }\n});\nexport default defineComponent({\n  name: 'Space',\n  props: spaceProps,\n  setup(props) {\n    const {\n      mergedClsPrefixRef,\n      mergedRtlRef\n    } = useConfig(props);\n    const themeRef = useTheme('Space', '-space', undefined, spaceLight, props, mergedClsPrefixRef);\n    const rtlEnabledRef = useRtl('Space', mergedRtlRef, mergedClsPrefixRef);\n    return {\n      useGap: ensureSupportFlexGap(),\n      rtlEnabled: rtlEnabledRef,\n      mergedClsPrefix: mergedClsPrefixRef,\n      margin: computed(() => {\n        const {\n          size\n        } = props;\n        if (Array.isArray(size)) {\n          return {\n            horizontal: size[0],\n            vertical: size[1]\n          };\n        }\n        if (typeof size === 'number') {\n          return {\n            horizontal: size,\n            vertical: size\n          };\n        }\n        const {\n          self: {\n            [createKey('gap', size)]: gap\n          }\n        } = themeRef.value;\n        const {\n          row,\n          col\n        } = getGap(gap);\n        return {\n          horizontal: depx(col),\n          vertical: depx(row)\n        };\n      })\n    };\n  },\n  render() {\n    const {\n      vertical,\n      reverse,\n      align,\n      inline,\n      justify,\n      itemClass,\n      itemStyle,\n      margin,\n      wrap,\n      mergedClsPrefix,\n      rtlEnabled,\n      useGap,\n      wrapItem,\n      internalUseGap\n    } = this;\n    const children = flatten(getSlot(this), false);\n    if (!children.length) return null;\n    const horizontalMargin = `${margin.horizontal}px`;\n    const semiHorizontalMargin = `${margin.horizontal / 2}px`;\n    const verticalMargin = `${margin.vertical}px`;\n    const semiVerticalMargin = `${margin.vertical / 2}px`;\n    const lastIndex = children.length - 1;\n    const isJustifySpace = justify.startsWith('space-');\n    return h(\"div\", {\n      role: \"none\",\n      class: [`${mergedClsPrefix}-space`, rtlEnabled && `${mergedClsPrefix}-space--rtl`],\n      style: {\n        display: inline ? 'inline-flex' : 'flex',\n        flexDirection: (() => {\n          if (vertical && !reverse) return 'column';\n          if (vertical && reverse) return 'column-reverse';\n          if (!vertical && reverse) return 'row-reverse';\n          /** (!vertical && !reverse) */else return 'row';\n        })(),\n        justifyContent: ['start', 'end'].includes(justify) ? `flex-${justify}` : justify,\n        flexWrap: !wrap || vertical ? 'nowrap' : 'wrap',\n        marginTop: useGap || vertical ? '' : `-${semiVerticalMargin}`,\n        marginBottom: useGap || vertical ? '' : `-${semiVerticalMargin}`,\n        alignItems: align,\n        gap: useGap ? `${margin.vertical}px ${margin.horizontal}px` : ''\n      }\n    }, !wrapItem && (useGap || internalUseGap) ? children : children.map((child, index) => child.type === Comment ? child : h(\"div\", {\n      role: \"none\",\n      class: itemClass,\n      style: [itemStyle, {\n        maxWidth: '100%'\n      }, useGap ? '' : vertical ? {\n        marginBottom: index !== lastIndex ? verticalMargin : ''\n      } : rtlEnabled ? {\n        marginLeft: isJustifySpace ? justify === 'space-between' && index === lastIndex ? '' : semiHorizontalMargin : index !== lastIndex ? horizontalMargin : '',\n        marginRight: isJustifySpace ? justify === 'space-between' && index === 0 ? '' : semiHorizontalMargin : '',\n        paddingTop: semiVerticalMargin,\n        paddingBottom: semiVerticalMargin\n      } : {\n        marginRight: isJustifySpace ? justify === 'space-between' && index === lastIndex ? '' : semiHorizontalMargin : index !== lastIndex ? horizontalMargin : '',\n        marginLeft: isJustifySpace ? justify === 'space-between' && index === 0 ? '' : semiHorizontalMargin : '',\n        paddingTop: semiVerticalMargin,\n        paddingBottom: semiVerticalMargin\n      }]\n    }, child)));\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,IAAI;AACG,SAAS,uBAAuB;AACrC,MAAI,CAAC,UAAW,QAAO;AACvB,MAAI,mBAAmB,QAAW;AAEhC,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,MAAM,UAAU;AACrB,SAAK,MAAM,gBAAgB;AAC3B,SAAK,MAAM,SAAS;AAEpB,SAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AAC9C,SAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AAE9C,aAAS,KAAK,YAAY,IAAI;AAC9B,UAAM,cAAc,KAAK,iBAAiB;AAC1C,aAAS,KAAK,YAAY,IAAI;AAC9B,WAAO,iBAAiB;AAAA,EAC1B;AACA,SAAO;AACT;;;ACbO,IAAM,aAAa,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAS,KAAK,GAAG;AAAA,EACzE,OAAO;AAAA,EACP,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,IACJ,MAAM,CAAC,QAAQ,QAAQ,KAAK;AAAA,IAC5B,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,EACX,WAAW,CAAC,QAAQ,MAAM;AAAA,EAC1B,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF,CAAC;AACD,IAAO,gBAAQ,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,UAAU,KAAK;AACnB,UAAM,WAAW,kBAAS,SAAS,UAAU,QAAW,eAAY,OAAO,kBAAkB;AAC7F,UAAM,gBAAgB,OAAO,SAAS,cAAc,kBAAkB;AACtE,WAAO;AAAA,MACL,QAAQ,qBAAqB;AAAA,MAC7B,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,QAAQ,SAAS,MAAM;AACrB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,iBAAO;AAAA,YACL,YAAY,KAAK,CAAC;AAAA,YAClB,UAAU,KAAK,CAAC;AAAA,UAClB;AAAA,QACF;AACA,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,UACZ;AAAA,QACF;AACA,cAAM;AAAA,UACJ,MAAM;AAAA,YACJ,CAAC,UAAU,OAAO,IAAI,CAAC,GAAG;AAAA,UAC5B;AAAA,QACF,IAAI,SAAS;AACb,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,OAAO,GAAG;AACd,eAAO;AAAA,UACL,YAAY,KAAK,GAAG;AAAA,UACpB,UAAU,KAAK,GAAG;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC7C,QAAI,CAAC,SAAS,OAAQ,QAAO;AAC7B,UAAM,mBAAmB,GAAG,OAAO,UAAU;AAC7C,UAAM,uBAAuB,GAAG,OAAO,aAAa,CAAC;AACrD,UAAM,iBAAiB,GAAG,OAAO,QAAQ;AACzC,UAAM,qBAAqB,GAAG,OAAO,WAAW,CAAC;AACjD,UAAM,YAAY,SAAS,SAAS;AACpC,UAAM,iBAAiB,QAAQ,WAAW,QAAQ;AAClD,WAAO,EAAE,OAAO;AAAA,MACd,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,eAAe,UAAU,cAAc,GAAG,eAAe,aAAa;AAAA,MACjF,OAAO;AAAA,QACL,SAAS,SAAS,gBAAgB;AAAA,QAClC,gBAAgB,MAAM;AACpB,cAAI,YAAY,CAAC,QAAS,QAAO;AACjC,cAAI,YAAY,QAAS,QAAO;AAChC,cAAI,CAAC,YAAY,QAAS,QAAO;AAAA,cACE,QAAO;AAAA,QAC5C,GAAG;AAAA,QACH,gBAAgB,CAAC,SAAS,KAAK,EAAE,SAAS,OAAO,IAAI,QAAQ,OAAO,KAAK;AAAA,QACzE,UAAU,CAAC,QAAQ,WAAW,WAAW;AAAA,QACzC,WAAW,UAAU,WAAW,KAAK,IAAI,kBAAkB;AAAA,QAC3D,cAAc,UAAU,WAAW,KAAK,IAAI,kBAAkB;AAAA,QAC9D,YAAY;AAAA,QACZ,KAAK,SAAS,GAAG,OAAO,QAAQ,MAAM,OAAO,UAAU,OAAO;AAAA,MAChE;AAAA,IACF,GAAG,CAAC,aAAa,UAAU,kBAAkB,WAAW,SAAS,IAAI,CAAC,OAAO,UAAU,MAAM,SAAS,UAAU,QAAQ,EAAE,OAAO;AAAA,MAC/H,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO,CAAC,WAAW;AAAA,QACjB,UAAU;AAAA,MACZ,GAAG,SAAS,KAAK,WAAW;AAAA,QAC1B,cAAc,UAAU,YAAY,iBAAiB;AAAA,MACvD,IAAI,aAAa;AAAA,QACf,YAAY,iBAAiB,YAAY,mBAAmB,UAAU,YAAY,KAAK,uBAAuB,UAAU,YAAY,mBAAmB;AAAA,QACvJ,aAAa,iBAAiB,YAAY,mBAAmB,UAAU,IAAI,KAAK,uBAAuB;AAAA,QACvG,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB,IAAI;AAAA,QACF,aAAa,iBAAiB,YAAY,mBAAmB,UAAU,YAAY,KAAK,uBAAuB,UAAU,YAAY,mBAAmB;AAAA,QACxJ,YAAY,iBAAiB,YAAY,mBAAmB,UAAU,IAAI,KAAK,uBAAuB;AAAA,QACtG,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB,CAAC;AAAA,IACH,GAAG,KAAK,CAAC,CAAC;AAAA,EACZ;AACF,CAAC;", "names": []}