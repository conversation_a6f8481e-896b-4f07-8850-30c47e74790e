from abc import ABC, abstractmethod
from enum import Enum
from src.log import logger

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.applet import applet

class code(Enum):
    SUCCESS  = 0
    FAILURE  = 1
    DEFERRED = 2

class service():

    context: "applet"

    def __init__(self, applet):
        self.context = applet
        pass
    
    @abstractmethod
    def init(self):
        return code.SUCCESS

    @abstractmethod
    def start(self):
        return code.SUCCESS

    @abstractmethod
    def stop(self):
        return code.SUCCESS

    @abstractmethod
    def get_name(self):
        return "<anonymous service>"
    
    @abstractmethod
    def get_status(self):
        return code.FAILURE
    
    def get_service(self, svtype):
        return self.context.get_service(svtype)

    def get_config(self):
        return self.context.get_config()

    def __log(self, name, level, msg):
        from src.logger.service import logservice
        _logservice = self.context.get_service(logservice)

        if not _logservice:
            print(f"[INIT] [{self.get_name()}] {msg}")
            return
        
        _logservice.log_base(name, level, msg)

    def log_info(self, msg: str):
        self.__log(self.get_name(), logger.INFO, msg)
        pass

    def log_debug(self, msg: str):
        self.__log(self.get_name(), logger.DEBUG, msg)
        pass

    def log_warning(self, msg: str):
        self.__log(self.get_name(), logger.WARNING, msg)
        pass

    def log_fatal(self, msg: str):
        self.__log(self.get_name(), logger.FATAL, msg)
