import {
  select_default
} from "./chunk-XYVZ4HU5.js";
import "./chunk-QVKWU4R6.js";
import "./chunk-FWZ7IGE4.js";
import "./chunk-45Y6P64G.js";
import "./chunk-JL3MBCJ6.js";
import "./chunk-PLEPSOXJ.js";
import {
  dynamicApp
} from "./chunk-TLDIGKI7.js";
import {
  VxeUI
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js
var VxeSelect = Object.assign(select_default, {
  install: function(app) {
    app.component(select_default.name, select_default);
  }
});
dynamicApp.use(VxeSelect);
VxeUI.component(select_default);
var Select = VxeSelect;
var select_default2 = VxeSelect;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-select/index.js
var vxe_select_default = select_default2;
export {
  Select,
  VxeSelect,
  vxe_select_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-select_index__js.js.map
