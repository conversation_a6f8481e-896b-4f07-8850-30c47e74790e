import { requestClient } from '#/api/request';

export async function jobIsLoaded() {
  return requestClient.get('/job/manage/jobIsLoaded');
}

export async function getJobList() {
  return requestClient.get('/job/manage/getJobList');
}

export async function openJob(name) {
  return requestClient.get(`/job/manage/openJob?name=${name}`);
}

export async function closeJob() {
  return requestClient.get('/job/manage/closeJob');
}

export async function saveJob() {
  return requestClient.get('/job/manage/saveJob');
}

export async function getCurrentJob() {
  return requestClient.get('/job/manage/getCurrentJob');
}

export async function createNewJob(name) {
  return requestClient.get(`/job/manage/createNewJob?name=${name}`);
}

export async function setJobPrefrences(category, body) {
  return requestClient.post(`/job/manage/setJobPrefrences?category=${category}`, body);
}

export async function getJobPrefrences(category) {
  return requestClient.get(`/job/manage/getJobPrefrences?category=${category}`);
}

export async function getJobStatus() {
  return requestClient.get(`/job/manage/getJobStatus`);
}

export async function jobControl(action) {
  return requestClient.get(`/job/manage/jobControl?action=${action}`);
}

export async function jobMachineControl(action) {
  return requestClient.get(`/job/manage/jobMachineControl?action=${action}`);
}
