import os
import time
from src.service import code
from src.service import service
from src.utils import utils
from src.job.runner import runner
from src.config import config, default_job

class manager(service):

    _job_dir: str = None
    _runner: runner = None

    def __init__(self, context):
        super().__init__(context)

        from src.controller.service import controller
        if not self.context.get_service(controller):
            return code.DEFERRED

        self._workdir = utils.get_workdir()
        self._job_dir = f"{self._workdir}/jobs"
        self._runner = runner(self, self._job_dir)
        if not os.path.exists(self._job_dir):
            os.makedirs(self._job_dir)

    def init(self):
        return code.SUCCESS

    def start(self):
        return code.SUCCESS

    def stop(self):
        return code.SUCCESS
    
    def get_name(self):
        return "job-manager"

    def get_job_list(self):
        try:
            # return [{name, date}]
            if not os.path.exists(self._job_dir):
                self.log_info("Job directory does not exist, returning empty job list.")
                return []
            self.log_info("Fetching job list.")

            # List all directories in the job directory
            # Each directory represents a job
            job_list = []
            for job_name in os.listdir(self._job_dir):
                job_path = os.path.join(self._job_dir, job_name)
                if os.path.isdir(job_path):
                    # Get the creation time of the job directory
                    creation_time = os.path.getctime(job_path)
                    job_list.append({
                        "name": job_name,
                        "date": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(creation_time))
                    })
            self.log_info(f"Found {len(job_list)} jobs.")
            return job_list

        except Exception as e:
            self.log_fatal(f"Failed to get job list: {e}")
            return []
        
    def create_new_job(self, name):
        """
        Create a new job with the given name.
        :param name: Name of the job to create.
        :return: Path to the created job directory.
        """
        if not name:
            self.log_fatal("Job name cannot be empty.")
            return None
        
        job_path = os.path.join(self._job_dir, name)
        if os.path.exists(job_path):
            self.log_fatal(f"Job '{name}' already exists.")
            return None
        
        try:
            os.makedirs(job_path)
            self.log_info(f"Created new job: {name}")

            # make a system config copy into job directory
            default_job().dump(job_path, item={"job"})
            # self.context.get_config().dump(job_path)

            return True
        except Exception as e:
            self.log_fatal(f"Failed to create job '{name}': {e}")
            return None

    def delete_job(self, name):
        """
        Delete a job with the given name.
        :param name: Name of the job to delete.
        :return: True if the job was deleted, False otherwise.
        """
        job_path = os.path.join(self._job_dir, name)
        if not os.path.exists(job_path):
            self.log_fatal(f"Job '{name}' does not exist.")
            return False
        
        try:
            os.rmdir(job_path)
            self.log_info(f"Deleted job: {name}")
            return True
        except Exception as e:
            self.log_fatal(f"Failed to delete job '{name}': {e}")
            return False

    def open_job(self, name):
        if not name:
            self.log_fatal("Job name cannot be empty.")
            return False
        if self._runner.is_running():
            self.log_fatal("A job is already running. Please close it before opening a new one.")
            return False

        job_path = os.path.join(self._job_dir, name)
        if not os.path.exists(job_path):
            self.log_fatal(f"Job '{name}' does not exist.")
            return False
        
        self.log_info(f"Opened job: {name}")
        self._runner.reset()
        self._runner.set_job_name(name)
        self._runner.load_config(self.context.get_config())
        return True

    def close_job(self):
        if self._runner.is_running():
            self.log_fatal("Please stop the job first.")
            return False
        
        self._runner.reset()
        return True

    def save_job(self):
        if not self._runner.get_job_name():
            self.log_fatal("No job is currently loaded.")
            return False

        self._runner.save_config()
        self.log_info(f"Saved job: {self._runner.get_job_name()}")
        return True

    def get_job_runner(self):
        return self._runner
