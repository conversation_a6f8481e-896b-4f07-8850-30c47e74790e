from src.utils import utils

class cfggen:
    
    def generate(config, workdir):
        # read nginx config
        _workdir = utils.get_srcdir()
        _nginx_conf = f"{_workdir}/http/config/nginx-template.conf"
        with open(_nginx_conf, "r") as f:
            _nginx_conf = f.read()

        _htdoc = f"{_workdir}/http/htdoc/publish"
        _nginx_port = str(config.server["nginx"]["port"])
        _, _backend_port = config.server["http"]["listen"].split(":")

        # generate nginx config
        _nginx_conf = _nginx_conf.replace("$NGINX_PORT", _nginx_port)
        _nginx_conf = _nginx_conf.replace("$BACKEND_PORT", _backend_port)
        _nginx_conf = _nginx_conf.replace("$BACKEND_WWW_STATIC", _htdoc)
        _nginx_conf = _nginx_conf.replace("$NGINX_WORKDIR", workdir)

        return _nginx_conf
