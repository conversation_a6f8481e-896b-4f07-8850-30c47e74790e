{"version": 3, "sources": ["../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/divider/styles/light.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/divider/styles/dark.mjs"], "sourcesContent": ["import { commonLight } from \"../../_styles/common/index.mjs\";\nexport function self(vars) {\n  const {\n    textColor1,\n    dividerColor,\n    fontWeightStrong\n  } = vars;\n  return {\n    textColor: textColor1,\n    color: dividerColor,\n    fontWeight: fontWeightStrong\n  };\n}\nconst dividerLight = {\n  name: 'Divider',\n  common: commonLight,\n  self\n};\nexport default dividerLight;", "import { commonDark } from \"../../_styles/common/index.mjs\";\nimport { self } from \"./light.mjs\";\nconst dividerDark = {\n  name: 'Divider',\n  common: commonDark,\n  self\n};\nexport default dividerDark;"], "mappings": ";;;;;;AACO,SAAS,KAAK,MAAM;AACzB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AACA,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR;AACF;AACA,IAAOA,iBAAQ;;;AChBf,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR;AACF;AACA,IAAOC,gBAAQ;", "names": ["light_default", "dark_default"]}