import{E as Z,H as n,I as F,_ as h}from"./bootstrap-MyT3sENS.js";import{_ as ee}from"./jobcube.vue_vue_type_script_setup_true_lang-BePFcVJY.js";import oe from"./statbar-DnChwtfo.js";/* empty css                                                              */import{g as se,a as te,j as g,b as le,o as I,c as ae,s as ne,d as _,e as re}from"./job-CyTXo-tT.js";import{u as ue,_ as V}from"./form-1Qpdj_eE.js";import{N as ie}from"./Progress-BJgs0fD_.js";import{d as de,r as d,y as pe,G as me,A as w,j as y,o as v,s as t,z as A,v as a,g as r,u as s,l as k,q as j,x as u,e as z,F as ve,B as fe}from"../jse/index-index-Y3_OtjO-.js";import{u as L}from"./use-modal-O6aMVd6f.js";/* empty css                                                                */import"./index-DGcxnQ4T.js";import"./format-length-B-p6aW7q.js";const be={class:"container"},ce={class:"job-cube"},ge={class:"job-information"},ye={class:"buttons"},Ce={class:"job-select-list"},ke=["onClick"],je=de({__name:"job",setup(_e){const f=Z(),b=d(""),p=d(!1);se().then(o=>{if(o==null){p.value=!1,b.value="";return}p.value=!0,b.value=o});const[P,x]=L(),J=d([]),R=()=>{J.value=[],le().then(o=>{o.forEach(e=>J.value.push(e)),x.open()}).catch(o=>{console.error("获取工程列表失败:",o)})},T=o=>{console.log("选择的工程:",o.name),I(o.name).then(e=>{if(!e){f.error("打开工程失败");return}b.value=o.name,x.close(),p.value=!0,f.success(`工程 '${o.name}' 已加载`)}).catch(e=>{console.error("打开工程失败:",e)})},E=()=>{ae().then(()=>{b.value="",p.value=!1,f.success("工程文件已关闭")}).catch(o=>{console.error("关闭工程失败:",o)})},q=()=>{ne().then(()=>{f.success("工程文件已保存")}).catch(o=>{console.error(o.message)})},[D,N]=L(),[H,O]=ue({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"md:grid-cols-1 lg:grid-cols-1",handleSubmit:o=>{re(o.jobName).then(e=>{if(!e){f.error("创建工程失败 -1");return}I(o.jobName).then(B=>{if(!B){f.error("创建工程失败 -2");return}b.value=o.jobName,p.value=!0,N.close(),f.success(`工程 '${o.jobName}' 已创建`)})}).catch(e=>{console.error("创建工程失败:",e)})},resetButtonOptions:{show:!1},submitButtonOptions:{show:!1},schema:[{component:"Input",fieldName:"jobName",label:"工程名",rules:"required",componentProps:{showButtons:!1,placeholder:"未命名工程"}}]});N.onConfirm=()=>{O.validateAndSubmitForm()};const c=d("加载数据..."),C=d(0),$=d("加载数据..."),l=d(!1),m=d(!1),G=()=>{_("start")},U=()=>{_("pause")},K=()=>{_("resume")},Q=()=>{_("stop")};let S;pe(()=>{S=setInterval(()=>{p.value&&W()},500)}),me(()=>{clearInterval(S)});const M=d("加载数据..."),W=()=>{te().then(o=>{console.log("获取任务状态:",o),M.value=o.message||"",o.status==="running"?(l.value=!0,m.value=!1,c.value=`正在扫描 ${o.progress}%`,C.value=o.progress):o.status==="paused"?(l.value=!1,m.value=!0,c.value=`已暂停 ${o.progress}%`,C.value=o.progress):o.status==="planning"?(l.value=!0,m.value=!1,c.value="规划路径中",C.value=o.progress):(l.value=!1,m.value=!1,c.value="等待中",C.value=0)}).catch(o=>{console.error("获取任务状态失败:",o)}),l.value?$.value="任务运行中不可操作":$.value="已解锁控制按钮，请谨慎操作"};return(o,e)=>{const B=w("jobpage"),X=w("addjob"),Y=w("root");return v(),y(Y,null,{default:t(()=>[A(a(B,null,{default:t(()=>[r("div",be,[r("div",ce,[a(s(ee),{bkgColor:"#14161a",animate:l.value,zoom:l.value?4.5:5.5},null,8,["animate","zoom"])]),r("div",ge,[r("h1",null,j(c.value),1),l.value?(v(),y(s(ie),{key:0,style:{"margin-bottom":"24px"},showIndicator:!1,percentage:C.value},null,8,["percentage"])):k("",!0),r("div",ye,[!l.value&&!m.value?(v(),y(s(n),{key:0,strong:"",round:"",type:"primary",onClick:G},{default:t(()=>e[7]||(e[7]=[u("一键开始")])),_:1})):k("",!0),l.value&&!m.value?(v(),y(s(n),{key:1,strong:"",round:"",type:"primary",onClick:U},{default:t(()=>e[8]||(e[8]=[u("暂停执行")])),_:1})):k("",!0),m.value?(v(),y(s(n),{key:2,strong:"",round:"",type:"primary",onClick:K},{default:t(()=>e[9]||(e[9]=[u("恢复执行")])),_:1})):k("",!0),l.value||m.value?(v(),y(s(n),{key:3,strong:"",round:"",type:"error",onClick:Q},{default:t(()=>e[10]||(e[10]=[u("停止执行")])),_:1})):k("",!0)])])]),a(s(oe),{message:M.value},null,8,["message"]),a(s(V),{id:"page-control",contentClass:"p-6",title:"控制选项",description:$.value},{default:t(()=>[a(s(n),{disabled:l.value,onClick:e[0]||(e[0]=i=>s(g)("homming")),type:"primary"},{default:t(()=>e[11]||(e[11]=[u("回到零位置")])),_:1},8,["disabled"]),a(s(n),{disabled:l.value,onClick:e[1]||(e[1]=i=>s(g)("go_scan_start")),type:"primary"},{default:t(()=>e[12]||(e[12]=[u("运行到扫描起点")])),_:1},8,["disabled"]),a(s(n),{disabled:l.value,onClick:e[2]||(e[2]=i=>s(g)("go_scan_end")),type:"primary"},{default:t(()=>e[13]||(e[13]=[u("运行到扫描终点")])),_:1},8,["disabled"]),a(s(n),{disabled:l.value,onClick:e[3]||(e[3]=i=>s(g)("set_scan_start")),type:"primary"},{default:t(()=>e[14]||(e[14]=[u("设置当前位置为扫描起点")])),_:1},8,["disabled"]),a(s(n),{disabled:l.value,onClick:e[4]||(e[4]=i=>s(g)("set_scan_end")),type:"primary"},{default:t(()=>e[15]||(e[15]=[u("设置当前位置为扫描终点")])),_:1},8,["disabled"]),a(s(n),{disabled:l.value,onClick:e[5]||(e[5]=i=>s(g)("swap_scan")),type:"info"},{default:t(()=>e[16]||(e[16]=[u("交换起点和终点")])),_:1},8,["disabled"])]),_:1},8,["description"]),a(s(V),{id:"page-info",contentClass:"p-0",class:"job-page"},{default:t(()=>[r("p",null,"任务名称："+j(b.value),1),r("p",null,"执行状态："+j(c.value),1),a(s(n),{disabled:l.value,type:"primary",onClick:q},{default:t(()=>e[17]||(e[17]=[u("保存")])),_:1},8,["disabled"]),a(s(n),{disabled:l.value,type:"error",onClick:E},{default:t(()=>e[18]||(e[18]=[u("关闭工程文件")])),_:1},8,["disabled"])]),_:1})]),_:1},512),[[F,p.value]]),A(a(X,null,{default:t(()=>[e[21]||(e[21]=r("h1",null,"工程尚未加载",-1)),e[22]||(e[22]=r("div",{class:"divider"},null,-1)),e[23]||(e[23]=r("span",null,"你可以",-1)),a(s(n),{type:"primary",onClick:R},{default:t(()=>e[19]||(e[19]=[u("打开现有工程文件")])),_:1}),e[24]||(e[24]=r("span",null,"或者",-1)),a(s(n),{type:"primary",onClick:e[6]||(e[6]=i=>s(N).open())},{default:t(()=>e[20]||(e[20]=[u("新建一个工程")])),_:1}),a(s(P),{title:"最近打开的工程","close-on-press-escape":!1,"fullscreen-button":!1,overlayBlur:10,footer:!1,draggable:!1},{default:t(()=>[r("ul",Ce,[(v(!0),z(ve,null,fe(J.value,i=>(v(),z("li",{onClick:Je=>T(i)},[r("span",null,j(i.name),1),r("span",null,j(i.date),1)],8,ke))),256))])]),_:1}),a(s(D),{title:"新建工程","close-on-press-escape":!1,"fullscreen-button":!1,overlayBlur:10,confirmText:"提交",draggable:!1},{default:t(()=>[a(s(H))]),_:1})]),_:1},512),[[F,!p.value]])]),_:1})}}}),Le=h(je,[["__scopeId","data-v-9c9a6fe1"]]);export{Le as default};
