var x=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable;var h=(a,e)=>{var t={};for(var s in a)C.call(a,s)&&e.indexOf(s)<0&&(t[s]=a[s]);if(a!=null&&x)for(var s of x(a))e.indexOf(s)<0&&j.call(a,s)&&(t[s]=a[s]);return t};import{a as b,ar as B,as as S,at as V,au as D,av as O,aw as w,ax as L,ay as N,az as q,aA as G,aB as I}from"./bootstrap-MyT3sENS.js";import{d as c,j as m,o as i,u as o,a3 as P,a4 as $,s as p,n as f,c as g,v as T,L as y,a5 as _,I as A,i as F,e as v,l as z,x as E,q as k}from"../jse/index-index-Y3_OtjO-.js";const X=b("moon-star",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9",key:"4ay0iu"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}]]);const Y=b("sun-moon",[["path",{d:"M12 8a2.83 2.83 0 0 0 4 4 4 4 0 1 1-4-4",key:"1fu5g2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.9 4.9 1.4 1.4",key:"b9915j"}],["path",{d:"m17.7 17.7 1.4 1.4",key:"qc3ed3"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.3 17.7-1.4 1.4",key:"5gca6"}],["path",{d:"m19.1 4.9-1.4 1.4",key:"wpu9u6"}]]);const Z=b("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),ee=c({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(a,{emit:e}){const n=B(a,e);return(r,d)=>(i(),m(o(S),P($(o(n))),{default:p(()=>[f(r.$slots,"default")]),_:3},16))}}),ae=c({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(a,{emit:e}){const t=a,s=e,n=g(()=>{const l=t,{class:d}=l;return h(l,["class"])}),r=B(n,s);return(d,u)=>(i(),m(o(V),null,{default:p(()=>[T(o(D),y(o(r),{class:o(_)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:p(()=>[f(d.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),te=c({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(a){const e=a;return(t,s)=>(i(),m(o(O),P($(e)),{default:p(()=>[f(t.$slots,"default")]),_:3},16))}}),se=c({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(a){const e=a,t=g(()=>{const d=e,{class:n}=d;return h(d,["class"])}),s=w(t);return(n,r)=>(i(),m(o(L),y(o(s),{class:o(_)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n.inset&&"pl-8",e.class)}),{default:p(()=>[f(n.$slots,"default")]),_:3},16,["class"]))}}),oe=c({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(a){const t=w(a);return(s,n)=>(i(),m(o(N),y({class:"outline-none"},o(t)),{default:p(()=>[f(s.$slots,"default")]),_:3},16))}}),K=q("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{defaultVariants:{size:"default",variant:"default"},variants:{size:{default:"h-9 px-3",lg:"h-10 px-3",sm:"h-8 px-2"},variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground"}}}),ne=c({__name:"ToggleGroup",props:{rovingFocus:{type:Boolean},disabled:{type:Boolean},orientation:{},dir:{},loop:{type:Boolean},asChild:{type:Boolean},as:{},type:{},modelValue:{},defaultValue:{},class:{},size:{},variant:{}},emits:["update:modelValue"],setup(a,{emit:e}){const t=a,s=e;A("toggleGroup",{size:t.size,variant:t.variant});const n=g(()=>{const l=t,{class:d}=l;return h(l,["class"])}),r=B(n,s);return(d,u)=>(i(),m(o(G),y(o(r),{class:o(_)("flex items-center justify-center gap-1",t.class)}),{default:p(()=>[f(d.$slots,"default")]),_:3},16,["class"]))}}),re=c({__name:"ToggleGroupItem",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{},size:{},variant:{}},setup(a){const e=a,t=F("toggleGroup"),s=g(()=>{const M=e,{class:r,size:d,variant:u}=M;return h(M,["class","size","variant"])}),n=w(s);return(r,d)=>{var u,l;return i(),m(o(I),y(o(n),{class:o(_)(o(K)({variant:((u=o(t))==null?void 0:u.variant)||r.variant,size:((l=o(t))==null?void 0:l.size)||r.size}),e.class)}),{default:p(()=>[f(r.$slots,"default")]),_:3},16,["class"])}}}),R={class:"text-md flex-center"},W=["href"],H=["href"],de=c({name:"Copyright",__name:"copyright",props:{companyName:{default:"Vben Admin"},companySiteLink:{default:""},date:{default:"2024"},icp:{default:""},icpLink:{default:""}},setup(a){return(e,t)=>(i(),v("div",R,[e.icp?(i(),v("a",{key:0,href:e.icpLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},k(e.icp),9,W)):z("",!0),E(" Copyright © "+k(e.date)+" ",1),e.companyName?(i(),v("a",{key:1,href:e.companySiteLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},k(e.companyName),9,H)):z("",!0)]))}});export{X as M,Z as S,ee as _,oe as a,ae as b,te as c,se as d,Y as e,ne as f,re as g,de as h};
