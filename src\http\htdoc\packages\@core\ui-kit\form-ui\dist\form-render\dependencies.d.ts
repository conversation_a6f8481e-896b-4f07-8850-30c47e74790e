import type { FormItemDependencies, FormSchemaRuleType, MaybeComponentProps } from '../types';
export default function useDependencies(getDependencies: () => FormItemDependencies | undefined): {
    dynamicComponentProps: import("vue").Ref<{
        [x: Record<never, never> & string]: any;
        class?: any;
        options?: any;
        placeholder?: any;
        title?: any;
        onCopy?: any;
        onCut?: any;
        onPaste?: any;
        onCompositionend?: any;
        onCompositionstart?: any;
        onCompositionupdate?: any;
        onDrag?: any;
        onDragend?: any;
        onDragenter?: any;
        onDragexit?: any;
        onDragleave?: any;
        onDragover?: any;
        onDragstart?: any;
        onDrop?: any;
        onFocus?: any;
        onFocusin?: any;
        onFocusout?: any;
        onBlur?: any;
        onChange?: any;
        onBeforeinput?: any;
        onInput?: any;
        onReset?: any;
        onSubmit?: any;
        onInvalid?: any;
        onLoad?: any;
        onError?: any;
        onKeydown?: any;
        onKeypress?: any;
        onKeyup?: any;
        onAuxclick?: any;
        onClick?: any;
        onContextmenu?: any;
        onDblclick?: any;
        onMousedown?: any;
        onMouseenter?: any;
        onMouseleave?: any;
        onMousemove?: any;
        onMouseout?: any;
        onMouseover?: any;
        onMouseup?: any;
        onAbort?: any;
        onCanplay?: any;
        onCanplaythrough?: any;
        onDurationchange?: any;
        onEmptied?: any;
        onEncrypted?: any;
        onEnded?: any;
        onLoadeddata?: any;
        onLoadedmetadata?: any;
        onLoadstart?: any;
        onPause?: any;
        onPlay?: any;
        onPlaying?: any;
        onProgress?: any;
        onRatechange?: any;
        onSeeked?: any;
        onSeeking?: any;
        onStalled?: any;
        onSuspend?: any;
        onTimeupdate?: any;
        onVolumechange?: any;
        onWaiting?: any;
        onSelect?: any;
        onScroll?: any;
        onScrollend?: any;
        onTouchcancel?: any;
        onTouchend?: any;
        onTouchmove?: any;
        onTouchstart?: any;
        onPointerdown?: any;
        onPointermove?: any;
        onPointerup?: any;
        onPointercancel?: any;
        onPointerenter?: any;
        onPointerleave?: any;
        onPointerover?: any;
        onPointerout?: any;
        onWheel?: any;
        onAnimationstart?: any;
        onAnimationend?: any;
        onAnimationiteration?: any;
        onTransitionend?: any;
        onTransitionstart?: any;
        manifest?: any;
        innerHTML?: any;
        style?: any;
        accesskey?: any;
        contenteditable?: any;
        contextmenu?: any;
        dir?: any;
        draggable?: any;
        hidden?: any;
        id?: any;
        inert?: any;
        lang?: any;
        spellcheck?: any;
        tabindex?: any;
        translate?: any;
        radiogroup?: any;
        role?: any;
        about?: any;
        datatype?: any;
        inlist?: any;
        prefix?: any;
        property?: any;
        resource?: any;
        typeof?: any;
        vocab?: any;
        autocapitalize?: any;
        autocorrect?: any;
        autosave?: any;
        color?: any;
        itemprop?: any;
        itemscope?: any;
        itemtype?: any;
        itemid?: any;
        itemref?: any;
        results?: any;
        security?: any;
        unselectable?: any;
        inputmode?: any;
        is?: any;
        "aria-activedescendant"?: any;
        "aria-atomic"?: any;
        "aria-autocomplete"?: any;
        "aria-busy"?: any;
        "aria-checked"?: any;
        "aria-colcount"?: any;
        "aria-colindex"?: any;
        "aria-colspan"?: any;
        "aria-controls"?: any;
        "aria-current"?: any;
        "aria-describedby"?: any;
        "aria-details"?: any;
        "aria-disabled"?: any;
        "aria-dropeffect"?: any;
        "aria-errormessage"?: any;
        "aria-expanded"?: any;
        "aria-flowto"?: any;
        "aria-grabbed"?: any;
        "aria-haspopup"?: any;
        "aria-hidden"?: any;
        "aria-invalid"?: any;
        "aria-keyshortcuts"?: any;
        "aria-label"?: any;
        "aria-labelledby"?: any;
        "aria-level"?: any;
        "aria-live"?: any;
        "aria-modal"?: any;
        "aria-multiline"?: any;
        "aria-multiselectable"?: any;
        "aria-orientation"?: any;
        "aria-owns"?: any;
        "aria-placeholder"?: any;
        "aria-posinset"?: any;
        "aria-pressed"?: any;
        "aria-readonly"?: any;
        "aria-relevant"?: any;
        "aria-required"?: any;
        "aria-roledescription"?: any;
        "aria-rowcount"?: any;
        "aria-rowindex"?: any;
        "aria-rowspan"?: any;
        "aria-selected"?: any;
        "aria-setsize"?: any;
        "aria-sort"?: any;
        "aria-valuemax"?: any;
        "aria-valuemin"?: any;
        "aria-valuenow"?: any;
        "aria-valuetext"?: any;
    }, MaybeComponentProps | {
        [x: Record<never, never> & string]: any;
        class?: any;
        options?: any;
        placeholder?: any;
        title?: any;
        onCopy?: any;
        onCut?: any;
        onPaste?: any;
        onCompositionend?: any;
        onCompositionstart?: any;
        onCompositionupdate?: any;
        onDrag?: any;
        onDragend?: any;
        onDragenter?: any;
        onDragexit?: any;
        onDragleave?: any;
        onDragover?: any;
        onDragstart?: any;
        onDrop?: any;
        onFocus?: any;
        onFocusin?: any;
        onFocusout?: any;
        onBlur?: any;
        onChange?: any;
        onBeforeinput?: any;
        onInput?: any;
        onReset?: any;
        onSubmit?: any;
        onInvalid?: any;
        onLoad?: any;
        onError?: any;
        onKeydown?: any;
        onKeypress?: any;
        onKeyup?: any;
        onAuxclick?: any;
        onClick?: any;
        onContextmenu?: any;
        onDblclick?: any;
        onMousedown?: any;
        onMouseenter?: any;
        onMouseleave?: any;
        onMousemove?: any;
        onMouseout?: any;
        onMouseover?: any;
        onMouseup?: any;
        onAbort?: any;
        onCanplay?: any;
        onCanplaythrough?: any;
        onDurationchange?: any;
        onEmptied?: any;
        onEncrypted?: any;
        onEnded?: any;
        onLoadeddata?: any;
        onLoadedmetadata?: any;
        onLoadstart?: any;
        onPause?: any;
        onPlay?: any;
        onPlaying?: any;
        onProgress?: any;
        onRatechange?: any;
        onSeeked?: any;
        onSeeking?: any;
        onStalled?: any;
        onSuspend?: any;
        onTimeupdate?: any;
        onVolumechange?: any;
        onWaiting?: any;
        onSelect?: any;
        onScroll?: any;
        onScrollend?: any;
        onTouchcancel?: any;
        onTouchend?: any;
        onTouchmove?: any;
        onTouchstart?: any;
        onPointerdown?: any;
        onPointermove?: any;
        onPointerup?: any;
        onPointercancel?: any;
        onPointerenter?: any;
        onPointerleave?: any;
        onPointerover?: any;
        onPointerout?: any;
        onWheel?: any;
        onAnimationstart?: any;
        onAnimationend?: any;
        onAnimationiteration?: any;
        onTransitionend?: any;
        onTransitionstart?: any;
        manifest?: any;
        innerHTML?: any;
        style?: any;
        accesskey?: any;
        contenteditable?: any;
        contextmenu?: any;
        dir?: any;
        draggable?: any;
        hidden?: any;
        id?: any;
        inert?: any;
        lang?: any;
        spellcheck?: any;
        tabindex?: any;
        translate?: any;
        radiogroup?: any;
        role?: any;
        about?: any;
        datatype?: any;
        inlist?: any;
        prefix?: any;
        property?: any;
        resource?: any;
        typeof?: any;
        vocab?: any;
        autocapitalize?: any;
        autocorrect?: any;
        autosave?: any;
        color?: any;
        itemprop?: any;
        itemscope?: any;
        itemtype?: any;
        itemid?: any;
        itemref?: any;
        results?: any;
        security?: any;
        unselectable?: any;
        inputmode?: any;
        is?: any;
        "aria-activedescendant"?: any;
        "aria-atomic"?: any;
        "aria-autocomplete"?: any;
        "aria-busy"?: any;
        "aria-checked"?: any;
        "aria-colcount"?: any;
        "aria-colindex"?: any;
        "aria-colspan"?: any;
        "aria-controls"?: any;
        "aria-current"?: any;
        "aria-describedby"?: any;
        "aria-details"?: any;
        "aria-disabled"?: any;
        "aria-dropeffect"?: any;
        "aria-errormessage"?: any;
        "aria-expanded"?: any;
        "aria-flowto"?: any;
        "aria-grabbed"?: any;
        "aria-haspopup"?: any;
        "aria-hidden"?: any;
        "aria-invalid"?: any;
        "aria-keyshortcuts"?: any;
        "aria-label"?: any;
        "aria-labelledby"?: any;
        "aria-level"?: any;
        "aria-live"?: any;
        "aria-modal"?: any;
        "aria-multiline"?: any;
        "aria-multiselectable"?: any;
        "aria-orientation"?: any;
        "aria-owns"?: any;
        "aria-placeholder"?: any;
        "aria-posinset"?: any;
        "aria-pressed"?: any;
        "aria-readonly"?: any;
        "aria-relevant"?: any;
        "aria-required"?: any;
        "aria-roledescription"?: any;
        "aria-rowcount"?: any;
        "aria-rowindex"?: any;
        "aria-rowspan"?: any;
        "aria-selected"?: any;
        "aria-setsize"?: any;
        "aria-sort"?: any;
        "aria-valuemax"?: any;
        "aria-valuemin"?: any;
        "aria-valuenow"?: any;
        "aria-valuetext"?: any;
    }>;
    dynamicRules: import("vue").Ref<FormSchemaRuleType | undefined, FormSchemaRuleType | undefined>;
    isDisabled: import("vue").Ref<boolean, boolean>;
    isIf: import("vue").Ref<boolean, boolean>;
    isRequired: import("vue").Ref<boolean, boolean>;
    isShow: import("vue").Ref<boolean, boolean>;
};
