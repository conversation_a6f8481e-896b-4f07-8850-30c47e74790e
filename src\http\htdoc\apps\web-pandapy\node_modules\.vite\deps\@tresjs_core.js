import {
  ACESFilmicToneMapping,
  ArrowHelper,
  BackSide,
  BufferAttribute,
  BufferGeometry,
  Camera,
  Clock,
  Color,
  DirectionalLightHelper,
  DoubleSide,
  Float32BufferAttribute,
  HemisphereLightHelper,
  Layers,
  Line,
  LineBasicMaterial,
  MathUtils,
  Mesh,
  MeshBasicMaterial,
  NoToneMapping,
  Object3D,
  PCFSoftShadowMap,
  PerspectiveCamera,
  PointLightHelper,
  REVISION,
  Raycaster,
  SRGBColorSpace,
  Scene,
  SpotLightHelper,
  TextureLoader,
  Vector2,
  Vector3,
  WebGLRenderer,
  three_module_exports
} from "./chunk-DRVXZSRF.js";
import "./chunk-JUL7CFXM.js";
import {
  Fragment,
  computed,
  createElementBlock,
  createRenderer,
  customRef,
  defineComponent,
  getCurrentInstance,
  getCurrentScope,
  h,
  hasInjectionContext,
  inject,
  isRef,
  nextTick,
  onMounted,
  onScopeDispose,
  onUnmounted,
  openBlock,
  provide,
  reactive,
  readonly,
  ref,
  renderSlot,
  shallowRef,
  toRefs,
  toValue,
  unref,
  useSlots,
  watch,
  watchEffect,
  withAsyncContext
} from "./chunk-ZLVVKZUX.js";
import {
  normalizeClass,
  normalizeStyle
} from "./chunk-CMAVT37G.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/@vueuse+shared@12.8.2_typescript@5.8.3/node_modules/@vueuse/shared/index.mjs
function tryOnScopeDispose(fn2) {
  if (getCurrentScope()) {
    onScopeDispose(fn2);
    return true;
  }
  return false;
}
function createEventHook() {
  const fns = /* @__PURE__ */ new Set();
  const off = (fn2) => {
    fns.delete(fn2);
  };
  const clear = () => {
    fns.clear();
  };
  const on2 = (fn2) => {
    fns.add(fn2);
    const offFn = () => off(fn2);
    tryOnScopeDispose(offFn);
    return {
      off: offFn
    };
  };
  const trigger = (...args) => {
    return Promise.all(Array.from(fns).map((fn2) => fn2(...args)));
  };
  return {
    on: on2,
    off,
    trigger,
    clear
  };
}
var localProvidedStateMap = /* @__PURE__ */ new WeakMap();
var injectLocal = (...args) => {
  var _a;
  const key = args[0];
  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;
  if (instance == null && !hasInjectionContext())
    throw new Error("injectLocal must be called in setup");
  if (instance && localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance))
    return localProvidedStateMap.get(instance)[key];
  return inject(...args);
};
var isClient = typeof window !== "undefined" && typeof document !== "undefined";
var isWorker = typeof WorkerGlobalScope !== "undefined" && globalThis instanceof WorkerGlobalScope;
var notNullish = (val) => val != null;
var toString = Object.prototype.toString;
var isObject = (val) => toString.call(val) === "[object Object]";
var noop = () => {
};
var isIOS = getIsIOS();
function getIsIOS() {
  var _a, _b;
  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));
}
function createFilterWrapper(filter, fn2) {
  function wrapper(...args) {
    return new Promise((resolve, reject) => {
      Promise.resolve(filter(() => fn2.apply(this, args), { fn: fn2, thisArg: this, args })).then(resolve).catch(reject);
    });
  }
  return wrapper;
}
function debounceFilter(ms, options = {}) {
  let timer;
  let maxTimer;
  let lastRejector = noop;
  const _clearTimeout = (timer2) => {
    clearTimeout(timer2);
    lastRejector();
    lastRejector = noop;
  };
  let lastInvoker;
  const filter = (invoke) => {
    const duration = toValue(ms);
    const maxDuration = toValue(options.maxWait);
    if (timer)
      _clearTimeout(timer);
    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {
      if (maxTimer) {
        _clearTimeout(maxTimer);
        maxTimer = null;
      }
      return Promise.resolve(invoke());
    }
    return new Promise((resolve, reject) => {
      lastRejector = options.rejectOnCancel ? reject : resolve;
      lastInvoker = invoke;
      if (maxDuration && !maxTimer) {
        maxTimer = setTimeout(() => {
          if (timer)
            _clearTimeout(timer);
          maxTimer = null;
          resolve(lastInvoker());
        }, maxDuration);
      }
      timer = setTimeout(() => {
        if (maxTimer)
          _clearTimeout(maxTimer);
        maxTimer = null;
        resolve(invoke());
      }, duration);
    });
  };
  return filter;
}
function cacheStringFunction(fn2) {
  const cache = /* @__PURE__ */ Object.create(null);
  return (str) => {
    const hit = cache[str];
    return hit || (cache[str] = fn2(str));
  };
}
var hyphenateRE = /\B([A-Z])/g;
var hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, "-$1").toLowerCase());
var camelizeRE = /-(\w)/g;
var camelize = cacheStringFunction((str) => {
  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : "");
});
function identity(arg) {
  return arg;
}
function pxValue(px) {
  return px.endsWith("rem") ? Number.parseFloat(px) * 16 : Number.parseFloat(px);
}
function objectPick(obj, keys2, omitUndefined = false) {
  return keys2.reduce((n, k) => {
    if (k in obj) {
      if (!omitUndefined || obj[k] !== void 0)
        n[k] = obj[k];
    }
    return n;
  }, {});
}
function getLifeCycleTarget(target) {
  return target || getCurrentInstance();
}
function toArray(value) {
  return Array.isArray(value) ? value : [value];
}
function useDebounceFn(fn2, ms = 200, options = {}) {
  return createFilterWrapper(
    debounceFilter(ms, options),
    fn2
  );
}
function refDebounced(value, ms = 200, options = {}) {
  const debounced = ref(value.value);
  const updater = useDebounceFn(() => {
    debounced.value = value.value;
  }, ms, options);
  watch(value, () => updater());
  return debounced;
}
function toRefs2(objectRef, options = {}) {
  if (!isRef(objectRef))
    return toRefs(objectRef);
  const result = Array.isArray(objectRef.value) ? Array.from({ length: objectRef.value.length }) : {};
  for (const key in objectRef.value) {
    result[key] = customRef(() => ({
      get() {
        return objectRef.value[key];
      },
      set(v) {
        var _a;
        const replaceRef = (_a = toValue(options.replaceRef)) != null ? _a : true;
        if (replaceRef) {
          if (Array.isArray(objectRef.value)) {
            const copy = [...objectRef.value];
            copy[key] = v;
            objectRef.value = copy;
          } else {
            const newObject = { ...objectRef.value, [key]: v };
            Object.setPrototypeOf(newObject, Object.getPrototypeOf(objectRef.value));
            objectRef.value = newObject;
          }
        } else {
          objectRef.value[key] = v;
        }
      }
    }));
  }
  return result;
}
var toValue2 = toValue;
function tryOnMounted(fn2, sync = true, target) {
  const instance = getLifeCycleTarget();
  if (instance)
    onMounted(fn2, target);
  else if (sync)
    fn2();
  else
    nextTick(fn2);
}
function useIntervalFn(cb, interval = 1e3, options = {}) {
  const {
    immediate = true,
    immediateCallback = false
  } = options;
  let timer = null;
  const isActive = shallowRef(false);
  function clean() {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  }
  function pause() {
    isActive.value = false;
    clean();
  }
  function resume() {
    const intervalValue = toValue(interval);
    if (intervalValue <= 0)
      return;
    isActive.value = true;
    if (immediateCallback)
      cb();
    clean();
    if (isActive.value)
      timer = setInterval(cb, intervalValue);
  }
  if (immediate && isClient)
    resume();
  if (isRef(interval) || typeof interval === "function") {
    const stopWatch = watch(interval, () => {
      if (isActive.value && isClient)
        resume();
    });
    tryOnScopeDispose(stopWatch);
  }
  tryOnScopeDispose(pause);
  return {
    isActive,
    pause,
    resume
  };
}
function watchImmediate(source, cb, options) {
  return watch(
    source,
    cb,
    {
      ...options,
      immediate: true
    }
  );
}

// ../../node_modules/.pnpm/@vueuse+core@12.8.2_typescript@5.8.3/node_modules/@vueuse/core/index.mjs
var defaultWindow = isClient ? window : void 0;
var defaultDocument = isClient ? window.document : void 0;
var defaultNavigator = isClient ? window.navigator : void 0;
var defaultLocation = isClient ? window.location : void 0;
function unrefElement(elRef) {
  var _a;
  const plain = toValue(elRef);
  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;
}
function useEventListener(...args) {
  const cleanups = [];
  const cleanup = () => {
    cleanups.forEach((fn2) => fn2());
    cleanups.length = 0;
  };
  const register = (el, event, listener, options) => {
    el.addEventListener(event, listener, options);
    return () => el.removeEventListener(event, listener, options);
  };
  const firstParamTargets = computed(() => {
    const test = toArray(toValue(args[0])).filter((e) => e != null);
    return test.every((e) => typeof e !== "string") ? test : void 0;
  });
  const stopWatch = watchImmediate(
    () => {
      var _a, _b;
      return [
        (_b = (_a = firstParamTargets.value) == null ? void 0 : _a.map((e) => unrefElement(e))) != null ? _b : [defaultWindow].filter((e) => e != null),
        toArray(toValue(firstParamTargets.value ? args[1] : args[0])),
        toArray(unref(firstParamTargets.value ? args[2] : args[1])),
        // @ts-expect-error - TypeScript gets the correct types, but somehow still complains
        toValue(firstParamTargets.value ? args[3] : args[2])
      ];
    },
    ([raw_targets, raw_events, raw_listeners, raw_options]) => {
      cleanup();
      if (!(raw_targets == null ? void 0 : raw_targets.length) || !(raw_events == null ? void 0 : raw_events.length) || !(raw_listeners == null ? void 0 : raw_listeners.length))
        return;
      const optionsClone = isObject(raw_options) ? { ...raw_options } : raw_options;
      cleanups.push(
        ...raw_targets.flatMap(
          (el) => raw_events.flatMap(
            (event) => raw_listeners.map((listener) => register(el, event, listener, optionsClone))
          )
        )
      );
    },
    { flush: "post" }
  );
  const stop = () => {
    stopWatch();
    cleanup();
  };
  tryOnScopeDispose(cleanup);
  return stop;
}
function useMounted() {
  const isMounted = shallowRef(false);
  const instance = getCurrentInstance();
  if (instance) {
    onMounted(() => {
      isMounted.value = true;
    }, instance);
  }
  return isMounted;
}
function useSupported(callback) {
  const isMounted = useMounted();
  return computed(() => {
    isMounted.value;
    return Boolean(callback());
  });
}
function useMutationObserver(target, callback, options = {}) {
  const { window: window2 = defaultWindow, ...mutationOptions } = options;
  let observer;
  const isSupported = useSupported(() => window2 && "MutationObserver" in window2);
  const cleanup = () => {
    if (observer) {
      observer.disconnect();
      observer = void 0;
    }
  };
  const targets = computed(() => {
    const value = toValue(target);
    const items = toArray(value).map(unrefElement).filter(notNullish);
    return new Set(items);
  });
  const stopWatch = watch(
    () => targets.value,
    (targets2) => {
      cleanup();
      if (isSupported.value && targets2.size) {
        observer = new MutationObserver(callback);
        targets2.forEach((el) => observer.observe(el, mutationOptions));
      }
    },
    { immediate: true, flush: "post" }
  );
  const takeRecords = () => {
    return observer == null ? void 0 : observer.takeRecords();
  };
  const stop = () => {
    stopWatch();
    cleanup();
  };
  tryOnScopeDispose(stop);
  return {
    isSupported,
    stop,
    takeRecords
  };
}
function useRafFn(fn2, options = {}) {
  const {
    immediate = true,
    fpsLimit = void 0,
    window: window2 = defaultWindow,
    once = false
  } = options;
  const isActive = shallowRef(false);
  const intervalLimit = computed(() => {
    return fpsLimit ? 1e3 / toValue(fpsLimit) : null;
  });
  let previousFrameTimestamp = 0;
  let rafId = null;
  function loop(timestamp2) {
    if (!isActive.value || !window2)
      return;
    if (!previousFrameTimestamp)
      previousFrameTimestamp = timestamp2;
    const delta = timestamp2 - previousFrameTimestamp;
    if (intervalLimit.value && delta < intervalLimit.value) {
      rafId = window2.requestAnimationFrame(loop);
      return;
    }
    previousFrameTimestamp = timestamp2;
    fn2({ delta, timestamp: timestamp2 });
    if (once) {
      isActive.value = false;
      rafId = null;
      return;
    }
    rafId = window2.requestAnimationFrame(loop);
  }
  function resume() {
    if (!isActive.value && window2) {
      isActive.value = true;
      previousFrameTimestamp = 0;
      rafId = window2.requestAnimationFrame(loop);
    }
  }
  function pause() {
    isActive.value = false;
    if (rafId != null && window2) {
      window2.cancelAnimationFrame(rafId);
      rafId = null;
    }
  }
  if (immediate)
    resume();
  tryOnScopeDispose(pause);
  return {
    isActive: readonly(isActive),
    pause,
    resume
  };
}
var ssrWidthSymbol = Symbol("vueuse-ssr-width");
function useSSRWidth() {
  const ssrWidth = hasInjectionContext() ? injectLocal(ssrWidthSymbol, null) : null;
  return typeof ssrWidth === "number" ? ssrWidth : void 0;
}
function useMediaQuery(query, options = {}) {
  const { window: window2 = defaultWindow, ssrWidth = useSSRWidth() } = options;
  const isSupported = useSupported(() => window2 && "matchMedia" in window2 && typeof window2.matchMedia === "function");
  const ssrSupport = shallowRef(typeof ssrWidth === "number");
  const mediaQuery = shallowRef();
  const matches = shallowRef(false);
  const handler = (event) => {
    matches.value = event.matches;
  };
  watchEffect(() => {
    if (ssrSupport.value) {
      ssrSupport.value = !isSupported.value;
      const queryStrings = toValue(query).split(",");
      matches.value = queryStrings.some((queryString) => {
        const not = queryString.includes("not all");
        const minWidth = queryString.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);
        const maxWidth = queryString.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);
        let res = Boolean(minWidth || maxWidth);
        if (minWidth && res) {
          res = ssrWidth >= pxValue(minWidth[1]);
        }
        if (maxWidth && res) {
          res = ssrWidth <= pxValue(maxWidth[1]);
        }
        return not ? !res : res;
      });
      return;
    }
    if (!isSupported.value)
      return;
    mediaQuery.value = window2.matchMedia(toValue(query));
    matches.value = mediaQuery.value.matches;
  });
  useEventListener(mediaQuery, "change", handler, { passive: true });
  return computed(() => matches.value);
}
var _global = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
var globalKey = "__vueuse_ssr_handlers__";
var handlers = getHandlers();
function getHandlers() {
  if (!(globalKey in _global))
    _global[globalKey] = _global[globalKey] || {};
  return _global[globalKey];
}
function useDevicePixelRatio(options = {}) {
  const {
    window: window2 = defaultWindow
  } = options;
  const pixelRatio = shallowRef(1);
  const query = useMediaQuery(() => `(resolution: ${pixelRatio.value}dppx)`, options);
  let stop = noop;
  if (window2) {
    stop = watchImmediate(query, () => pixelRatio.value = window2.devicePixelRatio);
  }
  return {
    pixelRatio: readonly(pixelRatio),
    stop
  };
}
function useResizeObserver(target, callback, options = {}) {
  const { window: window2 = defaultWindow, ...observerOptions } = options;
  let observer;
  const isSupported = useSupported(() => window2 && "ResizeObserver" in window2);
  const cleanup = () => {
    if (observer) {
      observer.disconnect();
      observer = void 0;
    }
  };
  const targets = computed(() => {
    const _targets = toValue(target);
    return Array.isArray(_targets) ? _targets.map((el) => unrefElement(el)) : [unrefElement(_targets)];
  });
  const stopWatch = watch(
    targets,
    (els) => {
      cleanup();
      if (isSupported.value && window2) {
        observer = new ResizeObserver(callback);
        for (const _el of els) {
          if (_el)
            observer.observe(_el, observerOptions);
        }
      }
    },
    { immediate: true, flush: "post" }
  );
  const stop = () => {
    cleanup();
    stopWatch();
  };
  tryOnScopeDispose(stop);
  return {
    isSupported,
    stop
  };
}
function useElementBounding(target, options = {}) {
  const {
    reset = true,
    windowResize = true,
    windowScroll = true,
    immediate = true,
    updateTiming = "sync"
  } = options;
  const height = shallowRef(0);
  const bottom = shallowRef(0);
  const left = shallowRef(0);
  const right = shallowRef(0);
  const top = shallowRef(0);
  const width = shallowRef(0);
  const x = shallowRef(0);
  const y = shallowRef(0);
  function recalculate() {
    const el = unrefElement(target);
    if (!el) {
      if (reset) {
        height.value = 0;
        bottom.value = 0;
        left.value = 0;
        right.value = 0;
        top.value = 0;
        width.value = 0;
        x.value = 0;
        y.value = 0;
      }
      return;
    }
    const rect = el.getBoundingClientRect();
    height.value = rect.height;
    bottom.value = rect.bottom;
    left.value = rect.left;
    right.value = rect.right;
    top.value = rect.top;
    width.value = rect.width;
    x.value = rect.x;
    y.value = rect.y;
  }
  function update() {
    if (updateTiming === "sync")
      recalculate();
    else if (updateTiming === "next-frame")
      requestAnimationFrame(() => recalculate());
  }
  useResizeObserver(target, update);
  watch(() => unrefElement(target), (ele) => !ele && update());
  useMutationObserver(target, update, {
    attributeFilter: ["style", "class"]
  });
  if (windowScroll)
    useEventListener("scroll", update, { capture: true, passive: true });
  if (windowResize)
    useEventListener("resize", update, { passive: true });
  tryOnMounted(() => {
    if (immediate)
      update();
  });
  return {
    height,
    bottom,
    left,
    right,
    top,
    width,
    x,
    y,
    update
  };
}
function useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {
  const { window: window2 = defaultWindow, box = "content-box" } = options;
  const isSVG = computed(() => {
    var _a, _b;
    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes("svg");
  });
  const width = shallowRef(initialSize.width);
  const height = shallowRef(initialSize.height);
  const { stop: stop1 } = useResizeObserver(
    target,
    ([entry]) => {
      const boxSize = box === "border-box" ? entry.borderBoxSize : box === "content-box" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;
      if (window2 && isSVG.value) {
        const $elem = unrefElement(target);
        if ($elem) {
          const rect = $elem.getBoundingClientRect();
          width.value = rect.width;
          height.value = rect.height;
        }
      } else {
        if (boxSize) {
          const formatBoxSize = toArray(boxSize);
          width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);
          height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);
        } else {
          width.value = entry.contentRect.width;
          height.value = entry.contentRect.height;
        }
      }
    },
    options
  );
  tryOnMounted(() => {
    const ele = unrefElement(target);
    if (ele) {
      width.value = "offsetWidth" in ele ? ele.offsetWidth : initialSize.width;
      height.value = "offsetHeight" in ele ? ele.offsetHeight : initialSize.height;
    }
  });
  const stop2 = watch(
    () => unrefElement(target),
    (ele) => {
      width.value = ele ? initialSize.width : 0;
      height.value = ele ? initialSize.height : 0;
    }
  );
  function stop() {
    stop1();
    stop2();
  }
  return {
    width,
    height,
    stop
  };
}
function useFps(options) {
  var _a;
  const fps = shallowRef(0);
  if (typeof performance === "undefined")
    return fps;
  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;
  let last = performance.now();
  let ticks = 0;
  useRafFn(() => {
    ticks += 1;
    if (ticks >= every) {
      const now = performance.now();
      const diff = now - last;
      fps.value = Math.round(1e3 / (diff / ticks));
      last = now;
      ticks = 0;
    }
  });
  return fps;
}
function useMemory(options = {}) {
  const memory = ref();
  const isSupported = useSupported(() => typeof performance !== "undefined" && "memory" in performance);
  if (isSupported.value) {
    const { interval = 1e3 } = options;
    useIntervalFn(() => {
      memory.value = performance.memory;
    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });
  }
  return { isSupported, memory };
}
var defaultState = {
  x: 0,
  y: 0,
  pointerId: 0,
  pressure: 0,
  tiltX: 0,
  tiltY: 0,
  width: 0,
  height: 0,
  twist: 0,
  pointerType: null
};
var keys = Object.keys(defaultState);
function usePointer(options = {}) {
  const {
    target = defaultWindow
  } = options;
  const isInside = shallowRef(false);
  const state = ref(options.initialValue || {});
  Object.assign(state.value, defaultState, state.value);
  const handler = (event) => {
    isInside.value = true;
    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))
      return;
    state.value = objectPick(event, keys, false);
  };
  if (target) {
    const listenerOptions = { passive: true };
    useEventListener(target, ["pointerdown", "pointermove", "pointerup"], handler, listenerOptions);
    useEventListener(target, "pointerleave", () => isInside.value = false, listenerOptions);
  }
  return {
    ...toRefs2(state),
    isInside
  };
}
var DEFAULT_UNITS = [
  { max: 6e4, value: 1e3, name: "second" },
  { max: 276e4, value: 6e4, name: "minute" },
  { max: 72e6, value: 36e5, name: "hour" },
  { max: 5184e5, value: 864e5, name: "day" },
  { max: 24192e5, value: 6048e5, name: "week" },
  { max: 28512e6, value: 2592e6, name: "month" },
  { max: Number.POSITIVE_INFINITY, value: 31536e6, name: "year" }
];
var _TransitionPresets = {
  easeInSine: [0.12, 0, 0.39, 0],
  easeOutSine: [0.61, 1, 0.88, 1],
  easeInOutSine: [0.37, 0, 0.63, 1],
  easeInQuad: [0.11, 0, 0.5, 0],
  easeOutQuad: [0.5, 1, 0.89, 1],
  easeInOutQuad: [0.45, 0, 0.55, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeInQuart: [0.5, 0, 0.75, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  easeInQuint: [0.64, 0, 0.78, 0],
  easeOutQuint: [0.22, 1, 0.36, 1],
  easeInOutQuint: [0.83, 0, 0.17, 1],
  easeInExpo: [0.7, 0, 0.84, 0],
  easeOutExpo: [0.16, 1, 0.3, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInCirc: [0.55, 0, 1, 0.45],
  easeOutCirc: [0, 0.55, 0.45, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInBack: [0.36, 0, 0.66, -0.56],
  easeOutBack: [0.34, 1.56, 0.64, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6]
};
var TransitionPresets = Object.assign({}, { linear: identity }, _TransitionPresets);
function useWindowSize(options = {}) {
  const {
    window: window2 = defaultWindow,
    initialWidth = Number.POSITIVE_INFINITY,
    initialHeight = Number.POSITIVE_INFINITY,
    listenOrientation = true,
    includeScrollbar = true,
    type = "inner"
  } = options;
  const width = shallowRef(initialWidth);
  const height = shallowRef(initialHeight);
  const update = () => {
    if (window2) {
      if (type === "outer") {
        width.value = window2.outerWidth;
        height.value = window2.outerHeight;
      } else if (type === "visual" && window2.visualViewport) {
        const { width: visualViewportWidth, height: visualViewportHeight, scale } = window2.visualViewport;
        width.value = Math.round(visualViewportWidth * scale);
        height.value = Math.round(visualViewportHeight * scale);
      } else if (includeScrollbar) {
        width.value = window2.innerWidth;
        height.value = window2.innerHeight;
      } else {
        width.value = window2.document.documentElement.clientWidth;
        height.value = window2.document.documentElement.clientHeight;
      }
    }
  };
  update();
  tryOnMounted(update);
  const listenerOptions = { passive: true };
  useEventListener("resize", update, listenerOptions);
  if (window2 && type === "visual" && window2.visualViewport) {
    useEventListener(window2.visualViewport, "resize", update, listenerOptions);
  }
  if (listenOrientation) {
    const matches = useMediaQuery("(orientation: portrait)");
    watch(matches, () => update());
  }
  return { width, height };
}

// ../../node_modules/.pnpm/@tresjs+core@4.3.6_three@0.177.0_typescript@5.8.3_vue@3.5.13_typescript@5.8.3_/node_modules/@tresjs/core/dist/tres.js
var At = Object.defineProperty;
var xt = (e, t, r) => t in e ? At(e, t, { enumerable: true, configurable: true, writable: true, value: r }) : e[t] = r;
var de = (e, t, r) => xt(e, typeof t != "symbol" ? t + "" : t, r);
var gr = "@tresjs/core";
var hr = "module";
var vr = "4.3.6";
var yr = "pnpm@10.6.3";
var _r = "Declarative ThreeJS using Vue Components";
var wr = "Alvaro Saburido <<EMAIL>> (https://github.com/alvarosabu/)";
var br = "MIT";
var Mr = { type: "git", url: "git+https://github.com/Tresjs/tres.git" };
var Pr = ["vue", "3d", "threejs", "three", "threejs-vue"];
var Cr = false;
var Er = { ".": { types: "./dist/index.d.ts", import: "./dist/tres.js", require: "./dist/tres.umd.cjs" }, "./components": { types: "./dist/src/components/index.d.ts" }, "./composables": { types: "./dist/src/composables/index.d.ts" }, "./types": { types: "./dist/src/types/index.d.ts" }, "./utils": { types: "./dist/src/utils/index.d.ts" }, "./*": "./*" };
var Tr = "./dist/tres.js";
var Sr = "./dist/tres.js";
var Ar = "./dist/index.d.ts";
var xr = ["*.d.ts", "dist"];
var kr = { access: "public" };
var Lr = { dev: "pnpm --filter='./playground/vue' dev", "dev:nuxt": "pnpm --filter='./playground/nuxt' dev", build: "vite build", test: "vitest", "test:ci": "vitest run", "test:ui": "vitest --ui --coverage.enabled=true", release: "release-it", coverage: "vitest run --coverage", lint: "eslint .", "lint:fix": "eslint . --fix", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "vitepress serve docs", "docs:preview": "vitepress preview docs", "docs:contributors": "esno scripts/update-contributors.ts" };
var Rr = { three: ">=0.133", vue: ">=3.4" };
var Or = { "@alvarosabu/utils": "^3.2.0", "@vue/devtools-api": "^6.6.3", "@vueuse/core": "^12.5.0" };
var Dr = { "@release-it/conventional-changelog": "^10.0.0", "@stackblitz/sdk": "^1.11.0", "@tresjs/cientos": "4.1.0", "@tresjs/eslint-config": "^1.4.0", "@types/three": "^0.173.0", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.5", "@vue/test-utils": "^2.4.6", eslint: "^9.19.0", "eslint-plugin-vue": "^9.32.0", esno: "^4.8.0", gsap: "^3.12.7", jsdom: "^26.0.0", kolorist: "^1.8.0", ohmyfetch: "^0.4.21", pathe: "^2.0.2", "release-it": "^18.1.2", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^5.14.0", sponsorkit: "^16.3.0", three: "^0.173.0", unocss: "^65.4.3", unplugin: "^2.1.2", "unplugin-vue-components": "^28.0.0", vite: "^6.1.0", "vite-plugin-banner": "^0.8.0", "vite-plugin-dts": "4.5.0", "vite-plugin-inspect": "^10.1.0", "vite-plugin-require-transform": "^1.0.21", "vite-svg-loader": "^5.1.0", vitepress: "1.6.3", vitest: "3.0.5", vue: "3.5.13", "vue-demi": "^0.14.10" };
var jr = {
  name: gr,
  type: hr,
  version: vr,
  packageManager: yr,
  description: _r,
  author: wr,
  license: br,
  repository: Mr,
  keywords: Pr,
  sideEffects: Cr,
  exports: Er,
  main: Tr,
  module: Sr,
  types: Ar,
  files: xr,
  publishConfig: kr,
  scripts: Lr,
  peerDependencies: Rr,
  dependencies: Or,
  devDependencies: Dr
};
function Br(e) {
  const t = { nodes: {}, materials: {} };
  return e && e.traverse((r) => {
    r.name && (t.nodes[r.name] = r), r.material && !t.materials[r.material.name] && (t.materials[r.material.name] = r.material);
  }), t;
}
async function Ir(e, t, r, n, o) {
  const { logError: l } = Q(), s = new e();
  return o && o(s), r && r(s), await new Promise((a, c) => {
    s.load(
      t,
      (i) => {
        const f = i;
        f.scene && Object.assign(f, Br(f.scene)), a(f);
      },
      n,
      (i) => {
        l("[useLoader] - Failed to load resource", i), c(i);
      }
    );
  });
}
var eo = defineComponent({
  __name: "component",
  props: {
    loader: {},
    url: {}
  },
  async setup(e) {
    let t, r;
    const n = e, o = ([t, r] = withAsyncContext(() => reactive(Ir(n.loader, n.url))), t = await t, r(), t);
    return (l, s) => renderSlot(l.$slots, "default", { data: unref(o) });
  }
});
var $r = class extends Mesh {
  constructor(...r) {
    super(...r);
    de(this, "type", "HightlightMesh");
    de(this, "createTime");
    this.createTime = Date.now();
  }
  onBeforeRender() {
    const n = (Date.now() - this.createTime) / 1e3, s = 1 + 0.07 * Math.sin(2.5 * n);
    this.scale.set(s, s, s);
  }
};
function be(e) {
  return typeof e > "u";
}
function ke(e) {
  return Array.isArray(e);
}
function Hr(e) {
  return typeof e == "number";
}
function dt(e) {
  return typeof e == "string";
}
function Y(e) {
  return typeof e == "function";
}
function j(e) {
  return e === Object(e) && !ke(e) && !Y(e);
}
function N(e) {
  return j(e) && !!e.isObject3D;
}
function mt(e) {
  return j(e) && !!e.isColor;
}
function Ur(e) {
  return e != null && (typeof e == "string" || typeof e == "number" || mt(e));
}
function Me(e) {
  return e !== null && typeof e == "object" && "set" in e && typeof e.set == "function";
}
function Fr(e) {
  return Me(e) && "copy" in e && typeof e.copy == "function";
}
function Wr(e) {
  return !!(e != null && e.constructor);
}
function Ge(e) {
  return e instanceof Layers;
}
function Ve(e) {
  return j(e) && !!e.isCamera;
}
function Nr(e) {
  return j(e) && !!e.isBufferGeometry;
}
function zr(e) {
  return j(e) && !!e.isMaterial;
}
function Gr(e) {
  return j(e) && !!e.isLight;
}
function Vr(e) {
  return j(e) && !!e.isFog;
}
function Yr(e) {
  return j(e) && !!e.isScene;
}
function ne(e) {
  return N(e) || Nr(e) || zr(e) || Vr(e);
}
function qr(e) {
  return j(e) && !!e.isPrimitive;
}
var gt = (e, t) => {
  for (const r of Object.keys(t))
    t[r] instanceof Object && Object.assign(t[r], gt(e[r], t[r]));
  return Object.assign(e || {}, t), e;
};
var Kr = "html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot";
var Jr = Zr(Kr);
function Ye(e) {
  return e && e.nodeType === 1;
}
function he(e) {
  return e.replace(/-([a-z])/g, (t, r) => r.toUpperCase());
}
var Qr = /\B([A-Z])/g;
function Xr(e) {
  return e.replace(Qr, "-$1").toLowerCase();
}
function Zr(e, t) {
  const r = /* @__PURE__ */ Object.create(null), n = e.split(",");
  for (let o = 0; o < n.length; o++)
    r[n[o]] = true;
  return t ? (o) => !!r[o.toLowerCase()] : (o) => !!r[o];
}
var qe = (e, t) => {
  if (!t)
    return;
  const r = Array.isArray(t) ? t : t.match(/([^[.\]])+/g);
  return r == null ? void 0 : r.reduce((n, o) => n && n[o], e);
};
var en = (e, t, r) => {
  const n = Array.isArray(t) ? t : t.match(/([^[.\]])+/g);
  n && n.reduce((o, l, s) => (o[l] === void 0 && (o[l] = {}), s === n.length - 1 && (o[l] = r), o[l]), e);
};
function ht(e, t) {
  if (Ye(e) && Ye(t)) {
    const o = e.attributes, l = t.attributes;
    return o.length !== l.length ? false : Array.from(o).every(({ name: s, value: a }) => t.getAttribute(s) === a);
  }
  if (e === t)
    return true;
  if (e === null || typeof e != "object" || t === null || typeof t != "object")
    return false;
  const r = Object.keys(e), n = Object.keys(t);
  if (r.length !== n.length)
    return false;
  for (const o of r)
    if (!n.includes(o) || !ht(e[o], t[o]))
      return false;
  return true;
}
function tn(e, t) {
  if (!Array.isArray(e) || !Array.isArray(t) || e.length !== t.length)
    return false;
  for (let r = 0; r < e.length; r++)
    if (!ht(e[r], t[r]))
      return false;
  return true;
}
var rn = Array.isArray;
function nn(e, t, r, n) {
  const o = (c) => {
    if (c.uuid === t)
      return c;
    for (const i of c.children) {
      const f = o(i);
      if (f)
        return f;
    }
  }, l = o(e);
  if (!l) {
    console.warn("Object with UUID not found in the scene.");
    return;
  }
  let s = l;
  for (let c = 0; c < r.length - 1; c++)
    if (s[r[c]] !== void 0)
      s = s[r[c]];
    else {
      console.warn(`Property path is not valid: ${r.join(".")}`);
      return;
    }
  const a = r[r.length - 1];
  s[a] !== void 0 ? s[a] = n : console.warn(`Property path is not valid: ${r.join(".")}`);
}
function on(e) {
  const t = new MeshBasicMaterial({
    color: 11003607,
    // Highlight color, e.g., yellow
    transparent: true,
    opacity: 0.2,
    depthTest: false,
    // So the highlight is always visible
    side: DoubleSide
    // To e
  });
  return new $r(e.geometry.clone(), t);
}
function sn(e) {
  var r;
  let t = e.value;
  return e.value && ((r = e.value) != null && r.isMesh) && (t = e.value.position), Array.isArray(e.value) && (t = new Vector3(...t)), t;
}
function an(e) {
  return "map" in e;
}
function Ke(e) {
  an(e) && e.map && e.map.dispose(), e.dispose();
}
function vt(e) {
  var r, n;
  if (e.parent && ((r = e.removeFromParent) == null || r.call(e)), delete e.__tres, [...e.children].forEach((o) => vt(o)), !(e instanceof Scene)) {
    const o = e;
    e && ((n = e.dispose) == null || n.call(e)), o.geometry && o.geometry.dispose(), Array.isArray(o.material) ? o.material.forEach((l) => Ke(l)) : o.material && Ke(o.material);
  }
}
function ln(e, t) {
  let r = 0;
  for (let n = 0; n < e.length; n++)
    t(e[n], n) && (e[r] = e[n], r++);
  return e.length = r, e;
}
function Pe(e, t) {
  let r = e;
  if (t.includes("-")) {
    const n = t.split("-");
    let o = n.shift();
    for (; r && n.length; )
      o in r ? (r = r[o], o = n.shift()) : o = Je(o, n.shift());
    return { target: r, key: Je(o, ...n) };
  } else
    return { target: r, key: t };
}
function Je(...e) {
  return e.map((t, r) => r === 0 ? t : t.charAt(0).toUpperCase() + t.slice(1)).join("");
}
var Qe = /-\d+$/;
function cn(e, t, r) {
  if (dt(r)) {
    if (Qe.test(r)) {
      const l = r.replace(Qe, ""), { target: s, key: a } = Pe(e, l);
      if (!Array.isArray(s[a])) {
        const c = s[a], i = [];
        i.__tresDetach = () => {
          i.every((f) => be(f)) && (s[a] = c);
        }, s[a] = i;
      }
    }
    const { target: n, key: o } = Pe(e, r);
    t.__tres.previousAttach = n[o], n[o] = J(t);
  } else
    t.__tres.previousAttach = r(e, t);
}
function un(e, t, r) {
  var n, o, l;
  if (dt(r)) {
    const { target: s, key: a } = Pe(e, r), c = t.__tres.previousAttach;
    c === void 0 ? delete s[a] : s[a] = c, "__tresDetach" in s && s.__tresDetach();
  } else
    (o = (n = t.__tres) == null ? void 0 : n.previousAttach) == null || o.call(n, e, t);
  (l = t.__tres) == null || delete l.previousAttach;
}
function z(e, t, r) {
  const n = e;
  return n.__tres = {
    type: "unknown",
    eventCount: 0,
    root: r,
    handlers: {},
    memoizedProps: {},
    objects: [],
    parent: null,
    previousAttach: null,
    ...t
  }, n.__tres.attach || (n.isMaterial ? n.__tres.attach = "material" : n.isBufferGeometry ? n.__tres.attach = "geometry" : n.isFog && (n.__tres.attach = "fog")), n;
}
function yt(e) {
  var r;
  const t = (r = e == null ? void 0 : e.__tres) == null ? void 0 : r.root;
  t && t.render && t.render.canBeInvalidated.value && t.invalidate();
}
function fn(e, t, r) {
  var o;
  if (!Y(e.setPixelRatio))
    return;
  let n = 0;
  if (r && ke(r) && r.length >= 2) {
    const [l, s] = r;
    n = MathUtils.clamp(t, l, s);
  } else Hr(r) ? n = r : n = t;
  n !== ((o = e.getPixelRatio) == null ? void 0 : o.call(e)) && e.setPixelRatio(n);
}
function pn(e, t, r, n, o) {
  const l = [...t.__tres.objects], s = J(t);
  if (e = J(e), s === e)
    return true;
  const a = z(e, t.__tres ?? {}, o), c = t.parent ?? t.__tres.parent ?? null, i = { ...t.__tres.memoizedProps };
  delete i.object;
  for (const f of l)
    _t(f, o), wt(f, o);
  s.__tres.objects = [], n.remove(t);
  for (const [f, v] of Object.entries(i))
    n.patchProp(a, f, a[f], v);
  r(e), n.insert(t, c);
  for (const f of l)
    n.insert(f, t);
  return true;
}
function J(e) {
  return qr(e) ? (e.object.__tres = e.__tres, e.object) : e;
}
function _t(e, t) {
  var n, o, l, s;
  const r = ((n = e.__tres) == null ? void 0 : n.parent) || t.scene.value;
  e.__tres && (e.__tres.parent = null), r && r.__tres && "objects" in r.__tres && ln(r.__tres.objects, (a) => a !== e), (o = e.__tres) != null && o.attach ? un(r, e, e.__tres.attach) : ((s = (l = e.parent) == null ? void 0 : l.remove) == null || s.call(l, J(e)), e.parent = null);
}
function wt(e, t) {
  var r;
  (r = e.traverse) == null || r.call(e, (n) => {
    var o;
    t.deregisterCamera(n), (o = t.eventManager) == null || o.deregisterPointerMissedObject(n);
  }), t.deregisterCamera(e), yt(e);
}
async function dn(e, t) {
  const r = new TextureLoader(t), n = (o) => new Promise((l, s) => {
    r.load(
      o,
      (a) => l(a),
      () => null,
      () => {
        s(new Error("[useTextures] - Failed to load texture"));
      }
    );
  });
  if (rn(e)) {
    const o = await Promise.all(e.map((l) => n(l)));
    return e.length > 1 ? o : o[0];
  } else {
    const {
      map: o,
      displacementMap: l,
      normalMap: s,
      roughnessMap: a,
      metalnessMap: c,
      aoMap: i,
      alphaMap: f,
      matcap: v
    } = e;
    return {
      map: o ? await n(o) : null,
      displacementMap: l ? await n(l) : null,
      normalMap: s ? await n(s) : null,
      roughnessMap: a ? await n(a) : null,
      metalnessMap: c ? await n(c) : null,
      aoMap: i ? await n(i) : null,
      alphaMap: f ? await n(f) : null,
      matcap: v ? await n(v) : null
    };
  }
}
var to = defineComponent({
  __name: "component",
  props: {
    map: {},
    displacementMap: {},
    normalMap: {},
    roughnessMap: {},
    metalnessMap: {},
    aoMap: {},
    alphaMap: {},
    matcap: {}
  },
  async setup(e) {
    let t, r;
    const n = e, o = ([t, r] = withAsyncContext(() => reactive(dn(n))), t = await t, r(), t);
    return (l, s) => renderSlot(l.$slots, "default", { textures: unref(o) });
  }
});
var mn = ({ sizes: e }) => {
  const t = ref([]), r = computed(
    () => t.value[0]
  ), n = (s) => {
    const a = s instanceof Camera ? s : t.value.find((i) => i.uuid === s);
    if (!a)
      return;
    const c = t.value.filter(({ uuid: i }) => i !== a.uuid);
    t.value = [a, ...c];
  }, o = (s, a = false) => {
    if (Ve(s)) {
      const c = s;
      if (t.value.some(({ uuid: i }) => i === c.uuid))
        return;
      a ? n(c) : t.value.push(c);
    }
  }, l = (s) => {
    if (Ve(s)) {
      const a = s;
      t.value = t.value.filter(({ uuid: c }) => c !== a.uuid);
    }
  };
  return watchEffect(() => {
    e.aspectRatio.value && t.value.forEach((s) => {
      !s.manual && (s instanceof PerspectiveCamera || gn(s)) && (s instanceof PerspectiveCamera ? s.aspect = e.aspectRatio.value : (s.left = e.width.value * -0.5, s.right = e.width.value * 0.5, s.top = e.height.value * 0.5, s.bottom = e.height.value * -0.5), s.updateProjectionMatrix());
    });
  }), onUnmounted(() => {
    t.value = [];
  }), {
    camera: r,
    cameras: t,
    registerCamera: o,
    deregisterCamera: l,
    setCameraActive: n
  };
};
function gn(e) {
  return e.hasOwnProperty("isOrthographicCamera") && e.isOrthographicCamera;
}
var ro = true;
var fe = "[TresJS ▲ ■ ●] ";
function hn(...e) {
  typeof e[0] == "string" ? e[0] = fe + e[0] : e.unshift(fe), console.error(...e);
}
function vn(...e) {
  typeof e[0] == "string" ? e[0] = fe + e[0] : e.unshift(fe), console.warn(...e);
}
function yn(e, t) {
}
function Q() {
  return {
    logError: hn,
    logWarning: vn,
    logMessage: yn
  };
}
var Ce = ref({});
var Ee = (e) => Object.assign(Ce.value, e);
function ve() {
  const e = /* @__PURE__ */ new Map(), t = /* @__PURE__ */ new Set();
  let r = 0, n = false;
  const o = () => {
    const i = Array.from(e.entries()).sort((f, v) => {
      const u = f[1].priority - v[1].priority;
      return u === 0 ? f[1].addI - v[1].addI : u;
    });
    t.clear(), i.forEach((f) => t.add(f[0]));
  }, l = (i) => {
    e.delete(i), t.delete(i);
  };
  return { on: (i, f = 0) => {
    e.set(i, { priority: f, addI: r++ });
    const v = () => l(i);
    return tryOnScopeDispose(v), n = true, {
      off: v
    };
  }, off: l, trigger: (...i) => {
    n && (o(), n = false), t.forEach((f) => f(...i));
  }, dispose: () => {
    e.clear(), t.clear();
  }, get count() {
    return e.size;
  } };
}
function _n() {
  let e = true, t = true, r = false;
  const n = new Clock(false), o = ref(n.running), l = ref(false);
  let s;
  const a = MathUtils.generateUUID();
  let c = null;
  const i = ve(), f = ve(), v = ve();
  S();
  let u = {};
  function _(M) {
    u = M;
  }
  function d(M, x, m = 0) {
    switch (x) {
      case "before":
        return i.on(M, m);
      case "render":
        return c || (c = M), f.dispose(), f.on(M);
      case "after":
        return v.on(M, m);
    }
  }
  function y() {
    t && (t = false, S(), E());
  }
  function p() {
    t = true, S(), cancelAnimationFrame(s);
  }
  function h2() {
    r = false, S();
  }
  function P() {
    r = true, S();
  }
  function C() {
    l.value = true;
  }
  function b() {
    l.value = false;
  }
  function E() {
    if (!e) {
      s = requestAnimationFrame(E);
      return;
    }
    const M = n.getDelta(), x = n.getElapsedTime(), m = {
      camera: unref(u.camera),
      scene: unref(u.scene),
      renderer: unref(u.renderer),
      raycaster: unref(u.raycaster),
      controls: unref(u.controls),
      invalidate: u.invalidate,
      advance: u.advance
    }, w = { delta: M, elapsed: x, clock: n, ...m };
    o.value && i.trigger(w), l.value || (f.count ? f.trigger(w) : c && c(w)), o.value && v.trigger(w), s = requestAnimationFrame(E);
  }
  function S() {
    const M = !t && !r;
    n.running !== M && (n.running ? n.stop() : n.start()), o.value = n.running;
  }
  return {
    loopId: a,
    register: (M, x, m) => d(M, x, m),
    start: y,
    stop: p,
    pause: P,
    resume: h2,
    pauseRender: C,
    resumeRender: b,
    isRenderPaused: l,
    isActive: o,
    setContext: _,
    setReady: (M) => e = M
  };
}
function Le(e) {
  let t = 0;
  return e.traverse((r) => {
    if (r.isMesh && r.geometry && r.type !== "HightlightMesh") {
      const n = r.geometry, o = n.attributes.position.count * 3 * Float32Array.BYTES_PER_ELEMENT, l = n.index ? n.index.count * Uint32Array.BYTES_PER_ELEMENT : 0, s = n.attributes.normal ? n.attributes.normal.count * 3 * Float32Array.BYTES_PER_ELEMENT : 0, a = n.attributes.uv ? n.attributes.uv.count * 2 * Float32Array.BYTES_PER_ELEMENT : 0, c = o + l + s + a;
      t += c;
    }
  }), t;
}
function wn(e) {
  return (e / 1024).toFixed(2);
}
var bn = Number.parseInt(REVISION.replace("dev", ""));
function no(e) {
  return typeof e == "number" ? [e, e, e] : e instanceof Vector3 ? [e.x, e.y, e.z] : e;
}
function Mn(e) {
  return e instanceof Color ? e : Array.isArray(e) ? new Color(...e) : new Color(e);
}
var oe = {
  realistic: {
    shadows: true,
    physicallyCorrectLights: true,
    outputColorSpace: SRGBColorSpace,
    toneMapping: ACESFilmicToneMapping,
    toneMappingExposure: 3,
    shadowMap: {
      enabled: true,
      type: PCFSoftShadowMap
    }
  },
  flat: {
    toneMapping: NoToneMapping,
    toneMappingExposure: 1
  }
};
function Pn({
  canvas: e,
  options: t,
  contextParts: { sizes: r, render: n, invalidate: o, advance: l }
}) {
  const s = computed(() => ({
    alpha: toValue2(t.alpha) ?? true,
    depth: toValue2(t.depth),
    canvas: unrefElement(e),
    context: toValue2(t.context),
    stencil: toValue2(t.stencil),
    antialias: toValue2(t.antialias) ?? true,
    precision: toValue2(t.precision),
    powerPreference: toValue2(t.powerPreference),
    premultipliedAlpha: toValue2(t.premultipliedAlpha),
    preserveDrawingBuffer: toValue2(t.preserveDrawingBuffer),
    logarithmicDepthBuffer: toValue2(t.logarithmicDepthBuffer),
    failIfMajorPerformanceCaveat: toValue2(t.failIfMajorPerformanceCaveat)
  })), a = shallowRef(new WebGLRenderer(s.value));
  function c() {
    t.renderMode === "on-demand" && o();
  }
  watch(s, () => {
    a.value.dispose(), a.value = new WebGLRenderer(s.value), c();
  }), watch([r.width, r.height], () => {
    a.value.setSize(r.width.value, r.height.value), c();
  }, {
    immediate: true
  }), watch(() => t.clearColor, c);
  const { pixelRatio: i } = useDevicePixelRatio(), { logError: f } = Q(), u = (() => {
    const d = new WebGLRenderer(), y = {
      shadowMap: {
        enabled: d.shadowMap.enabled,
        type: d.shadowMap.type
      },
      toneMapping: d.toneMapping,
      toneMappingExposure: d.toneMappingExposure,
      outputColorSpace: d.outputColorSpace
    };
    return d.dispose(), y;
  })(), _ = toValue2(t.renderMode);
  return _ === "on-demand" && o(), _ === "manual" && setTimeout(() => {
    l();
  }, 100), watchEffect(() => {
    const d = toValue2(t.preset);
    d && (d in oe || f(`Renderer Preset must be one of these: ${Object.keys(oe).join(", ")}`), gt(a.value, oe[d])), fn(a.value, i.value, toValue2(t.dpr)), _ === "always" && (n.frames.value = Math.max(1, n.frames.value));
    const y = (P, C) => {
      const b = toValue2(P), E = () => {
        if (d)
          return qe(oe[d], C);
      };
      if (b !== void 0)
        return b;
      const S = E();
      return S !== void 0 ? S : qe(u, C);
    }, p = (P, C) => en(a.value, C, y(P, C));
    p(t.shadows, "shadowMap.enabled"), p(t.toneMapping ?? ACESFilmicToneMapping, "toneMapping"), p(t.shadowMapType, "shadowMap.type"), bn < 150 && p(!t.useLegacyLights, "physicallyCorrectLights"), p(t.outputColorSpace, "outputColorSpace"), p(t.toneMappingExposure, "toneMappingExposure");
    const h2 = y(t.clearColor, "clearColor");
    h2 && a.value.setClearColor(
      h2 ? Mn(h2) : new Color(0)
      // default clear color is not easily/efficiently retrievable from three
    );
  }), onUnmounted(() => {
    a.value.dispose(), a.value.forceContextLoss();
  }), {
    renderer: a
  };
}
function Cn(e, t, r = 10) {
  const n = toValue2(e) ? useWindowSize() : useElementSize(computed(() => toValue2(t).parentElement)), o = readonly(refDebounced(n.width, r)), l = readonly(refDebounced(n.height, r)), s = computed(() => o.value / l.value);
  return {
    height: l,
    width: o,
    aspectRatio: s
  };
}
var En = (e, t) => {
  const r = computed(() => t.renderer.value.domElement), n = shallowRef([]), { x: o, y: l } = usePointer({ target: r });
  let s = 0;
  const { width: a, height: c, top: i, left: f } = useElementBounding(r), v = ({ x: g, y: T }) => {
    if (r.value)
      return {
        x: (g - f.value) / a.value * 2 - 1,
        y: -((T - i.value) / c.value) * 2 + 1
      };
  }, u = ({ x: g, y: T }) => {
    if (t.camera.value)
      return t.raycaster.value.setFromCamera(new Vector2(g, T), t.camera.value), n.value = t.raycaster.value.intersectObjects(e.value, true), n.value;
  }, _ = (g) => {
    const T = v({
      x: (g == null ? void 0 : g.clientX) ?? o.value,
      y: (g == null ? void 0 : g.clientY) ?? l.value
    });
    return T ? u(T) || [] : [];
  }, d = createEventHook(), y = createEventHook(), p = createEventHook(), h2 = createEventHook(), P = createEventHook(), C = createEventHook(), b = createEventHook(), E = createEventHook();
  function S(g) {
    const T = {};
    for (const U in g)
      typeof U != "function" && (T[U] = g[U]);
    return T;
  }
  const M = (g, T) => {
    var Ie, $e, He;
    const U = S(T), re = new Vector3(T == null ? void 0 : T.clientX, T == null ? void 0 : T.clientY, 0).unproject((Ie = t.camera) == null ? void 0 : Ie.value);
    g.trigger({
      ...U,
      intersections: n.value,
      // The unprojectedPoint is wrong, math needs to be fixed
      unprojectedPoint: re,
      ray: ($e = t.raycaster) == null ? void 0 : $e.value.ray,
      camera: (He = t.camera) == null ? void 0 : He.value,
      sourceEvent: T,
      delta: s,
      stopPropagating: false
    });
  };
  let x;
  const m = (g) => {
    _(g), M(p, g), x = g;
  }, w = () => {
    x && m(x);
  };
  let k, L, R;
  const B = (g) => {
    var T;
    k = (T = n.value[0]) == null ? void 0 : T.object, s = 0, L = new Vector2(
      (g == null ? void 0 : g.clientX) ?? o.value,
      (g == null ? void 0 : g.clientY) ?? l.value
    ), M(P, g);
  };
  let O, G = false;
  const Oe = (g) => {
    var T, U, re;
    g instanceof PointerEvent && (n.value.length === 0 && M(C, g), k === ((T = n.value[0]) == null ? void 0 : T.object) && (R = new Vector2(
      (g == null ? void 0 : g.clientX) ?? o.value,
      (g == null ? void 0 : g.clientY) ?? l.value
    ), s = L == null ? void 0 : L.distanceTo(R), g.button === 0 ? (M(d, g), O === ((U = n.value[0]) == null ? void 0 : U.object) ? G = true : (O = (re = n.value[0]) == null ? void 0 : re.object, G = false)) : g.button === 2 && M(b, g)), M(h2, g));
  }, De = (g) => {
    G && (M(y, g), O = void 0, G = false);
  }, je = (g) => M(p, g), Be = (g) => M(E, g);
  return r.value.addEventListener("pointerup", Oe), r.value.addEventListener("pointerdown", B), r.value.addEventListener("pointermove", m), r.value.addEventListener("pointerleave", je), r.value.addEventListener("dblclick", De), r.value.addEventListener("wheel", Be), onUnmounted(() => {
    r != null && r.value && (r.value.removeEventListener("pointerup", Oe), r.value.removeEventListener("pointerdown", B), r.value.removeEventListener("pointermove", m), r.value.removeEventListener("pointerleave", je), r.value.removeEventListener("dblclick", De), r.value.removeEventListener("wheel", Be));
  }), {
    intersects: n,
    onClick: (g) => d.on(g).off,
    onDblClick: (g) => y.on(g).off,
    onContextMenu: (g) => b.on(g).off,
    onPointerMove: (g) => p.on(g).off,
    onPointerUp: (g) => h2.on(g).off,
    onPointerDown: (g) => P.on(g).off,
    onPointerMissed: (g) => C.on(g).off,
    onWheel: (g) => E.on(g).off,
    forceUpdate: w
  };
};
function ye(e, t) {
  if (Array.isArray(e))
    for (const r of e)
      r(t);
  typeof e == "function" && e(t);
}
function Tn(e, t, r) {
  var x;
  const n = shallowRef(), o = shallowRef();
  e && (n.value = e), t && (o.value = t);
  const l = (m) => {
    var w;
    return ((w = m.__tres) == null ? void 0 : w.eventCount) > 0;
  }, s = (m) => {
    var w;
    return ((w = m.children) == null ? void 0 : w.some((k) => s(k))) || l(m);
  }, a = shallowRef(((x = n.value) == null ? void 0 : x.children).filter(s) || []);
  function c(m, w) {
    const k = [], L = () => w.stopPropagating = true;
    w.stopPropagation = L;
    for (const R of w == null ? void 0 : w.intersections) {
      if (w.stopPropagating)
        return;
      w = { ...w, ...R };
      const { object: B } = R;
      w.eventObject = B, ye(B[m], w), k.push(B);
      let O = B.parent;
      for (; O !== null && !w.stopPropagating && !k.includes(O); )
        w.eventObject = O, ye(O[m], w), k.push(O), O = O.parent;
      const G = Xr(m.slice(2));
      r(G, { intersection: R, event: w });
    }
  }
  const {
    onClick: i,
    onDblClick: f,
    onContextMenu: v,
    onPointerMove: u,
    onPointerDown: _,
    onPointerUp: d,
    onPointerMissed: y,
    onWheel: p,
    forceUpdate: h2
  } = En(a, t);
  d((m) => c("onPointerUp", m)), _((m) => c("onPointerDown", m)), i((m) => c("onClick", m)), f((m) => c("onDoubleClick", m)), v((m) => c("onContextMenu", m)), p((m) => c("onWheel", m));
  let P = [];
  u((m) => {
    const w = m.intersections.map(({ object: L }) => L), k = m.intersections;
    P.forEach(({ object: L }) => {
      w.includes(L) || (m.intersections = P, c("onPointerLeave", m), c("onPointerOut", m));
    }), m.intersections = k, m.intersections.forEach(({ object: L }) => {
      P.includes(L) || (c("onPointerEnter", m), c("onPointerOver", m));
    }), c("onPointerMove", m), P = m.intersections;
  });
  const C = [];
  y((m) => {
    const w = () => m.stopPropagating = true;
    m.stopPropagation = w, C.forEach((k) => {
      m.stopPropagating || (m.eventObject = k, ye(k.onPointerMissed, m));
    }), r("pointer-missed", { event: m });
  });
  function b(m) {
    ne(m) && N(m) && a.value.push(m);
  }
  function E(m) {
    if (ne(m) && N(m)) {
      const w = a.value.indexOf(m);
      w > -1 && a.value.splice(w, 1);
    }
  }
  function S(m) {
    ne(m) && N(m) && m.onPointerMissed && C.push(m);
  }
  function M(m) {
    if (ne(m) && N(m)) {
      const w = C.indexOf(m);
      w > -1 && C.splice(w, 1);
    }
  }
  return t.eventManager = {
    forceUpdate: h2,
    registerObject: b,
    deregisterObject: E,
    registerPointerMissedObject: S,
    deregisterPointerMissedObject: M
  }, {
    forceUpdate: h2,
    registerObject: b,
    deregisterObject: E,
    registerPointerMissedObject: S,
    deregisterPointerMissedObject: M
  };
}
function Sn(e, t, r = 100) {
  r = r <= 0 ? 100 : r;
  const n = createEventHook(), o = /* @__PURE__ */ new Set();
  let l = false, s = false, a = null;
  function c() {
    a && clearTimeout(a), !s && !l && e() ? (n.trigger(t), o.forEach((u) => u()), o.clear(), l = true) : !s && !l && (a = setTimeout(c, r));
  }
  function i() {
    s = true, a && clearTimeout(a);
  }
  c();
  const f = (u, ..._) => {
    u(..._);
  };
  return {
    on: (u) => {
      if (l)
        return f(u, t), { off: () => {
        } };
      {
        const _ = n.on(u);
        return o.add(_.off), n.on(u);
      }
    },
    off: n.off,
    trigger: n.trigger,
    clear: n.clear,
    cancel: i
  };
}
var ee = /* @__PURE__ */ new WeakMap();
function bt(e) {
  if (e = e || pe(), ee.has(e))
    return ee.get(e);
  const t = 100, r = Date.now(), l = Sn(() => {
    if (Date.now() - r >= t)
      return true;
    {
      const s = e.renderer.value, a = (s == null ? void 0 : s.domElement) || { width: 0, height: 0 };
      return !!(s && a.width > 0 && a.height > 0);
    }
  }, e);
  return ee.set(e, l), l;
}
function oo(e) {
  const t = pe();
  if (t)
    return ee.has(t) ? ee.get(t).on(e) : bt(t).on(e);
}
function An({
  scene: e,
  canvas: t,
  windowSize: r,
  rendererOptions: n,
  emit: o
}) {
  const l = shallowRef(e), s = Cn(r, t), {
    camera: a,
    cameras: c,
    registerCamera: i,
    deregisterCamera: f,
    setCameraActive: v
  } = mn({ sizes: s }), u = {
    mode: ref(n.renderMode || "always"),
    priority: ref(0),
    frames: ref(0),
    maxFrames: 60,
    canBeInvalidated: computed(() => u.mode.value === "on-demand" && u.frames.value === 0)
  };
  function _(R = 1) {
    n.renderMode === "on-demand" && (u.frames.value = Math.min(u.maxFrames, u.frames.value + R));
  }
  function d() {
    n.renderMode === "manual" && (u.frames.value = 1);
  }
  const { renderer: y } = Pn(
    {
      canvas: t,
      options: n,
      // TODO: replace contextParts with full ctx at https://github.com/Tresjs/tres/issues/516
      contextParts: { sizes: s, render: u, invalidate: _, advance: d }
    }
  ), p = {
    sizes: s,
    scene: l,
    camera: a,
    cameras: readonly(c),
    renderer: y,
    raycaster: shallowRef(new Raycaster()),
    controls: ref(null),
    perf: {
      maxFrames: 160,
      fps: {
        value: 0,
        accumulator: []
      },
      memory: {
        currentMem: 0,
        allocatedMem: 0,
        accumulator: []
      }
    },
    render: u,
    advance: d,
    extend: Ee,
    invalidate: _,
    registerCamera: i,
    setCameraActive: v,
    deregisterCamera: f,
    loop: _n()
  };
  provide("useTres", p), p.scene.value.__tres = {
    root: p
  }, p.loop.register(() => {
    a.value && u.frames.value > 0 && (y.value.render(e, a.value), o("render", p.renderer.value)), u.priority.value = 0, u.mode.value === "always" ? u.frames.value = 1 : u.frames.value = Math.max(0, u.frames.value - 1);
  }, "render");
  const { on: h2, cancel: P } = bt(p);
  p.loop.setReady(false), p.loop.start(), h2(() => {
    o("ready", p), p.loop.setReady(true), Tn(e, p, o);
  }), onUnmounted(() => {
    P(), p.loop.stop();
  });
  const C = 100, b = useFps({ every: C }), { isSupported: E, memory: S } = useMemory({ interval: C }), M = 160;
  let x = performance.now();
  const m = ({ timestamp: R }) => {
    p.scene.value && (p.perf.memory.allocatedMem = Le(p.scene.value)), R - x >= C && (x = R, p.perf.fps.accumulator.push(b.value), p.perf.fps.accumulator.length > M && p.perf.fps.accumulator.shift(), p.perf.fps.value = b.value, E.value && S.value && (p.perf.memory.accumulator.push(S.value.usedJSHeapSize / 1024 / 1024), p.perf.memory.accumulator.length > M && p.perf.memory.accumulator.shift(), p.perf.memory.currentMem = p.perf.memory.accumulator.reduce((B, O) => B + O, 0) / p.perf.memory.accumulator.length));
  };
  let w = 0;
  const k = 1, { pause: L } = useRafFn(({ delta: R }) => {
    window.__TRES__DEVTOOLS__ && (m({ timestamp: performance.now() }), w += R, w >= k && (window.__TRES__DEVTOOLS__.cb(p), w = 0));
  }, { immediate: true });
  return onUnmounted(() => {
    L();
  }), p;
}
function pe() {
  const e = inject("useTres");
  if (!e)
    throw new Error("useTresContext must be used together with useTresContextProvider");
  return e;
}
var so = pe;
function io() {
  const {
    camera: e,
    scene: t,
    renderer: r,
    loop: n,
    raycaster: o,
    controls: l,
    invalidate: s,
    advance: a
  } = pe();
  n.setContext({
    camera: e,
    scene: t,
    renderer: r,
    raycaster: o,
    controls: l,
    invalidate: s,
    advance: a
  });
  function c(v, u = 0) {
    return n.register(v, "before", u);
  }
  function i(v) {
    return n.register(v, "render");
  }
  function f(v, u = 0) {
    return n.register(v, "after", u);
  }
  return {
    pause: n.pause,
    resume: n.resume,
    pauseRender: n.pauseRender,
    resumeRender: n.resumeRender,
    isActive: n.isActive,
    onBeforeRender: c,
    render: i,
    onAfterRender: f
  };
}
var Mt = createEventHook();
var Pt = createEventHook();
var Re = createEventHook();
var te = new Clock();
var le = 0;
var ce = 0;
var { pause: xn, resume: Xe, isActive: kn } = useRafFn(
  () => {
    Mt.trigger({ delta: le, elapsed: ce, clock: te }), Pt.trigger({ delta: le, elapsed: ce, clock: te }), Re.trigger({ delta: le, elapsed: ce, clock: te });
  },
  { immediate: false }
);
Re.on(() => {
  le = te.getDelta(), ce = te.getElapsedTime();
});
var Ze = false;
var ao = () => (Ze || (Ze = true, Xe()), {
  onBeforeLoop: Mt.on,
  onLoop: Pt.on,
  onAfterLoop: Re.on,
  pause: xn,
  resume: Xe,
  isActive: kn
});
function lo() {
  const { logWarning: e } = Q();
  function t(l, s, a) {
    let c = null;
    return l.traverse((i) => {
      i[s] === a && (c = i);
    }), c || e(`Child with ${s} '${a}' not found.`), c;
  }
  function r(l, s, a) {
    const c = [];
    return l.traverse((i) => {
      i[s].includes(a) && c.push(i);
    }), c.length || e(`Children with ${s} '${a}' not found.`), c;
  }
  function n(l, s) {
    return t(l, "name", s);
  }
  function o(l, s) {
    return r(l, "name", s);
  }
  return {
    seek: t,
    seekByName: n,
    seekAll: r,
    seekAllByName: o
  };
}
function Ln(e, t = {}, r = {}) {
  let n = e;
  const o = (a) => {
    n = a;
  };
  let l = new Proxy({}, {});
  const s = {
    has(a, c) {
      return c in t || c in n;
    },
    get(a, c, i) {
      return c in t ? t[c](n) : n[c];
    },
    set(a, c, i) {
      return r[c] ? r[c](i, n, l, o) : n[c] = i, true;
    }
  };
  return l = new Proxy({}, s), l;
}
var { logError: et } = Q();
var tt = [
  "onClick",
  "onContextMenu",
  "onPointerMove",
  "onPointerEnter",
  "onPointerLeave",
  "onPointerOver",
  "onPointerOut",
  "onDoubleClick",
  "onPointerDown",
  "onPointerUp",
  "onPointerCancel",
  "onPointerMissed",
  "onLostPointerCapture",
  "onWheel"
];
var Rn = (e) => {
  const t = e.scene.value;
  function r(i, f, v, u) {
    if (u || (u = {}), u.args || (u.args = []), i === "template" || Jr(i))
      return null;
    let _ = i.replace("Tres", ""), d;
    if (i === "primitive") {
      (!j(u.object) || isRef(u.object)) && et(
        "Tres primitives need an 'object' prop, whose value is an object or shallowRef<object>"
      ), _ = u.object.type;
      const y = {};
      d = Ln(
        u.object,
        {
          object: (h2) => h2,
          isPrimitive: () => true,
          __tres: () => y
        },
        {
          object: (h2, P, C, b) => {
            pn(h2, C, b, { patchProp: l, remove: o, insert: n }, e);
          },
          __tres: (h2) => {
            Object.assign(y, h2);
          }
        }
      );
    } else {
      const y = Ce.value[_];
      y || et(
        `${_} is not defined on the THREE namespace. Use extend to add it to the catalog.`
      ), d = new y(...u.args);
    }
    return d ? (d.isCamera && (u != null && u.position || d.position.set(3, 3, 3), u != null && u.lookAt || d.lookAt(0, 0, 0)), d = z(d, {
      ...d.__tres,
      type: _,
      memoizedProps: u,
      eventCount: 0,
      primitive: i === "primitive",
      attach: u.attach
    }, e), d) : null;
  }
  function n(i, f) {
    var _, d, y;
    if (!i)
      return;
    f = f || t;
    const v = i.__tres ? i : z(i, {}, e), u = f.__tres ? f : z(f, {}, e);
    i = J(v), f = J(u), i.__tres && ((_ = i.__tres) == null ? void 0 : _.eventCount) > 0 && ((d = e.eventManager) == null || d.registerObject(i)), e.registerCamera(i), (y = e.eventManager) == null || y.registerPointerMissedObject(i), v.__tres.attach ? cn(u, v, v.__tres.attach) : N(i) && N(u) && (u.add(i), i.dispatchEvent({ type: "added" })), v.__tres.parent = u, u.__tres.objects && !u.__tres.objects.includes(v) && u.__tres.objects.push(v);
  }
  function o(i, f) {
    var d, y, p, h2;
    if (!i)
      return;
    i != null && i.__tres && ((d = i.__tres) == null ? void 0 : d.eventCount) > 0 && ((y = e.eventManager) == null || y.deregisterObject(i)), f = be(f) ? "default" : f;
    const v = (p = i.__tres) == null ? void 0 : p.dispose;
    be(v) || (v === null ? f = false : f = v);
    const u = (h2 = i.__tres) == null ? void 0 : h2.primitive, _ = f === "default" ? !u : !!f;
    if (i.__tres && "objects" in i.__tres && [...i.__tres.objects].forEach((P) => o(P, f)), _ && i.children && [...i.children].forEach((P) => o(P, f)), _t(i, e), wt(i, e), _ && !Yr(i)) {
      if (Y(f))
        f(i);
      else if (Y(i.dispose))
        try {
          i.dispose();
        } catch {
        }
    }
    "__tres" in i && delete i.__tres;
  }
  function l(i, f, v, u) {
    var P, C;
    if (!i)
      return;
    let _ = i, d = f;
    if (i.__tres && (i.__tres.memoizedProps[f] = u), f === "attach") {
      const b = ((P = i.__tres) == null ? void 0 : P.parent) || i.parent;
      o(i), z(i, { attach: u }, e), b && n(i, b);
      return;
    }
    if (f === "dispose") {
      i.__tres || (i = z(i, {}, e)), i.__tres.dispose = u;
      return;
    }
    if (N(i) && d === "blocks-pointer-events") {
      u || u === "" ? i[d] = u : delete i[d];
      return;
    }
    tt.includes(f) && i.__tres && (i.__tres.eventCount += 1);
    let y = he(d), p = _ == null ? void 0 : _[y];
    if (d === "args") {
      const b = i, E = v ?? [], S = u ?? [], M = ((C = i.__tres) == null ? void 0 : C.type) || i.type;
      M && E.length && !tn(E, S) && (_ = Object.assign(
        b,
        new Ce.value[M](...u)
      ));
      return;
    }
    if (_.type === "BufferGeometry") {
      if (d === "args")
        return;
      _.setAttribute(
        he(d),
        new BufferAttribute(...u)
      );
      return;
    }
    if (d.includes("-") && p === void 0) {
      p = _;
      for (const b of d.split("-"))
        y = d = he(b), _ = p, p = p == null ? void 0 : p[d];
    }
    let h2 = u;
    if (h2 === "" && (h2 = true), Y(p)) {
      tt.includes(f) || (ke(h2) ? i[y](...h2) : i[y](h2)), y.startsWith("on") && Y(h2) && (_[y] = h2);
      return;
    }
    Ge(p) && Ge(h2) ? p.mask = h2.mask : mt(p) && Ur(h2) ? p.set(h2) : Fr(p) && Wr(h2) && p.constructor === h2.constructor ? p.copy(h2) : Me(p) && Array.isArray(h2) ? "fromArray" in p && typeof p.fromArray == "function" ? p.fromArray(h2) : p.set(...h2) : Me(p) && typeof h2 == "number" ? "setScalar" in p && typeof p.setScalar == "function" ? p.setScalar(h2) : p.set(h2) : _[y] = h2, yt(i);
  }
  function s(i) {
    var f;
    return ((f = i == null ? void 0 : i.__tres) == null ? void 0 : f.parent) || null;
  }
  function a(i) {
    const f = z(new Object3D(), { type: "Comment" }, e);
    return f.name = i, f;
  }
  function c(i) {
    var _;
    const f = s(i), v = ((_ = f == null ? void 0 : f.__tres) == null ? void 0 : _.objects) || [], u = v.indexOf(i);
    return u < 0 || u >= v.length - 1 ? null : v[u + 1];
  }
  return {
    insert: n,
    remove: o,
    createElement: r,
    patchProp: l,
    parentNode: s,
    createText: () => void 0,
    createComment: a,
    setText: () => void 0,
    setElementText: () => void 0,
    nextSibling: c,
    querySelector: () => void 0,
    setScopeId: () => void 0,
    cloneNode: () => void 0,
    insertStaticContent: () => void 0
  };
};
function On() {
  return Ct().__VUE_DEVTOOLS_GLOBAL_HOOK__;
}
function Ct() {
  return typeof navigator < "u" && typeof window < "u" ? window : typeof globalThis < "u" ? globalThis : {};
}
var Dn = typeof Proxy == "function";
var jn = "devtools-plugin:setup";
var Bn = "plugin:settings:set";
var V;
var Te;
function In() {
  var e;
  return V !== void 0 || (typeof window < "u" && window.performance ? (V = true, Te = window.performance) : typeof globalThis < "u" && (!((e = globalThis.perf_hooks) === null || e === void 0) && e.performance) ? (V = true, Te = globalThis.perf_hooks.performance) : V = false), V;
}
function $n() {
  return In() ? Te.now() : Date.now();
}
var Hn = class {
  constructor(t, r) {
    this.target = null, this.targetQueue = [], this.onQueue = [], this.plugin = t, this.hook = r;
    const n = {};
    if (t.settings)
      for (const s in t.settings) {
        const a = t.settings[s];
        n[s] = a.defaultValue;
      }
    const o = `__vue-devtools-plugin-settings__${t.id}`;
    let l = Object.assign({}, n);
    try {
      const s = localStorage.getItem(o), a = JSON.parse(s);
      Object.assign(l, a);
    } catch {
    }
    this.fallbacks = {
      getSettings() {
        return l;
      },
      setSettings(s) {
        try {
          localStorage.setItem(o, JSON.stringify(s));
        } catch {
        }
        l = s;
      },
      now() {
        return $n();
      }
    }, r && r.on(Bn, (s, a) => {
      s === this.plugin.id && this.fallbacks.setSettings(a);
    }), this.proxiedOn = new Proxy({}, {
      get: (s, a) => this.target ? this.target.on[a] : (...c) => {
        this.onQueue.push({
          method: a,
          args: c
        });
      }
    }), this.proxiedTarget = new Proxy({}, {
      get: (s, a) => this.target ? this.target[a] : a === "on" ? this.proxiedOn : Object.keys(this.fallbacks).includes(a) ? (...c) => (this.targetQueue.push({
        method: a,
        args: c,
        resolve: () => {
        }
      }), this.fallbacks[a](...c)) : (...c) => new Promise((i) => {
        this.targetQueue.push({
          method: a,
          args: c,
          resolve: i
        });
      })
    });
  }
  async setRealTarget(t) {
    this.target = t;
    for (const r of this.onQueue)
      this.target.on[r.method](...r.args);
    for (const r of this.targetQueue)
      r.resolve(await this.target[r.method](...r.args));
  }
};
function Un(e, t) {
  const r = e, n = Ct(), o = On(), l = Dn && r.enableEarlyProxy;
  if (o && (n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !l))
    o.emit(jn, e, t);
  else {
    const s = l ? new Hn(r, o) : null;
    (n.__VUE_DEVTOOLS_PLUGINS__ = n.__VUE_DEVTOOLS_PLUGINS__ || []).push({
      pluginDescriptor: r,
      setupFn: t,
      proxy: s
    }), s && t(s.proxiedTarget);
  }
}
function Fn(e, t) {
  const r = `▲ ■ ●${e}`;
  typeof rt == "function" ? rt(r, t) : console.log(r);
}
function rt(e, t) {
  throw new Error(e + t);
}
var Et = (e) => {
  const t = {
    id: e.uuid,
    label: e.type,
    children: [],
    tags: []
  };
  e.name !== "" && t.tags.push({
    label: e.name,
    textColor: 5750629,
    backgroundColor: 15793395
  });
  const r = Le(e);
  return r > 0 && t.tags.push({
    label: `${wn(r)} KB`,
    textColor: 15707189,
    backgroundColor: 16775644,
    tooltip: "Memory usage"
  }), e.type.includes("Light") && (Gr(e) && t.tags.push({
    label: `${e.intensity}`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Intensity"
  }), t.tags.push({
    label: `#${new Color(e.color).getHexString()}`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Color"
  })), e.type.includes("Camera") && (t.tags.push({
    label: `${e.fov}°`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Field of view"
  }), t.tags.push({
    label: `x: ${Math.round(e.position.x)} y: ${Math.round(e.position.y)} z: ${Math.round(e.position.z)}`,
    textColor: 9738662,
    backgroundColor: 16316922,
    tooltip: "Position"
  })), t;
};
function Tt(e, t, r = "") {
  e.children.forEach((n) => {
    if (n.type === "HightlightMesh" || r && !n.type.includes(r) && !n.name.includes(r))
      return;
    const o = Et(n);
    t.children.push(o), Tt(n, o, r);
  });
}
var Wn = [];
var X = "tres:inspector";
var Nn = reactive({
  sceneGraph: null
});
function zn(e, t) {
  Un(
    {
      id: "dev.esm.tres",
      label: "TresJS 🪐",
      logo: "https://raw.githubusercontent.com/Tresjs/tres/main/public/favicon.svg",
      packageName: "tresjs",
      homepage: "https://tresjs.org",
      componentStateTypes: Wn,
      app: e
    },
    (r) => {
      typeof r.now != "function" && Fn(
        "You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."
      ), r.addInspector({
        id: X,
        label: "TresJS 🪐",
        icon: "account_tree",
        treeFilterPlaceholder: "Search instances"
      }), setInterval(() => {
        r.sendInspectorTree(X);
      }, 1e3), setInterval(() => {
        r.notifyComponentUpdate();
      }, 5e3), r.on.getInspectorTree((l) => {
        if (l.inspectorId === X) {
          const s = Et(t.scene.value);
          Tt(t.scene.value, s, l.filter), Nn.sceneGraph = s, l.rootNodes = [s];
        }
      });
      let n = null, o = null;
      r.on.getInspectorState((l) => {
        var s;
        if (l.inspectorId === X) {
          const [a] = t.scene.value.getObjectsByProperty("uuid", l.nodeId);
          if (!a)
            return;
          if (o && n && n.parent && o.remove(n), a.isMesh) {
            const c = on(a);
            a.add(c), n = c, o = a;
          }
          l.state = {
            object: Object.entries(a).map(([c, i]) => c === "children" ? { key: c, value: i.filter((f) => f.type !== "HightlightMesh") } : { key: c, value: i, editable: true }).filter(({ key: c }) => c !== "parent")
          }, a.isScene && (l.state = {
            ...l.state,
            state: [
              {
                key: "Scene Info",
                value: {
                  objects: a.children.length,
                  memory: Le(a),
                  calls: t.renderer.value.info.render.calls,
                  triangles: t.renderer.value.info.render.triangles,
                  points: t.renderer.value.info.render.points,
                  lines: t.renderer.value.info.render.lines
                }
              },
              {
                key: "Programs",
                value: ((s = t.renderer.value.info.programs) == null ? void 0 : s.map((c) => ({
                  ...c,
                  programName: c.name
                }))) || []
              }
            ]
          });
        }
      }), r.on.editInspectorState((l) => {
        l.inspectorId === X && nn(t.scene.value, l.nodeId, l.path, l.state.value);
      });
    }
  );
}
var Gn = ["data-scene", "data-tres"];
var Vn = defineComponent({
  __name: "TresCanvas",
  props: {
    shadows: { type: Boolean, default: void 0 },
    clearColor: {},
    toneMapping: {},
    shadowMapType: {},
    useLegacyLights: { type: Boolean, default: void 0 },
    outputColorSpace: {},
    toneMappingExposure: {},
    renderMode: { default: "always" },
    dpr: {},
    camera: {},
    preset: {},
    windowSize: { type: Boolean, default: void 0 },
    enableProvideBridge: { type: Boolean, default: true },
    context: {},
    alpha: { type: Boolean, default: void 0 },
    premultipliedAlpha: { type: Boolean },
    antialias: { type: Boolean, default: void 0 },
    stencil: { type: Boolean, default: void 0 },
    preserveDrawingBuffer: { type: Boolean, default: void 0 },
    powerPreference: {},
    depth: { type: Boolean, default: void 0 },
    failIfMajorPerformanceCaveat: { type: Boolean, default: void 0 },
    precision: {},
    logarithmicDepthBuffer: { type: Boolean, default: void 0 },
    reverseDepthBuffer: { type: Boolean }
  },
  emits: [
    "render",
    "click",
    "double-click",
    "context-menu",
    "pointer-move",
    "pointer-up",
    "pointer-down",
    "pointer-enter",
    "pointer-leave",
    "pointer-over",
    "pointer-out",
    "pointer-missed",
    "wheel",
    "ready"
  ],
  setup(e, { expose: t, emit: r }) {
    const n = e, o = r, l = useSlots(), s = ref(), a = shallowRef(new Scene()), c = getCurrentInstance();
    Ee(three_module_exports);
    const i = (d, y = false) => defineComponent({
      setup() {
        var C;
        const p = (C = getCurrentInstance()) == null ? void 0 : C.appContext;
        p && (p.app = c == null ? void 0 : c.appContext.app);
        const h2 = {};
        function P(b) {
          b && (b.parent && P(b.parent), b.provides && Object.assign(h2, b.provides));
        }
        return c != null && c.parent && n.enableProvideBridge && (P(c.parent), Reflect.ownKeys(h2).forEach((b) => {
          provide(b, h2[b]);
        })), provide("useTres", d), provide("extend", Ee), typeof window < "u" && zn(p == null ? void 0 : p.app, d), () => h(Fragment, null, y ? [] : l.default());
      }
    }), f = (d, y = false) => {
      const p = i(d, y), { render: h2 } = createRenderer(Rn(d));
      h2(h(p), a.value);
    }, v = (d, y = false) => {
      vt(d.scene.value), y && (d.renderer.value.dispose(), d.renderer.value.renderLists.dispose(), d.renderer.value.forceContextLoss()), a.value.__tres = {
        root: d
      };
    }, u = shallowRef(null);
    t({ context: u, dispose: () => v(u.value, true) });
    const _ = () => {
      v(u.value), f(u.value, true);
    };
    return onMounted(() => {
      const d = s;
      u.value = An({
        scene: a.value,
        canvas: d,
        windowSize: n.windowSize ?? false,
        rendererOptions: n,
        emit: o
      });
      const { registerCamera: y, camera: p, cameras: h2, deregisterCamera: P } = u.value;
      f(u.value);
      const C = () => {
        const b = new PerspectiveCamera(
          45,
          window.innerWidth / window.innerHeight,
          0.1,
          1e3
        );
        b.position.set(3, 3, 3), b.lookAt(0, 0, 0), y(b);
        const E = watchEffect(() => {
          h2.value.length >= 2 && (b.removeFromParent(), P(b), E == null || E());
        });
      };
      watch(
        () => n.camera,
        (b, E) => {
          b && y(b), E && (E.removeFromParent(), P(E));
        },
        {
          immediate: true
        }
      ), p.value || C();
    }), onUnmounted(_), (d, y) => (openBlock(), createElementBlock("canvas", {
      ref_key: "canvas",
      ref: s,
      "data-scene": a.value.uuid,
      class: normalizeClass(d.$attrs.class),
      "data-tres": `tresjs ${unref(jr).version}`,
      style: normalizeStyle({
        display: "block",
        width: "100%",
        height: "100%",
        position: d.windowSize ? "fixed" : "relative",
        top: 0,
        left: 0,
        pointerEvents: "auto",
        touchAction: "none",
        ...d.$attrs.style
      })
    }, null, 14, Gn));
  }
});
var Yn = [
  "TresCanvas",
  "TresLeches",
  "TresScene"
];
var co = {
  template: {
    compilerOptions: {
      isCustomElement: (e) => e.startsWith("Tres") && !Yn.includes(e) || e === "primitive"
    }
  }
};
var { logWarning: qn } = Q();
var I = null;
var uo = {
  updated: (e, t) => {
    var o;
    const r = sn(t);
    if (!r) {
      qn(`v-distance-to: problem with binding value: ${t.value}`);
      return;
    }
    I && (I.dispose(), e.parent.remove(I));
    const n = r.clone().sub(e.position);
    n.normalize(), I = new ArrowHelper(n, e.position, e.position.distanceTo(r), 16776960), e.parent.add(I), console.table(
      [
        ["Distance:", e.position.distanceTo(r)],
        [`origin: ${e.name || e.type}`, `x:${e.position.x}, y:${e.position.y}, z:${(o = e.position) == null ? void 0 : o.z}`],
        [`Destiny: ${e.name || e.type}`, `x:${r.x}, y:${r.y}, z:${r == null ? void 0 : r.z}`]
      ]
    );
  },
  unmounted: (e) => {
    I == null || I.dispose(), e.parent && e.parent.remove(I);
  }
};
var St = class extends Line {
  constructor(t, r) {
    const n = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 1, 1, 0], o = new BufferGeometry();
    o.setAttribute("position", new Float32BufferAttribute(n, 3)), o.computeBoundingSphere();
    const l = new LineBasicMaterial({ fog: false });
    super(o, l), this.light = t, this.color = r, this.type = "RectAreaLightHelper";
    const s = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0], a = new BufferGeometry();
    a.setAttribute("position", new Float32BufferAttribute(s, 3)), a.computeBoundingSphere(), this.add(new Mesh(a, new MeshBasicMaterial({ side: BackSide, fog: false })));
  }
  updateMatrixWorld() {
    if (this.scale.set(0.5 * this.light.width, 0.5 * this.light.height, 1), this.color !== void 0)
      this.material.color.set(this.color), this.children[0].material.color.set(this.color);
    else {
      this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity);
      const t = this.material.color, r = Math.max(t.r, t.g, t.b);
      r > 1 && t.multiplyScalar(1 / r), this.children[0].material.color.copy(this.material.color);
    }
    this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld), this.children[0].matrixWorld.copy(this.matrixWorld);
  }
  dispose() {
    this.geometry.dispose(), this.material.dispose(), this.children[0].geometry.dispose(), this.children[0].material.dispose();
  }
};
var { logWarning: nt } = Q();
var se;
var F;
var Kn = {
  DirectionalLight: DirectionalLightHelper,
  PointLight: PointLightHelper,
  SpotLight: SpotLightHelper,
  HemisphereLight: HemisphereLightHelper,
  RectAreaLight: St
};
var fo = {
  mounted: (e) => {
    if (!e.isLight) {
      nt(`${e.type} is not a light`);
      return;
    }
    se = Kn[e.type], e.parent.add(new se(e, 1, e.color.getHex()));
  },
  updated: (e) => {
    F = e.parent.children.find((t) => t instanceof se), !(F instanceof St) && F.update();
  },
  unmounted: (e) => {
    if (!e.isLight) {
      nt(`${e.type} is not a light`);
      return;
    }
    F = e.parent.children.find((t) => t instanceof se), F && F.dispose && F.dispose(), e.parent && e.parent.remove(F);
  }
};
var po = {
  mounted: (e, t) => {
    if (t.arg) {
      console.log(`v-log:${t.arg}`, e[t.arg]);
      return;
    }
    console.log("v-log", e);
  }
};
var mo = {
  install(e) {
    e.component("TresCanvas", Vn);
  }
};
export {
  Vn as TresCanvas,
  eo as UseLoader,
  to as UseTexture,
  Ce as catalogue,
  _n as createRenderLoop,
  mo as default,
  vt as dispose,
  Ee as extend,
  ro as isProd,
  Mn as normalizeColor,
  no as normalizeVectorFlexibleParam,
  oo as onTresReady,
  co as templateCompilerOptions,
  Br as traverseObjects,
  mn as useCamera,
  Ir as useLoader,
  Q as useLogger,
  io as useLoop,
  En as useRaycaster,
  ao as useRenderLoop,
  Pn as useRenderer,
  lo as useSeek,
  dn as useTexture,
  so as useTres,
  pe as useTresContext,
  An as useTresContextProvider,
  Tn as useTresEventManager,
  uo as vDistanceTo,
  fo as vLightHelper,
  po as vLog
};
//# sourceMappingURL=@tresjs_core.js.map
