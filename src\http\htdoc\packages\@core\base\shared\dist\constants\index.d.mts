declare const CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT = "--vben-content-height";
declare const CSS_VARIABLE_LAYOUT_CONTENT_WIDTH = "--vben-content-width";
declare const CSS_VARIABLE_LAYOUT_HEADER_HEIGHT = "--vben-header-height";
declare const CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT = "--vben-footer-height";
declare const ELEMENT_ID_MAIN_CONTENT = "__vben_main_content";
declare const DEFAULT_NAMESPACE = "vben";

declare const VBEN_GITHUB_URL = "https://github.com/vbenjs/vue-vben-admin";
declare const VBEN_DOC_URL = "https://doc.vben.pro";
declare const VBEN_LOGO_URL = "https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp";
declare const VBEN_PREVIEW_URL = "https://www.vben.pro";
declare const VBEN_ELE_PREVIEW_URL = "https://ele.vben.pro";
declare const VBEN_NAIVE_PREVIEW_URL = "https://naive.vben.pro";
declare const VBEN_ANT_PREVIEW_URL = "https://ant.vben.pro";

export { CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT, CSS_VARIABLE_LAYOUT_CONTENT_WIDTH, CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT, CSS_VARIABLE_LAYOUT_HEADER_HEIGHT, DEFAULT_NAMESPACE, ELEMENT_ID_MAIN_CONTENT, VBEN_ANT_PREVIEW_URL, VBEN_DOC_URL, VBEN_ELE_PREVIEW_URL, VBEN_GITHUB_URL, VBEN_LOGO_URL, VBEN_NAIVE_PREVIEW_URL, VBEN_PREVIEW_URL };
