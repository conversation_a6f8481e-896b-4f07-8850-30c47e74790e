var Sl=Object.defineProperty,Tl=Object.defineProperties;var Ml=Object.getOwnPropertyDescriptors;var dt=Object.getOwnPropertySymbols;var ra=Object.prototype.hasOwnProperty,ia=Object.prototype.propertyIsEnumerable;var kt=(l,t,a)=>t in l?Sl(l,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):l[t]=a,Q=(l,t)=>{for(var a in t||(t={}))ra.call(t,a)&&kt(l,a,t[a]);if(dt)for(var a of dt(t))ia.call(t,a)&&kt(l,a,t[a]);return l},ke=(l,t)=>Tl(l,Ml(t));var xe=(l,t)=>{var a={};for(var o in l)ra.call(l,o)&&t.indexOf(o)<0&&(a[o]=l[o]);if(l!=null&&dt)for(var o of dt(l))t.indexOf(o)<0&&ia.call(l,o)&&(a[o]=l[o]);return a};var lt=(l,t,a)=>kt(l,typeof t!="symbol"?t+"":t,a);var G=(l,t,a)=>new Promise((o,s)=>{var n=d=>{try{r(a.next(d))}catch(f){s(f)}},i=d=>{try{r(a.throw(d))}catch(f){s(f)}},r=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,i);r((a=a.apply(l,t)).next())});import{c9 as Bl,a as _e,az as Ma,ca as _l,cb as $l,cc as Vl,bz as El,cd as Vt,ce as Pl,ar as Ie,cf as Il,aw as Qe,cg as Ll,ch as zl,ci as Al,cj as Ul,ck as Ol,cl as Hl,cm as Dl,cn as Wl,co as Nl,cp as Rl,cq as Fl,cr as Kl,cs as Gl,aC as jl,aF as ql,aD as Jl,aE as Xl,a9 as rt,aG as Yl,aH as Zl,aI as Ql,ct as eo,cu as to,cv as ao,cw as lo,cx as oo,_ as Me,D as Ze,cy as no,cz as Oe,bh as Pe,cA as pt,cB as so,cC as ro,cD as io,cE as uo,cF as co,cG as po,cH as fo,cI as bo,C as et,cJ as Re,aJ as Ba,aK as mo,aL as _a,aM as ho,aN as $a,aQ as Mt,aO as da,aP as vo,$ as g,be as qe,bf as go,bB as yo,bg as Va,cK as wo,bw as xo,cL as ko,cM as Co,cN as So,cO as To,cP as Mo,cQ as Ea,I as Le,cR as Bo,cS as _o,cT as je,cU as $o,cV as ft,cW as Vo,cX as Eo,cY as Po,cZ as Io}from"./bootstrap-MyT3sENS.js";import{aJ as Lo,aK as zo,aL as Pa,ag as U,ac as Ao,d as M,j as x,o as u,u as e,D as L,a5 as re,s as c,n as _,a3 as Ve,a4 as Ue,L as pe,e as S,v as b,c as w,l as I,q as V,i as bt,r as Z,Y as he,x as E,ad as ge,aM as Uo,ae as y,w as ye,F as ee,B as be,a9 as Ia,P as Bt,aN as Et,ao as Oo,y as Je,g as A,m as $e,ai as Ke,a7 as Ho,I as mt,a8 as Do,$ as ht,N as Wo,h as ua,H as Ge,G as Pt,X as He,f as Xe,aO as ca,aP as No,aQ as Ro,aR as Fo,aS as Ko,aT as pa,aU as Go,aV as fa,aW as La,aX as jo,K as za,aY as qo,aZ as Jo,aj as ut,z as ze,af as ba,A as Xo,a_ as Yo,a2 as ma,J as vt,a0 as Aa,aG as _t,a$ as Zo,t as Qo,b0 as ha,b1 as en,b2 as Ua,b3 as tn,Q as an,b4 as ln,aF as va}from"../jse/index-index-Y3_OtjO-.js";import{u as it}from"./use-preferences-Bw3ujLIE.js";import{_ as Oa,a as Ha,b as Da,d as Wa,c as on,f as Na,g as Ra,S as nn,M as sn,e as rn,h as dn}from"./copyright.vue_vue_type_script_setup_true_lang-BZYNGdt2.js";import{X as st,u as un}from"./use-modal-O6aMVd6f.js";import{M as cn,g as pn,a as It,_ as fn,S as bn,u as mn,h as tt,b as hn,d as vn,c as gn,f as yn,e as wn}from"./index-DGcxnQ4T.js";import{R as Lt}from"./rotate-cw-yWIR2jcF.js";function zt(l,t){for(const a of l){if(a.path===t)return a;const o=a.children&&zt(a.children,t);if(o)return o}return null}function nt(l,t,a=0){var i;const o=zt(l,t),s=(i=o==null?void 0:o.parents)==null?void 0:i[a],n=s?l.find(r=>r.path===s):void 0;return{findMenu:o,rootMenu:n,rootMenuPath:s}}const at=Bl("core-tabbar",{actions:{_bulkCloseByPaths(l){return G(this,null,function*(){this.tabs=this.tabs.filter(t=>!l.includes(me(t))),this.updateCacheTabs()})},_close(l){const{fullPath:t}=l;if(Ne(l))return;const a=this.tabs.findIndex(o=>o.fullPath===t);a!==-1&&this.tabs.splice(a,1)},_goToDefaultTab(l){return G(this,null,function*(){if(this.getTabs.length<=0)return;const t=this.getTabs[0];t&&(yield this._goToTab(t,l))})},_goToTab(l,t){return G(this,null,function*(){const{params:a,path:o,query:s}=l,n={params:a||{},path:o,query:s||{}};yield t.replace(n)})},addTab(l){var o,s;const t=xn(l);if(!kn(t))return;const a=this.tabs.findIndex(n=>me(n)===me(l));if(a===-1){const n=U.tabbar.maxCount,i=(s=(o=l==null?void 0:l.meta)==null?void 0:o.maxNumOfOpenTab)!=null?s:-1;if(i>0&&this.tabs.filter(r=>r.name===l.name).length>=i){const r=this.tabs.findIndex(d=>d.name===l.name);r!==-1&&this.tabs.splice(r,1)}else if(n>0&&this.tabs.length>=n){const r=this.tabs.findIndex(d=>!Reflect.has(d.meta,"affixTab")||!d.meta.affixTab);r!==-1&&this.tabs.splice(r,1)}this.tabs.push(t)}else{const n=Ao(this.tabs)[a],i=ke(Q(Q({},n),t),{meta:Q(Q({},n==null?void 0:n.meta),t.meta)});if(n){const r=n.meta;Reflect.has(r,"affixTab")&&(i.meta.affixTab=r.affixTab),Reflect.has(r,"newTabTitle")&&(i.meta.newTabTitle=r.newTabTitle)}this.tabs.splice(a,1,i)}this.updateCacheTabs()},closeAllTabs(l){return G(this,null,function*(){const t=this.tabs.filter(a=>Ne(a));this.tabs=t.length>0?t:[...this.tabs].splice(0,1),yield this._goToDefaultTab(l),this.updateCacheTabs()})},closeLeftTabs(l){return G(this,null,function*(){const t=this.tabs.findIndex(s=>me(s)===me(l));if(t<1)return;const a=this.tabs.slice(0,t),o=[];for(const s of a)Ne(s)||o.push(me(s));yield this._bulkCloseByPaths(o)})},closeOtherTabs(l){return G(this,null,function*(){const t=this.tabs.map(o=>me(o)),a=[];for(const o of t)if(o!==l.fullPath){const s=this.tabs.find(n=>me(n)===o);if(!s)continue;Ne(s)||a.push(me(s))}yield this._bulkCloseByPaths(a)})},closeRightTabs(l){return G(this,null,function*(){const t=this.tabs.findIndex(a=>me(a)===me(l));if(t!==-1&&t<this.tabs.length-1){const a=this.tabs.slice(t+1),o=[];for(const s of a)Ne(s)||o.push(me(s));yield this._bulkCloseByPaths(o)}})},closeTab(l,t){return G(this,null,function*(){const{currentRoute:a}=t;if(me(a.value)!==me(l)){this._close(l),this.updateCacheTabs();return}const o=this.getTabs.findIndex(i=>me(i)===me(a.value)),s=this.getTabs[o-1],n=this.getTabs[o+1];n?(this._close(l),yield this._goToTab(n,t)):s?(this._close(l),yield this._goToTab(s,t)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(l,t){return G(this,null,function*(){const a=decodeURIComponent(l),o=this.tabs.findIndex(n=>me(n)===a);if(o===-1)return;const s=this.tabs[o];s&&(yield this.closeTab(s,t))})},getTabByPath(l){return this.getTabs.find(t=>me(t)===l)},openTabInNewWindow(l){return G(this,null,function*(){Pa(l.fullPath||l.path)})},pinTab(l){return G(this,null,function*(){var s;const t=this.tabs.findIndex(n=>me(n)===me(l));if(t!==-1){const n=this.tabs[t];l.meta.affixTab=!0,l.meta.title=(s=n==null?void 0:n.meta)==null?void 0:s.title,this.tabs.splice(t,1,l)}const o=this.tabs.filter(n=>Ne(n)).findIndex(n=>me(n)===me(l));yield this.sortTabs(t,o)})},refresh(l){return G(this,null,function*(){const{currentRoute:t}=l,{name:a}=t.value;this.excludeCachedTabs.add(a),this.renderRouteView=!1,Lo(),yield new Promise(o=>setTimeout(o,200)),this.excludeCachedTabs.delete(a),this.renderRouteView=!0,zo()})},resetTabTitle(l){return G(this,null,function*(){var a;if((a=l==null?void 0:l.meta)!=null&&a.newTabTitle)return;const t=this.tabs.find(o=>me(o)===me(l));t&&(t.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(l){for(const t of l)t.meta.affixTab=!0,this.addTab(Cn(t))},setMenuList(l){this.menuList=l},setTabTitle(l,t){return G(this,null,function*(){const a=this.tabs.find(o=>me(o)===me(l));a&&(a.meta.newTabTitle=t,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(l,t){return G(this,null,function*(){const a=this.tabs[l];a&&(this.tabs.splice(l,1),this.tabs.splice(t,0,a),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(l){return G(this,null,function*(){var a,o;yield((o=(a=l==null?void 0:l.meta)==null?void 0:a.affixTab)!=null?o:!1)?this.unpinTab(l):this.pinTab(l)})},unpinTab(l){return G(this,null,function*(){var s;const t=this.tabs.findIndex(n=>me(n)===me(l));if(t!==-1){const n=this.tabs[t];l.meta.affixTab=!1,l.meta.title=(s=n==null?void 0:n.meta)==null?void 0:s.title,this.tabs.splice(t,1,l)}const o=this.tabs.filter(n=>Ne(n)).length;yield this.sortTabs(t,o)})},updateCacheTabs(){return G(this,null,function*(){var t;const l=new Set;for(const a of this.tabs){if(!((t=a.meta)==null?void 0:t.keepAlive))continue;(a.matched||[]).forEach((n,i)=>{i>0&&l.add(n.name)});const s=a.name;l.add(s)}this.cachedTabs=l})}},getters:{affixTabs(){return this.tabs.filter(t=>Ne(t)).sort((t,a)=>{var n,i,r,d;const o=(i=(n=t.meta)==null?void 0:n.affixTabOrder)!=null?i:0,s=(d=(r=a.meta)==null?void 0:r.affixTabOrder)!=null?d:0;return o-s})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getMenuList(){return this.menuList},getTabs(){const l=this.tabs.filter(t=>!Ne(t));return[...this.affixTabs,...l].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,menuList:["close","affix","maximize","reload","open-in-new-window","close-left","close-right","close-other","close-all"],renderRouteView:!0,tabs:[],updateTime:Date.now()})});function xn(l){if(!l)return l;const s=l,{matched:t,meta:a}=s,o=xe(s,["matched","meta"]);return ke(Q({},o),{matched:t?t.map(n=>({meta:n.meta,name:n.name,path:n.path})):void 0,meta:ke(Q({},a),{newTabTitle:a.newTabTitle})})}function Ne(l){var t,a;return(a=(t=l==null?void 0:l.meta)==null?void 0:t.affixTab)!=null?a:!1}function kn(l){var a;const t=(a=l==null?void 0:l.matched)!=null?a:[];return!l.meta.hideInTab&&t.every(o=>!o.meta.hideInTab)}function me(l){return decodeURIComponent(l.fullPath||l.path)}function Cn(l){return{meta:l.meta,name:l.name,path:l.path}}const Sn=_e("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const Tn=_e("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const Mn=_e("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const Bn=_e("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const _n=_e("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);const $n=_e("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);const Vn=_e("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const En=_e("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const Pn=_e("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const Fa=_e("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const Ka=_e("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const In=_e("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const At=_e("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const Ln=_e("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const zn=_e("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),An=Ma("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{shape:{circle:"rounded-full",square:"rounded-md"},size:{base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl",sm:"h-10 w-10 text-xs"}}}),Un=M({__name:"Avatar",props:{class:{},shape:{default:"circle"},size:{default:"sm"}},setup(l){const t=l;return(a,o)=>(u(),x(e(_l),{class:L(e(re)(e(An)({size:a.size,shape:a.shape}),t.class))},{default:c(()=>[_(a.$slots,"default")]),_:3},8,["class"]))}}),On=M({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{}},setup(l){const t=l;return(a,o)=>(u(),x(e($l),Ve(Ue(t)),{default:c(()=>[_(a.$slots,"default")]),_:3},16))}}),Hn=M({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{}},setup(l){const t=l;return(a,o)=>(u(),x(e(Vl),pe(t,{class:"h-full w-full object-cover"}),null,16))}}),Dn=M({__name:"Breadcrumb",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("nav",{class:L(t.class),"aria-label":"breadcrumb",role:"navigation"},[_(a.$slots,"default")],2))}}),Wn=M({__name:"BreadcrumbItem",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("li",{class:L(e(re)("hover:text-foreground inline-flex items-center gap-1.5",t.class))},[_(a.$slots,"default")],2))}}),Nn=M({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(l){const t=l;return(a,o)=>(u(),x(e(El),{as:a.as,"as-child":a.asChild,class:L(e(re)("hover:text-foreground transition-colors",t.class))},{default:c(()=>[_(a.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Rn=M({__name:"BreadcrumbList",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("ol",{class:L(e(re)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",t.class))},[_(a.$slots,"default")],2))}}),Fn=M({__name:"BreadcrumbPage",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("span",{class:L(e(re)("text-foreground font-normal",t.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[_(a.$slots,"default")],2))}}),Kn=M({__name:"BreadcrumbSeparator",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("li",{class:L(e(re)("[&>svg]:size-3.5",t.class)),"aria-hidden":"true",role:"presentation"},[_(a.$slots,"default",{},()=>[b(e(Vt))])],2))}}),Gn=M({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(Pl),pe(a.value,{class:e(re)("bg-border -mx-1 my-1 h-px",t.class)}),null,16,["class"]))}}),jn=M({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:t}){const s=Ie(l,t);return(n,i)=>(u(),x(e(Il),Ve(Ue(e(s))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),qn=M({__name:"HoverCardContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const i=t,{class:s}=i;return xe(i,["class"])}),o=Qe(a);return(s,n)=>(u(),x(e(Ll),null,{default:c(()=>[b(e(zl),pe(e(o),{class:e(re)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup w-64 rounded-md border p-4 shadow-md outline-none",t.class)}),{default:c(()=>[_(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Jn=M({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(l){const t=l;return(a,o)=>(u(),x(e(Al),Ve(Ue(t)),{default:c(()=>[_(a.$slots,"default")]),_:3},16))}}),Xn=M({__name:"NumberField",props:{defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const d=a,{class:i}=d;return xe(d,["class"])}),n=Ie(s,o);return(i,r)=>(u(),x(e(Ul),pe(e(n),{class:e(re)("grid gap-1.5",a.class)}),{default:c(()=>[_(i.$slots,"default")]),_:3},16,["class"]))}}),Yn=M({__name:"NumberFieldContent",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("div",{class:L(e(re)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",t.class))},[_(a.$slots,"default")],2))}}),Zn=M({__name:"NumberFieldDecrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const i=t,{class:s}=i;return xe(i,["class"])}),o=Qe(a);return(s,n)=>(u(),x(e(Ol),pe({"data-slot":"decrement"},e(o),{class:e(re)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",t.class)}),{default:c(()=>[_(s.$slots,"default",{},()=>[b(e(cn),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Qn=M({__name:"NumberFieldIncrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const i=t,{class:s}=i;return xe(i,["class"])}),o=Qe(a);return(s,n)=>(u(),x(e(Hl),pe({"data-slot":"increment"},e(o),{class:e(re)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",t.class)}),{default:c(()=>[_(s.$slots,"default",{},()=>[b(e(Ln),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),es=M({__name:"NumberFieldInput",setup(l){return(t,a)=>(u(),x(e(Dl),{class:L(e(re)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),Ga=M({__name:"ScrollBar",props:{orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(Wl),pe(a.value,{class:e(re)("flex touch-none select-none transition-colors",o.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",o.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",t.class)}),{default:c(()=>[b(e(Nl),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),ts=M({__name:"ScrollArea",props:{type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{},class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(Rl),pe(a.value,{class:e(re)("relative overflow-hidden",t.class)}),{default:c(()=>[b(e(Fl),{"as-child":"",class:"h-full w-full rounded-[inherit] focus:outline-none",onScroll:o.onScroll},{default:c(()=>[_(o.$slots,"default")]),_:3},8,["onScroll"]),b(Ga),b(e(Kl))]),_:3},16,["class"]))}}),as=M({__name:"Separator",props:{orientation:{},decorative:{type:Boolean},asChild:{type:Boolean},as:{},class:{},label:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(Gl),pe(a.value,{class:e(re)("bg-border relative shrink-0",t.orientation==="vertical"?"h-full w-px":"h-px w-full",t.class)}),{default:c(()=>[t.label?(u(),S("span",{key:0,class:L(e(re)("text-muted-foreground bg-background absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 items-center justify-center text-xs",t.orientation==="vertical"?"w-[1px] px-1 py-2":"h-[1px] px-2 py-1"))},V(t.label),3)):I("",!0)]),_:1},16,["class"]))}}),ls=Ma("bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),os=M({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(l,{emit:t}){const s=Ie(l,t);return(n,i)=>(u(),x(e(jl),Ve(Ue(e(s))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),ga=M({__name:"SheetClose",props:{asChild:{type:Boolean},as:{}},setup(l){const t=l;return(a,o)=>(u(),x(e(ql),Ve(Ue(t)),{default:c(()=>[_(a.$slots,"default")]),_:3},16))}}),ns=["data-dismissable-drawer"],ss=M({__name:"SheetOverlay",setup(l){Jl();const t=bt("DISMISSABLE_DRAWER_ID");return(a,o)=>(u(),S("div",{"data-dismissable-drawer":e(t),class:"bg-overlay z-popup inset-0"},null,8,ns))}}),rs=M({inheritAttrs:!1,__name:"SheetContent",props:{appendTo:{default:"body"},class:{},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},side:{},zIndex:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const z=a,{class:p,modal:h,open:m,side:v}=z;return xe(z,["class","modal","open","side"])});function n(){return a.appendTo==="body"||a.appendTo===document.body||!a.appendTo}const i=w(()=>n()?"fixed":"absolute"),r=Ie(s,o),d=Z(null);function f(p){var h;p.target===((h=d.value)==null?void 0:h.$el)&&(a.open?o("opened"):o("closed"))}return(p,h)=>(u(),x(e(Xl),{to:p.appendTo},{default:c(()=>[b(rt,{name:"fade"},{default:c(()=>[p.open&&p.modal?(u(),x(ss,{key:0,style:he(ke(Q({},p.zIndex?{zIndex:p.zIndex}:{}),{position:i.value,backdropFilter:p.overlayBlur&&p.overlayBlur>0?`blur(${p.overlayBlur}px)`:"none"}))},null,8,["style"])):I("",!0)]),_:1}),b(e(Yl),pe({ref_key:"contentRef",ref:d,class:e(re)("z-popup",e(ls)({side:p.side}),a.class),style:ke(Q({},p.zIndex?{zIndex:p.zIndex}:{}),{position:i.value}),onAnimationend:f},Q(Q({},e(r)),p.$attrs)),{default:c(()=>[_(p.$slots,"default")]),_:3},16,["class","style"])]),_:3},8,["to"]))}}),Ct=M({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(Zl),pe({class:e(re)("text-muted-foreground text-sm",t.class)},a.value),{default:c(()=>[_(o.$slots,"default")]),_:3},16,["class"]))}}),is=M({__name:"SheetFooter",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("div",{class:L(e(re)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[_(a.$slots,"default")],2))}}),ds=M({__name:"SheetHeader",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("div",{class:L(e(re)("flex flex-col text-center sm:text-left",t.class))},[_(a.$slots,"default")],2))}}),St=M({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(Ql),pe({class:e(re)("text-foreground font-medium",t.class)},a.value),{default:c(()=>[_(o.$slots,"default")]),_:3},16,["class"]))}}),us=M({__name:"Switch",props:{defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:checked"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const d=a,{class:i}=d;return xe(d,["class"])}),n=Ie(s,o);return(i,r)=>(u(),x(e(to),pe(e(n),{class:e(re)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a.class)}),{default:c(()=>[b(e(eo),{class:L(e(re)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),cs=M({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(l,{emit:t}){const s=Ie(l,t);return(n,i)=>(u(),x(e(ao),Ve(Ue(e(s))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),ps=M({__name:"TabsContent",props:{value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(lo),pe({class:e(re)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",t.class)},a.value),{default:c(()=>[_(o.$slots,"default")]),_:3},16,["class"]))}}),fs=M({__name:"TabsList",props:{loop:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(oo),pe(a.value,{class:e(re)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",t.class)}),{default:c(()=>[_(o.$slots,"default")]),_:3},16,["class"]))}}),bs=M({inheritAttrs:!1,__name:"avatar",props:{alt:{default:"avatar"},class:{},dot:{type:Boolean,default:!1},dotClass:{default:"bg-green-500"},size:{},delayMs:{},asChild:{type:Boolean},as:{default:"button"},src:{},referrerPolicy:{}},setup(l){const t=l,a=w(()=>t.alt.slice(-2).toUpperCase()),o=w(()=>t.size!==void 0&&t.size>0?{height:`${t.size}px`,width:`${t.size}px`}:{});return(s,n)=>(u(),S("div",{class:L([t.class,"relative flex flex-shrink-0 items-center"]),style:he(o.value)},[b(e(Un),{class:L([t.class,"size-full"])},{default:c(()=>[b(e(Hn),{alt:s.alt,src:s.src},null,8,["alt","src"]),b(e(On),null,{default:c(()=>[E(V(a.value),1)]),_:1})]),_:1},8,["class"]),s.dot?(u(),S("span",{key:0,class:L([s.dotClass,"border-background absolute bottom-0 right-0 size-3 rounded-full border-2"])},null,2)):I("",!0)],6))}}),ms=M({name:"VbenButtonGroup",__name:"button-group",props:{border:{type:Boolean,default:!1},gap:{default:0},size:{default:"middle"}},setup(l){return(t,a)=>(u(),S("div",{class:L(e(re)("vben-button-group rounded-md",`size-${t.size}`,t.gap?"with-gap":"no-gap",t.$attrs.class)),style:he({gap:t.gap?`${t.gap}px`:"0px"})},[_(t.$slots,"default",{},void 0,!0)],6))}}),hs=Me(ms,[["__scopeId","data-v-ba11c217"]]),vs={key:0,class:"icon-wrapper"},gs=M({__name:"check-button-group",props:ge({beforeChange:{},btnClass:{},gap:{default:0},multiple:{type:Boolean,default:!1},options:{},showIcon:{type:Boolean,default:!0},size:{default:"middle"},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:ge(["btnClick"],["update:modelValue"]),setup(l,{emit:t}){const a=l,o=t,s=w(()=>ke(Q({},Uo(a,["options","btnClass","size","disabled"])),{class:re(a.btnClass)})),n=y(l,"modelValue"),i=Z([]),r=Z([]);ye(()=>a.multiple,f=>{f?n.value=i.value:n.value=i.value.length>0?i.value[0]:void 0}),ye(()=>n.value,f=>{if(Array.isArray(f)){const p=f.filter(h=>h!==void 0);p.length>0?i.value=a.multiple?[...p]:[p[0]]:i.value=[]}else i.value=f===void 0?[]:[f]},{deep:!0,immediate:!0});function d(f){return G(this,null,function*(){if(a.beforeChange&&Ia(a.beforeChange))try{if(r.value.push(f),(yield a.beforeChange(f,!i.value.includes(f)))===!1)return}finally{r.value.splice(r.value.indexOf(f),1)}a.multiple?(i.value.includes(f)?i.value=i.value.filter(p=>p!==f):i.value.push(f),n.value=i.value):(i.value=[f],n.value=f),o("btnClick",f)})}return(f,p)=>(u(),x(hs,{size:a.size,gap:a.gap,class:"vben-check-button-group"},{default:c(()=>[(u(!0),S(ee,null,be(a.options,(h,m)=>(u(),x(Ze,pe({key:m,class:e(re)("border",a.btnClass),disabled:a.disabled||r.value.includes(h.value)||!a.multiple&&r.value.length>0,ref_for:!0},s.value,{variant:i.value.includes(h.value)?"default":"outline",onClick:v=>d(h.value)}),{default:c(()=>[a.showIcon?(u(),S("div",vs,[r.value.includes(h.value)?(u(),x(e(no),{key:0,class:"animate-spin"})):i.value.includes(h.value)?(u(),x(e(_n),{key:1})):(u(),x(e($n),{key:2}))])):I("",!0),_(f.$slots,"option",{label:h.label,value:h.value},()=>[b(e(pn),{content:h.label},null,8,["content"])],!0)]),_:2},1040,["class","disabled","variant","onClick"]))),128))]),_:3},8,["size","gap"]))}}),ys=Me(gs,[["__scopeId","data-v-bcad0201"]]),ws=l=>{const t=Bt(),a=Bt(),o=Z(!1),s=()=>{var r;t.value&&(o.value=t.value.scrollTop>=((r=l==null?void 0:l.visibilityHeight)!=null?r:0))},n=()=>{var r;(r=t.value)==null||r.scrollTo({behavior:"smooth",top:0})},i=Et(s,300,!0);return Oo(a,"scroll",i),Je(()=>{var r;if(a.value=document,t.value=document.documentElement,l.target){if(t.value=(r=document.querySelector(l.target))!=null?r:void 0,!t.value)throw new Error(`target does not exist: ${l.target}`);a.value=t.value}s()}),{handleClick:n,visible:o}},xs=M({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(l){const t=l,a=w(()=>({bottom:`${t.bottom}px`,right:`${t.right}px`})),{handleClick:o,visible:s}=ws(t);return(n,i)=>(u(),x(rt,{name:"fade-down"},{default:c(()=>[e(s)?(u(),x(e(Ze),{key:0,style:he(a.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float z-popup fixed bottom-10 size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(o)},{default:c(()=>[b(e(Bn),{class:"size-4"})]),_:1},8,["style","onClick"])):I("",!0)]),_:1}))}}),ks={class:"flex"},Cs=["onClick"],Ss={class:"flex-center z-10 h-full"},Ts=M({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:t}){const a=t;function o(s,n){!n||s===l.breadcrumbs.length-1||a("select",n)}return(s,n)=>(u(),S("ul",ks,[b(pt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),S(ee,null,be(s.breadcrumbs,(i,r)=>(u(),S("li",{key:`${i.path}-${i.title}-${r}`},[A("a",{href:"javascript:void 0",onClick:Pe(d=>o(r,i.path),["stop"])},[A("span",Ss,[s.showIcon?(u(),x(e(Oe),{key:0,icon:i.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):I("",!0),A("span",{class:L({"text-foreground font-normal":r===s.breadcrumbs.length-1})},V(i.title),3)])],8,Cs)]))),128))]),_:1})]))}}),Ms=Me(Ts,[["__scopeId","data-v-da1498bb"]]),Bs={key:0},_s={class:"flex-center"},$s={class:"flex-center"},Vs=M({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(l,{emit:t}){const a=t;function o(s){s&&a("select",s)}return(s,n)=>(u(),x(e(Dn),null,{default:c(()=>[b(e(Rn),null,{default:c(()=>[b(pt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),S(ee,null,be(s.breadcrumbs,(i,r)=>(u(),x(e(Wn),{key:`${i.path}-${i.title}-${r}`},{default:c(()=>{var d,f;return[(f=(d=i.items)==null?void 0:d.length)!=null&&f?(u(),S("div",Bs,[b(e(Oa),null,{default:c(()=>[b(e(Ha),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(u(),x(e(Oe),{key:0,icon:i.icon,class:"size-5"},null,8,["icon"])):I("",!0),E(" "+V(i.title)+" ",1),b(e(It),{class:"size-4"})]),_:2},1024),b(e(Da),{align:"start"},{default:c(()=>[(u(!0),S(ee,null,be(i.items,p=>(u(),x(e(Wa),{key:`sub-${p.path}`,onClick:Pe(h=>o(p.path),["stop"])},{default:c(()=>[E(V(p.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):r!==s.breadcrumbs.length-1?(u(),x(e(Nn),{key:1,href:"javascript:void 0",onClick:Pe(p=>o(i.path),["stop"])},{default:c(()=>[A("div",_s,[s.showIcon?(u(),x(e(Oe),{key:0,class:L([{"size-5":i.isHome},"mr-1 size-4"]),icon:i.icon},null,8,["class","icon"])):I("",!0),E(" "+V(i.title),1)])]),_:2},1032,["onClick"])):(u(),x(e(Fn),{key:2},{default:c(()=>[A("div",$s,[s.showIcon?(u(),x(e(Oe),{key:0,class:L([{"size-5":i.isHome},"mr-1 size-4"]),icon:i.icon},null,8,["class","icon"])):I("",!0),E(" "+V(i.title),1)])]),_:2},1024)),r<s.breadcrumbs.length-1&&!i.isHome?(u(),x(e(Kn),{key:3})):I("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),Es=M({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:t}){const s=Ie(l,t);return(n,i)=>(u(),S(ee,null,[n.styleType==="normal"?(u(),x(Vs,pe({key:0},e(s),{class:"vben-breadcrumb"}),null,16)):I("",!0),n.styleType==="background"?(u(),x(Ms,pe({key:1},e(s),{class:"vben-breadcrumb"}),null,16)):I("",!0)],64))}}),Ps=Me(Es,[["__scopeId","data-v-4cd036dd"]]),Is=M({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(l,{emit:t}){const s=Ie(l,t);return(n,i)=>(u(),x(e(so),Ve(Ue(e(s))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),Ls=M({__name:"ContextMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const d=a,{class:i}=d;return xe(d,["class"])}),n=Ie(s,o);return(i,r)=>(u(),x(e(ro),null,{default:c(()=>[b(e(io),pe(e(n),{class:e(re)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",a.class)}),{default:c(()=>[_(i.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),zs=M({__name:"ContextMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},emits:["select"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const d=a,{class:i}=d;return xe(d,["class"])}),n=Ie(s,o);return(i,r)=>(u(),x(e(uo),pe(e(n),{class:e(re)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",i.inset&&"pl-8",a.class)}),{default:c(()=>[_(i.$slots,"default")]),_:3},16,["class"]))}}),As=M({__name:"ContextMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const n=t,{class:o}=n;return xe(n,["class"])});return(o,s)=>(u(),x(e(co),pe(a.value,{class:e(re)("bg-border -mx-1 my-1 h-px",t.class)}),null,16,["class"]))}}),Us=M({__name:"ContextMenuShortcut",props:{class:{}},setup(l){const t=l;return(a,o)=>(u(),S("span",{class:L(e(re)("text-muted-foreground ml-auto text-xs tracking-widest",t.class))},[_(a.$slots,"default")],2))}}),Os=M({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const a=Qe(l);return(o,s)=>(u(),x(e(po),Ve(Ue(e(a))),{default:c(()=>[_(o.$slots,"default")]),_:3},16))}}),ja=M({__name:"context-menu",props:{dir:{},modal:{type:Boolean},class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function}},emits:["update:open"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const v=a,{class:d,contentClass:f,contentProps:p,itemClass:h}=v;return xe(v,["class","contentClass","contentProps","itemClass"])}),n=Ie(s,o),i=w(()=>{var d;return(d=a.menus)==null?void 0:d.call(a,a.handlerData)});function r(d){var f;d.disabled||(f=d==null?void 0:d.handler)==null||f.call(d,a.handlerData)}return(d,f)=>(u(),x(e(Is),Ve(Ue(e(n))),{default:c(()=>[b(e(Os),{"as-child":""},{default:c(()=>[_(d.$slots,"default")]),_:3}),b(e(Ls),pe({class:d.contentClass},d.contentProps,{class:"side-content z-popup"}),{default:c(()=>[(u(!0),S(ee,null,be(i.value,p=>(u(),S(ee,{key:p.key},[b(e(zs),{class:L([d.itemClass,"cursor-pointer"]),disabled:p.disabled,inset:p.inset||!p.icon,onClick:h=>r(p)},{default:c(()=>[p.icon?(u(),x($e(p.icon),{key:0,class:"mr-2 size-4 text-lg"})):I("",!0),E(" "+V(p.text)+" ",1),p.shortcut?(u(),x(e(Us),{key:1},{default:c(()=>[E(V(p.shortcut),1)]),_:2},1024)):I("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),p.separator?(u(),x(e(As),{key:0})):I("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),Hs=M({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(l){const t=l;function a(o){var s;o.disabled||(s=o==null?void 0:o.handler)==null||s.call(o,t)}return(o,s)=>(u(),x(e(Oa),null,{default:c(()=>[b(e(Ha),{class:"flex h-full items-center gap-1"},{default:c(()=>[_(o.$slots,"default")]),_:3}),b(e(Da),{align:"start"},{default:c(()=>[b(e(on),null,{default:c(()=>[(u(!0),S(ee,null,be(o.menus,n=>(u(),S(ee,{key:n.value},[b(e(Wa),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:i=>a(n)},{default:c(()=>[n.icon?(u(),x($e(n.icon),{key:0,class:"mr-2 size-4"})):I("",!0),E(" "+V(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(u(),x(e(Gn),{key:0,class:"bg-border"})):I("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),Ds={class:"h-full cursor-pointer"},Ws=M({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:t}){const a=l,o=t,s=w(()=>{const p=a,{class:i,contentClass:r,contentProps:d}=p;return xe(p,["class","contentClass","contentProps"])}),n=Ie(s,o);return(i,r)=>(u(),x(e(jn),Ve(Ue(e(n))),{default:c(()=>[b(e(Jn),{"as-child":"",class:"h-full"},{default:c(()=>[A("div",Ds,[_(i.$slots,"trigger")])]),_:3}),b(e(qn),pe({class:i.contentClass},i.contentProps,{class:"side-content z-popup"}),{default:c(()=>[_(i.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),Ns=["href"],Rs={class:"text-foreground truncate text-nowrap font-semibold"},Fs=M({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(l){return(t,a)=>(u(),S("div",{class:L([t.theme,"flex h-full items-center text-lg"])},[A("a",{class:L([t.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:t.href},[t.src?(u(),x(e(bs),{key:0,alt:t.text,src:t.src,size:t.logoSize,class:"relative rounded-none bg-transparent"},null,8,["alt","src","size"])):I("",!0),t.collapsed?I("",!0):_(t.$slots,"text",{key:1},()=>[A("span",Rs,V(t.text),1)])],10,Ns)],2))}}),ya=1,Ks=M({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(l,{emit:t}){const a=l,o=t,s=Z(!0),n=Z(!1),i=Z(!1),r=Z(!0),d=w(()=>a.shadow&&a.shadowTop),f=w(()=>a.shadow&&a.shadowBottom),p=w(()=>a.shadow&&a.shadowLeft),h=w(()=>a.shadow&&a.shadowRight),m=w(()=>({"both-shadow":!r.value&&!n.value&&p.value&&h.value,"left-shadow":!r.value&&p.value,"right-shadow":!n.value&&h.value}));function v(T){var j,q,B,R,J,se;const z=T.target,D=(j=z==null?void 0:z.scrollTop)!=null?j:0,$=(q=z==null?void 0:z.scrollLeft)!=null?q:0,O=(B=z==null?void 0:z.clientHeight)!=null?B:0,F=(R=z==null?void 0:z.clientWidth)!=null?R:0,P=(J=z==null?void 0:z.scrollHeight)!=null?J:0,K=(se=z==null?void 0:z.scrollWidth)!=null?se:0;s.value=D<=0,r.value=$<=0,i.value=Math.abs(D)+O>=P-ya,n.value=Math.abs($)+F>=K-ya,o("scrollAt",{bottom:i.value,left:r.value,right:n.value,top:s.value})}return(T,z)=>(u(),x(e(ts),{class:L([[e(re)(a.class),m.value],"vben-scrollbar relative"]),"on-scroll":v},{default:c(()=>[d.value?(u(),S("div",{key:0,class:L([{"opacity-100":!s.value,"border-border border-t":T.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):I("",!0),_(T.$slots,"default",{},void 0,!0),f.value?(u(),S("div",{key:1,class:L([{"opacity-100":!s.value&&!i.value,"border-border border-b":T.shadowBorder&&!s.value&&!i.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):I("",!0),T.horizontal?(u(),x(e(Ga),{key:2,class:L(T.scrollBarClass),orientation:"horizontal"},null,8,["class"])):I("",!0)]),_:3},8,["class"]))}}),$t=Me(Ks,[["__scopeId","data-v-c94474ed"]]),Gs={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},js=M({__name:"tabs-indicator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const i=t,{class:s}=i;return xe(i,["class"])}),o=Qe(a);return(s,n)=>(u(),x(e(fo),pe(e(o),{class:e(re)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",t.class)}),{default:c(()=>[A("div",Gs,[_(s.$slots,"default")])]),_:3},16,["class"]))}}),qs=M({__name:"segmented",props:ge({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=l,a=y(l,"modelValue"),o=w(()=>{var i;return t.defaultValue||((i=t.tabs[0])==null?void 0:i.value)}),s=w(()=>({"grid-template-columns":`repeat(${t.tabs.length}, minmax(0, 1fr))`})),n=w(()=>({width:`${(100/t.tabs.length).toFixed(0)}%`}));return(i,r)=>(u(),x(e(cs),{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=d=>a.value=d),"default-value":o.value},{default:c(()=>[b(e(fs),{style:he(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[b(js,{style:he(n.value)},null,8,["style"]),(u(!0),S(ee,null,be(i.tabs,d=>(u(),x(e(bo),{key:d.value,value:d.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[E(V(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(u(!0),S(ee,null,be(i.tabs,d=>(u(),x(e(ps),{key:d.value,value:d.value},{default:c(()=>[_(i.$slots,d.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}});function qa(){const{contentIsMaximize:l}=it();function t(){const a=l.value;Ke({header:{hidden:!a},sidebar:{hidden:!a}})}return{contentIsMaximize:l,toggleMaximize:t}}function Ja(){const l=et(),t=at();function a(){return G(this,null,function*(){yield t.refresh(l)})}return{refresh:a}}function Xa(){const l=et(),t=Re(),a=at();function o($){return G(this,null,function*(){yield a.closeLeftTabs($||t)})}function s(){return G(this,null,function*(){yield a.closeAllTabs(l)})}function n($){return G(this,null,function*(){yield a.closeRightTabs($||t)})}function i($){return G(this,null,function*(){yield a.closeOtherTabs($||t)})}function r($){return G(this,null,function*(){yield a.closeTab($||t,l)})}function d($){return G(this,null,function*(){yield a.pinTab($||t)})}function f($){return G(this,null,function*(){yield a.unpinTab($||t)})}function p($){return G(this,null,function*(){yield a.toggleTabPin($||t)})}function h(){return G(this,null,function*(){yield a.refresh(l)})}function m($){return G(this,null,function*(){yield a.openTabInNewWindow($||t)})}function v($){return G(this,null,function*(){yield a.closeTabByKey($,l)})}function T($){return G(this,null,function*(){a.setUpdateTime(),yield a.setTabTitle(t,$)})}function z(){return G(this,null,function*(){a.setUpdateTime(),yield a.resetTabTitle(t)})}function D($=t){var ue;const O=a.getTabs,F=a.affixTabs,P=O.findIndex(we=>we.path===$.path),K=O.length<=1,{meta:j}=$,q=(ue=j==null?void 0:j.affixTab)!=null?ue:!1,B=t.path===$.path,R=P===0||P-F.length<=0||!B,J=!B||P===O.length-1,se=K||!B||O.length-F.length<=1;return{disabledCloseAll:K,disabledCloseCurrent:!!q||K,disabledCloseLeft:R,disabledCloseOther:se,disabledCloseRight:J,disabledRefresh:!B}}return{closeAllTabs:s,closeCurrentTab:r,closeLeftTabs:o,closeOtherTabs:i,closeRightTabs:n,closeTabByKey:v,getTabDisableState:D,openTabInNewWindow:m,pinTab:d,refreshTab:h,resetTabTitle:z,setTabTitle:T,toggleTabPin:p,unpinTab:f}}const Js={class:"flex items-center"},Xs={class:"flex-center"},Ys=M({__name:"drawer",props:{drawerApi:{default:void 0},appendToMain:{type:Boolean,default:!1},cancelText:{},class:{},closable:{type:Boolean},closeIconPlacement:{default:"right"},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},placement:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean,default:!1},title:{},titleTooltip:{},zIndex:{default:1e3}},setup(l){var te,de;const t=l,a=Ba.getComponents(),o=Ho();mt("DISMISSABLE_DRAWER_ID",o);const s=Z(),{$t:n}=mo(),{isMobile:i}=_a(),r=(de=(te=t.drawerApi)==null?void 0:te.useStore)==null?void 0:de.call(te),{appendToMain:d,cancelText:f,class:p,closable:h,closeIconPlacement:m,closeOnClickModal:v,closeOnPressEscape:T,confirmLoading:z,confirmText:D,contentClass:$,description:O,destroyOnClose:F,footer:P,footerClass:K,header:j,headerClass:q,loading:B,modal:R,openAutoFocus:J,overlayBlur:se,placement:ue,showCancelButton:we,showConfirmButton:W,submitting:Y,title:ve,titleTooltip:Ce,zIndex:Se}=ho(t,r);function Be(X){(!v.value||Y.value)&&X.preventDefault()}function Ee(X){(!T.value||Y.value)&&X.preventDefault()}function N(X){const fe=X.target,We=fe==null?void 0:fe.dataset.dismissableDrawer;(Y.value||!v.value||We!==o)&&X.preventDefault()}function le(X){J.value||X==null||X.preventDefault()}function oe(X){X.preventDefault(),X.stopPropagation()}const ne=w(()=>d.value?`#${$a}>div:not(.absolute)>div`:void 0),ie=Z(!1),ce=Z(!0);ye(()=>{var X;return(X=r==null?void 0:r.value)==null?void 0:X.isOpen},X=>{ce.value=!1,X&&!e(ie)&&(ie.value=!0)});function De(){var X;ce.value=!0,(X=t.drawerApi)==null||X.onClosed()}const H=w(()=>!e(F)&&e(ie));return(X,fe)=>{var We;return u(),x(e(os),{modal:!1,open:(We=e(r))==null?void 0:We.isOpen,"onUpdate:open":fe[3]||(fe[3]=()=>{var Fe;return(Fe=X.drawerApi)==null?void 0:Fe.close()})},{default:c(()=>{var Fe;return[b(e(rs),{"append-to":ne.value,class:L(e(re)("flex w-[520px] flex-col",e(p),{"!w-full":e(i)||e(ue)==="bottom"||e(ue)==="top","max-h-[100vh]":e(ue)==="bottom"||e(ue)==="top",hidden:ce.value})),modal:e(R),open:(Fe=e(r))==null?void 0:Fe.isOpen,side:e(ue),"z-index":e(Se),"force-mount":H.value,"overlay-blur":e(se),onCloseAutoFocus:oe,onClosed:De,onEscapeKeyDown:Ee,onFocusOutside:oe,onInteractOutside:Be,onOpenAutoFocus:le,onOpened:fe[2]||(fe[2]=()=>{var Ae;return(Ae=X.drawerApi)==null?void 0:Ae.onOpened()}),onPointerDownOutside:N},{default:c(()=>[e(j)?(u(),x(e(ds),{key:0,class:L(e(re)("!flex flex-row items-center justify-between border-b px-6 py-5",e(q),{"px-4 py-3":e(h),"pl-2":e(h)&&e(m)==="left"}))},{default:c(()=>[A("div",Js,[e(h)&&e(m)==="left"?(u(),x(e(ga),{key:0,"as-child":"",disabled:e(Y),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[_(X.$slots,"close-icon",{},()=>[b(e(Mt),null,{default:c(()=>[b(e(st),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):I("",!0),e(h)&&e(m)==="left"?(u(),x(e(as),{key:1,class:"ml-1 mr-2 h-8",decorative:"",orientation:"vertical"})):I("",!0),e(ve)?(u(),x(e(St),{key:2,class:"text-left"},{default:c(()=>[_(X.$slots,"title",{},()=>[E(V(e(ve))+" ",1),e(Ce)?(u(),x(e(fn),{key:0,"trigger-class":"pb-1"},{default:c(()=>[E(V(e(Ce)),1)]),_:1})):I("",!0)])]),_:3})):I("",!0),e(O)?(u(),x(e(Ct),{key:3,class:"mt-1 text-xs"},{default:c(()=>[_(X.$slots,"description",{},()=>[E(V(e(O)),1)])]),_:3})):I("",!0)]),!e(ve)||!e(O)?(u(),x(e(da),{key:0},{default:c(()=>[e(ve)?I("",!0):(u(),x(e(St),{key:0})),e(O)?I("",!0):(u(),x(e(Ct),{key:1}))]),_:1})):I("",!0),A("div",Xs,[_(X.$slots,"extra"),e(h)&&e(m)==="right"?(u(),x(e(ga),{key:0,"as-child":"",disabled:e(Y),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[_(X.$slots,"close-icon",{},()=>[b(e(Mt),null,{default:c(()=>[b(e(st),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):I("",!0)])]),_:3},8,["class"])):(u(),x(e(da),{key:1},{default:c(()=>[b(e(St)),b(e(Ct))]),_:1})),A("div",{ref_key:"wrapperRef",ref:s,class:L(e(re)("relative flex-1 overflow-y-auto p-3",e($),{"pointer-events-none":e(B)||e(Y)}))},[_(X.$slots,"default")],2),e(B)||e(Y)?(u(),x(e(vo),{key:2,spinning:""})):I("",!0),e(P)?(u(),x(e(is),{key:3,class:L(e(re)("w-full flex-row items-center justify-end border-t p-2 px-3",e(K)))},{default:c(()=>[_(X.$slots,"prepend-footer"),_(X.$slots,"footer",{},()=>[e(we)?(u(),x($e(e(a).DefaultButton||e(Ze)),{key:0,variant:"ghost",disabled:e(Y),onClick:fe[0]||(fe[0]=()=>{var Ae;return(Ae=X.drawerApi)==null?void 0:Ae.onCancel()})},{default:c(()=>[_(X.$slots,"cancelText",{},()=>[E(V(e(f)||e(n)("cancel")),1)])]),_:3},8,["disabled"])):I("",!0),_(X.$slots,"center-footer"),e(W)?(u(),x($e(e(a).PrimaryButton||e(Ze)),{key:1,loading:e(z)||e(Y),onClick:fe[1]||(fe[1]=()=>{var Ae;return(Ae=X.drawerApi)==null?void 0:Ae.onConfirm()})},{default:c(()=>[_(X.$slots,"confirmText",{},()=>[E(V(e(D)||e(n)("confirm")),1)])]),_:3},8,["loading"])):I("",!0)]),_(X.$slots,"append-footer")]),_:3},8,["class"])):I("",!0)]),_:3},8,["append-to","class","modal","open","side","z-index","force-mount","overlay-blur"])]}),_:3},8,["open"])}}});class Zs{constructor(t={}){lt(this,"sharedData",{payload:{}});lt(this,"store");lt(this,"api");lt(this,"state");const h=t,{connectedComponent:a,onBeforeClose:o,onCancel:s,onClosed:n,onConfirm:i,onOpenChange:r,onOpened:d}=h,f=xe(h,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),p={class:"",closable:!0,closeIconPlacement:"right",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,placement:"right",showCancelButton:!0,showConfirmButton:!0,submitting:!1,title:""};this.store=new bn(Q(Q({},p),f),{onUpdate:()=>{var v,T,z;const m=this.store.state;(m==null?void 0:m.isOpen)===((v=this.state)==null?void 0:v.isOpen)?this.state=m:(this.state=m,(z=(T=this.api).onOpenChange)==null||z.call(T,!!(m!=null&&m.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:o,onCancel:s,onClosed:n,onConfirm:i,onOpenChange:r,onOpened:d},Do(this)}close(){return G(this,null,function*(){var a,o,s;((s=yield(o=(a=this.api).onBeforeClose)==null?void 0:o.call(a))!=null?s:!0)&&this.store.setState(n=>ke(Q({},n),{isOpen:!1,submitting:!1}))})}getData(){var t,a;return(a=(t=this.sharedData)==null?void 0:t.payload)!=null?a:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,a;this.api.onCancel?(a=(t=this.api).onCancel)==null||a.call(t):this.close()}onClosed(){var t,a;this.state.isOpen||(a=(t=this.api).onClosed)==null||a.call(t)}onConfirm(){var t,a;(a=(t=this.api).onConfirm)==null||a.call(t)}onOpened(){var t,a;this.state.isOpen&&((a=(t=this.api).onOpened)==null||a.call(t))}open(){this.store.setState(t=>ke(Q({},t),{isOpen:!0}))}setData(t){return this.sharedData.payload=t,this}setState(t){return Ia(t)?this.store.setState(t):this.store.setState(a=>Q(Q({},a),t)),this}unlock(){return this.lock(!1)}}const wa=Symbol("VBEN_DRAWER_INJECT"),Qs={};function Ya(l={}){var d;const{connectedComponent:t}=l;if(t){const f=ht({}),p=Z(!0),h=M((m,{attrs:v,slots:T})=>(mt(wa,{extendApi(D){Object.setPrototypeOf(f,D)},options:l,reCreateDrawer(){return G(this,null,function*(){p.value=!1,yield Ge(),p.value=!0})}}),er(f,Q(Q(Q({},m),v),T)),()=>ua(p.value?t:"div",Q(Q({},m),v),T)),{name:"VbenParentDrawer",inheritAttrs:!1});return Wo(()=>{var m;(m=f==null?void 0:f.close)==null||m.call(f)}),[h,f]}const a=bt(wa,{}),o=Q(Q(Q({},Qs),a.options),l);o.onOpenChange=f=>{var p,h,m;(p=l.onOpenChange)==null||p.call(l,f),(m=(h=a.options)==null?void 0:h.onOpenChange)==null||m.call(h,f)};const s=o.onClosed;o.onClosed=()=>{var f;s==null||s(),o.destroyOnClose&&((f=a.reCreateDrawer)==null||f.call(a))};const n=new Zs(o),i=n;i.useStore=f=>mn(n.store,f);const r=M((f,{attrs:p,slots:h})=>()=>ua(Ys,ke(Q(Q({},f),p),{drawerApi:i}),h),{name:"VbenDrawer",inheritAttrs:!1});return(d=a.extendApi)==null||d.call(a,i),[r,i]}function er(l,t){return G(this,null,function*(){var s;if(!t||Object.keys(t).length===0)return;yield Ge();const a=(s=l==null?void 0:l.store)==null?void 0:s.state;if(!a)return;const o=new Set(Object.keys(a));for(const n of Object.keys(t))o.has(n)&&!["class"].includes(n)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${n}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}const tr=M({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(l){const t=l,a=Re(),o=et(),s=w(()=>{const i=a.matched,r=[];for(const d of i){const{meta:f,path:p}=d,{hideChildrenInMenu:h,hideInBreadcrumb:m,icon:v,name:T,title:z}=f||{};m||h||!p||r.push({icon:v,path:p||a.path,title:z?g(z||T):""})}return t.showHome&&r.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),t.hideWhenOnlyOne&&r.length===1?[]:r});function n(i){o.push(i)}return(i,r)=>(u(),x(e(Ps),{breadcrumbs:s.value,"show-icon":i.showIcon,"style-type":i.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),ar=M({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(l){const t=l;let a=!1;const o=Z(""),s=Z(""),n=Z(),[i,r]=un({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=o.value,window.location.reload()}});function d(){return G(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const T=yield fetch(t.checkUpdateUrl,{cache:"no-cache",method:"HEAD"});return T.headers.get("etag")||T.headers.get("last-modified")}catch(T){return console.error("Failed to fetch version tag"),null}})}function f(){return G(this,null,function*(){const T=yield d();if(T){if(!s.value){s.value=T;return}s.value!==T&&T&&(clearInterval(n.value),p(T))}})}function p(T){o.value=T,r.open()}function h(){t.checkUpdatesInterval<=0||(n.value=setInterval(f,t.checkUpdatesInterval*60*1e3))}function m(){document.hidden?v():a||(a=!0,f().finally(()=>{a=!1,h()}))}function v(){clearInterval(n.value)}return Je(()=>{h(),document.addEventListener("visibilitychange",m)}),Pt(()=>{v(),document.removeEventListener("visibilitychange",m)}),(T,z)=>(u(),x(e(i),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.refresh"),"fullscreen-button":!1,title:e(g)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[E(V(e(g)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),lr={class:"flex flex-col py-4"},or={class:"mb-3 font-semibold leading-none tracking-tight"},Te=M({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(l){return(t,a)=>(u(),S("div",lr,[A("h3",or,V(t.title),1),_(t.$slots,"default")]))}}),nr={class:"flex items-center text-sm"},sr={key:0,class:"ml-auto mr-2 text-xs opacity-60"},ae=M({name:"PreferenceSwitchItem",__name:"switch-item",props:ge({disabled:{type:Boolean,default:!1},tip:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a=He();function o(){t.value=!t.value}return(s,n)=>(u(),S("div",{class:L([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:o},[A("span",nr,[_(s.$slots,"default"),e(a).tip||s.tip?(u(),x(e(qe),{key:0,side:"bottom"},{trigger:c(()=>[b(e(tt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[_(s.$slots,"tip",{},()=>[s.tip?(u(!0),S(ee,{key:0},be(s.tip.split(`
`),(i,r)=>(u(),S("p",{key:r},V(i),1))),128)):I("",!0)])]),_:3})):I("",!0)]),s.$slots.shortcut?(u(),S("span",sr,[_(s.$slots,"shortcut")])):I("",!0),b(e(us),{checked:t.value,"onUpdate:checked":n[0]||(n[0]=i=>t.value=i),onClick:n[1]||(n[1]=Pe(()=>{},["stop"]))},null,8,["checked"])],2))}}),rr={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},ir=["onClick"],dr=M({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(l){const t=y(l,"transitionProgress"),a=y(l,"transitionName"),o=y(l,"transitionEnable"),s=y(l,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function i(r){a.value=r}return(r,d)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=f=>t.value=f)},{default:c(()=>[E(V(e(g)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:s.value,"onUpdate:modelValue":d[1]||(d[1]=f=>s.value=f)},{default:c(()=>[E(V(e(g)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":d[2]||(d[2]=f=>o.value=f)},{default:c(()=>[E(V(e(g)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),o.value?(u(),S("div",rr,[(u(),S(ee,null,be(n,f=>A("div",{key:f,class:L([{"outline-box-active":a.value===f},"outline-box p-2"]),onClick:p=>i(f)},[A("div",{class:L([`${f}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,ir)),64))])):I("",!0)],64))}}),ur={class:"flex items-center text-sm"},gt=M({name:"PreferenceSelectItem",__name:"select-item",props:ge({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a=He();return(o,s)=>(u(),S("div",{class:L([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[A("span",ur,[_(o.$slots,"default"),e(a).tip?(u(),x(e(qe),{key:0,side:"bottom"},{trigger:c(()=>[b(e(tt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[_(o.$slots,"tip")]),_:3})):I("",!0)]),b(e(hn),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},{default:c(()=>[b(e(vn),{class:"h-8 w-[165px]"},{default:c(()=>[b(e(gn),{placeholder:o.placeholder},null,8,["placeholder"])]),_:1}),b(e(yn),null,{default:c(()=>[(u(!0),S(ee,null,be(o.items,n=>(u(),x(e(wn),{key:n.value,value:n.value},{default:c(()=>[E(V(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),cr=M({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(l){const t=y(l,"appLocale"),a=y(l,"appDynamicTitle"),o=y(l,"appWatermark"),s=y(l,"appEnableCheckUpdates");return(n,i)=>(u(),S(ee,null,[b(gt,{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=r=>t.value=r),items:e(go)},{default:c(()=>[E(V(e(g)("preferences.language")),1)]),_:1},8,["modelValue","items"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":i[1]||(i[1]=r=>a.value=r)},{default:c(()=>[E(V(e(g)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=r=>o.value=r)},{default:c(()=>[E(V(e(g)("preferences.watermark")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:s.value,"onUpdate:modelValue":i[3]||(i[3]=r=>s.value=r)},{default:c(()=>[E(V(e(g)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),pr={class:"text-sm"},Ut=M({name:"PreferenceToggleItem",__name:"toggle-item",props:ge({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=y(l,"modelValue");return(a,o)=>(u(),S("div",{class:L([{"pointer-events-none opacity-50":a.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[A("span",pr,[_(a.$slots,"default")]),b(e(Na),{modelValue:t.value,"onUpdate:modelValue":o[0]||(o[0]=s=>t.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(!0),S(ee,null,be(a.items,s=>(u(),x(e(Ra),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[E(V(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),fr=M({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:ge({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(l){const t=l,a=y(l,"breadcrumbEnable"),o=y(l,"breadcrumbShowIcon"),s=y(l,"breadcrumbStyleType"),n=y(l,"breadcrumbShowHome"),i=y(l,"breadcrumbHideOnlyOne"),r=[{label:g("preferences.normal"),value:"normal"},{label:g("preferences.breadcrumb.background"),value:"background"}],d=w(()=>!a.value||t.disabled);return(f,p)=>(u(),S(ee,null,[b(ae,{modelValue:a.value,"onUpdate:modelValue":p[0]||(p[0]=h=>a.value=h),disabled:f.disabled},{default:c(()=>[E(V(e(g)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:i.value,"onUpdate:modelValue":p[1]||(p[1]=h=>i.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":p[2]||(p[2]=h=>o.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:n.value,"onUpdate:modelValue":p[3]||(p[3]=h=>n.value=h),disabled:d.value||!o.value},{default:c(()=>[E(V(e(g)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),b(Ut,{modelValue:s.value,"onUpdate:modelValue":p[4]||(p[4]=h=>s.value=h),disabled:d.value,items:r},{default:c(()=>[E(V(e(g)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),br={},mr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function hr(l,t){return u(),S("svg",mr,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Za=Me(br,[["render",hr]]),vr={},gr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function yr(l,t){return u(),S("svg",gr,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const wr=Me(vr,[["render",yr]]),xr={},kr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Cr(l,t){return u(),S("svg",kr,t[0]||(t[0]=[A("g",null,[A("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),A("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),A("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),A("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const Sr=Me(xr,[["render",Cr]]),Tr={},Mr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Br(l,t){return u(),S("svg",Mr,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="35.14924" y="4.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="47.25735" y="4.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="59.23033" y="4.07319"></rect></g>',1)]))}const _r=Me(Tr,[["render",Br]]),$r={},Vr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Er(l,t){return u(),S("svg",Vr,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#b2b2b2" height="1.689" rx="1.395" stroke="null" width="6.52486" x="10.08168" y="3.50832"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="2.89362"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="2.89362"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="5.13843" rx="2" stroke="null" width="5.78397" x="1.5327" y="1.081"></rect><rect id="svg_5" fill="hsl(var(--primary))" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path></g>',1)]))}const Pr=Me($r,[["render",Er]]),Ir={},Lr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function zr(l,t){return u(),S("svg",Lr,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const Ar=Me(Ir,[["render",zr]]),Ur={},Or={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Hr(l,t){return u(),S("svg",Or,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const Dr=Me(Ur,[["render",Hr]]),Wr={},Nr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Rr(l,t){return u(),S("svg",Nr,t[0]||(t[0]=[Xe('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const Fr=Me(Wr,[["render",Rr]]),Kr=Za,Gr={class:"flex w-full gap-5"},jr=["onClick"],qr={class:"text-muted-foreground mt-2 text-center text-xs"},Jr=M({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a={compact:wr,wide:Kr},o=w(()=>[{name:g("preferences.wide"),type:"wide"},{name:g("preferences.compact"),type:"compact"}]);function s(n){return n===t.value?["outline-box-active"]:[]}return(n,i)=>(u(),S("div",Gr,[(u(!0),S(ee,null,be(o.value,r=>(u(),S("div",{key:r.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>t.value=r.type},[A("div",{class:L([s(r.type),"outline-box flex-center"])},[(u(),x($e(a[r.type])))],2),A("div",qr,V(r.name),1)],8,jr))),128))]))}}),Xr={class:"flex items-center text-sm"},ot=M({name:"PreferenceSelectItem",__name:"input-item",props:ge({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a=He();return(o,s)=>(u(),S("div",{class:L([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[A("span",Xr,[_(o.$slots,"default"),e(a).tip?(u(),x(e(qe),{key:0,side:"bottom"},{trigger:c(()=>[b(e(tt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[_(o.$slots,"tip")]),_:3})):I("",!0)]),b(e(yo),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),Yr=M({__name:"copyright",props:ge({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(l){const t=l,a=y(l,"copyrightEnable"),o=y(l,"copyrightDate"),s=y(l,"copyrightIcp"),n=y(l,"copyrightIcpLink"),i=y(l,"copyrightCompanyName"),r=y(l,"copyrightCompanySiteLink"),d=w(()=>t.disabled||!a.value);return(f,p)=>(u(),S(ee,null,[b(ae,{modelValue:a.value,"onUpdate:modelValue":p[0]||(p[0]=h=>a.value=h),disabled:f.disabled},{default:c(()=>[E(V(e(g)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),b(ot,{modelValue:i.value,"onUpdate:modelValue":p[1]||(p[1]=h=>i.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),b(ot,{modelValue:r.value,"onUpdate:modelValue":p[2]||(p[2]=h=>r.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),b(ot,{modelValue:o.value,"onUpdate:modelValue":p[3]||(p[3]=h=>o.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),b(ot,{modelValue:s.value,"onUpdate:modelValue":p[4]||(p[4]=h=>s.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),b(ot,{modelValue:n.value,"onUpdate:modelValue":p[5]||(p[5]=h=>n.value=h),disabled:d.value},{default:c(()=>[E(V(e(g)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Zr=M({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(l){const t=y(l,"footerEnable"),a=y(l,"footerFixed");return(o,s)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},{default:c(()=>[E(V(e(g)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":s[1]||(s[1]=n=>a.value=n),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Qr=M({__name:"header",props:ge({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{}}),emits:["update:headerEnable","update:headerMode","update:headerMenuAlign"],setup(l){const t=y(l,"headerEnable"),a=y(l,"headerMode"),o=y(l,"headerMenuAlign"),s=[{label:g("preferences.header.modeStatic"),value:"static"},{label:g("preferences.header.modeFixed"),value:"fixed"},{label:g("preferences.header.modeAuto"),value:"auto"},{label:g("preferences.header.modeAutoScroll"),value:"auto-scroll"}],n=[{label:g("preferences.header.menuAlignStart"),value:"start"},{label:g("preferences.header.menuAlignCenter"),value:"center"},{label:g("preferences.header.menuAlignEnd"),value:"end"}];return(i,r)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":r[0]||(r[0]=d=>t.value=d),disabled:i.disabled},{default:c(()=>[E(V(e(g)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),b(gt,{modelValue:a.value,"onUpdate:modelValue":r[1]||(r[1]=d=>a.value=d),disabled:!t.value,items:s},{default:c(()=>[E(V(e(g)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"]),b(Ut,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=d=>o.value=d),disabled:!t.value,items:n},{default:c(()=>[E(V(e(g)("preferences.header.menuAlign")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ei={class:"flex w-full flex-wrap gap-5"},ti=["onClick"],ai={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},li=M({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a={"full-content":Sr,"header-nav":Za,"mixed-nav":Ar,"sidebar-mixed-nav":Dr,"sidebar-nav":Fr,"header-mixed-nav":_r,"header-sidebar-nav":Pr},o=w(()=>[{name:g("preferences.vertical"),tip:g("preferences.verticalTip"),type:"sidebar-nav"},{name:g("preferences.twoColumn"),tip:g("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:g("preferences.horizontal"),tip:g("preferences.horizontalTip"),type:"header-nav"},{name:g("preferences.headerSidebarNav"),tip:g("preferences.headerSidebarNavTip"),type:"header-sidebar-nav"},{name:g("preferences.mixedMenu"),tip:g("preferences.mixedMenuTip"),type:"mixed-nav"},{name:g("preferences.headerTwoColumn"),tip:g("preferences.headerTwoColumnTip"),type:"header-mixed-nav"},{name:g("preferences.fullContent"),tip:g("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===t.value?["outline-box-active"]:[]}return(n,i)=>(u(),S("div",ei,[(u(!0),S(ee,null,be(o.value,r=>(u(),S("div",{key:r.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>t.value=r.type},[A("div",{class:L([s(r.type),"outline-box flex-center"])},[(u(),x($e(a[r.type])))],2),A("div",ai,[E(V(r.name)+" ",1),r.tip?(u(),x(e(qe),{key:0,side:"bottom"},{trigger:c(()=>[b(e(tt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(" "+V(r.tip),1)]),_:2},1024)):I("",!0)])],8,ti))),128))]))}}),oi=M({name:"PreferenceNavigationConfig",__name:"navigation",props:ge({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(l){const t=y(l,"navigationStyleType"),a=y(l,"navigationSplit"),o=y(l,"navigationAccordion"),s=[{label:g("preferences.rounded"),value:"rounded"},{label:g("preferences.plain"),value:"plain"}];return(n,i)=>(u(),S(ee,null,[b(Ut,{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=r=>t.value=r),disabled:n.disabled,items:s},{default:c(()=>[E(V(e(g)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":i[1]||(i[1]=r=>a.value=r),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[E(V(e(g)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[E(V(e(g)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=r=>o.value=r),disabled:n.disabled},{default:c(()=>[E(V(e(g)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ni={class:"flex items-center text-sm"},si=M({name:"PreferenceCheckboxItem",__name:"checkbox-item",props:ge({disabled:{type:Boolean,default:!1},items:{default:()=>[]},multiple:{type:Boolean,default:!1},onBtnClick:{type:Function,default:()=>{}},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a=He();return(o,s)=>(u(),S("div",{class:L([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[A("span",ni,[_(o.$slots,"default"),e(a).tip?(u(),x(e(qe),{key:0,side:"bottom"},{trigger:c(()=>[b(e(tt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[_(o.$slots,"tip")]),_:3})):I("",!0)]),b(e(ys),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n),class:"h-8 w-[165px]",options:o.items,disabled:o.disabled,multiple:o.multiple,onBtnClick:o.onBtnClick},null,8,["modelValue","options","disabled","multiple","onBtnClick"])],2))}}),ri={class:"flex items-center text-sm"},Qa=M({name:"PreferenceSelectItem",__name:"number-field-item",props:ge({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""},tip:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const t=y(l,"modelValue"),a=He();return(o,s)=>(u(),S("div",{class:L([{"hover:bg-accent":!e(a).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[A("span",ri,[_(o.$slots,"default"),e(a).tip||o.tip?(u(),x(e(qe),{key:0,side:"bottom"},{trigger:c(()=>[b(e(tt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[_(o.$slots,"tip",{},()=>[o.tip?(u(!0),S(ee,{key:0},be(o.tip.split(`
`),(n,i)=>(u(),S("p",{key:i},V(n),1))),128)):I("",!0)])]),_:3})):I("",!0)]),b(e(Xn),pe({modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},o.$attrs,{class:"w-[165px]"}),{default:c(()=>[b(e(Yn),null,{default:c(()=>[b(e(Zn)),b(e(es)),b(e(Qn))]),_:1})]),_:1},16,["modelValue"])],2))}}),ii=M({__name:"sidebar",props:ge({currentLayout:{},disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarButtons:{default:[]},sidebarButtonsModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarCollapsed","update:sidebarExpandOnHover","update:sidebarButtons","update:sidebarCollapsedButton","update:sidebarFixedButton"],setup(l){const t=y(l,"sidebarEnable"),a=y(l,"sidebarWidth"),o=y(l,"sidebarCollapsedShowTitle"),s=y(l,"sidebarAutoActivateChild"),n=y(l,"sidebarCollapsed"),i=y(l,"sidebarExpandOnHover"),r=y(l,"sidebarButtons"),d=y(l,"sidebarCollapsedButton"),f=y(l,"sidebarFixedButton");Je(()=>{d.value&&!r.value.includes("collapsed")&&r.value.push("collapsed"),f.value&&!r.value.includes("fixed")&&r.value.push("fixed")});const p=()=>{d.value=!!r.value.includes("collapsed"),f.value=!!r.value.includes("fixed")};return(h,m)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=v=>t.value=v),disabled:h.disabled},{default:c(()=>[E(V(e(g)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:n.value,"onUpdate:modelValue":m[1]||(m[1]=v=>n.value=v),disabled:!t.value||h.disabled},{default:c(()=>[E(V(e(g)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:i.value,"onUpdate:modelValue":m[2]||(m[2]=v=>i.value=v),disabled:!t.value||h.disabled||!n.value,tip:e(g)("preferences.sidebar.expandOnHoverTip")},{default:c(()=>[E(V(e(g)("preferences.sidebar.expandOnHover")),1)]),_:1},8,["modelValue","disabled","tip"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":m[3]||(m[3]=v=>o.value=v),disabled:!t.value||h.disabled||!n.value},{default:c(()=>[E(V(e(g)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=v=>s.value=v),disabled:!t.value||!["sidebar-mixed-nav","mixed-nav","header-mixed-nav"].includes(h.currentLayout)||h.disabled,tip:e(g)("preferences.sidebar.autoActivateChildTip")},{default:c(()=>[E(V(e(g)("preferences.sidebar.autoActivateChild")),1)]),_:1},8,["modelValue","disabled","tip"]),b(si,{items:[{label:e(g)("preferences.sidebar.buttonCollapsed"),value:"collapsed"},{label:e(g)("preferences.sidebar.buttonFixed"),value:"fixed"}],multiple:"",modelValue:r.value,"onUpdate:modelValue":m[5]||(m[5]=v=>r.value=v),"on-btn-click":p},{default:c(()=>[E(V(e(g)("preferences.sidebar.buttons")),1)]),_:1},8,["items","modelValue"]),b(Qa,{modelValue:a.value,"onUpdate:modelValue":m[6]||(m[6]=v=>a.value=v),disabled:!t.value||h.disabled,max:320,min:160,step:10},{default:c(()=>[E(V(e(g)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),di=M({name:"PreferenceTabsConfig",__name:"tabbar",props:ge({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarMaxCount","update:tabbarMiddleClickToClose"],setup(l){const t=y(l,"tabbarEnable"),a=y(l,"tabbarShowIcon"),o=y(l,"tabbarPersist"),s=y(l,"tabbarDraggable"),n=y(l,"tabbarWheelable"),i=y(l,"tabbarStyleType"),r=y(l,"tabbarShowMore"),d=y(l,"tabbarShowMaximize"),f=y(l,"tabbarMaxCount"),p=y(l,"tabbarMiddleClickToClose"),h=w(()=>[{label:g("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:g("preferences.tabbar.styleType.plain"),value:"plain"},{label:g("preferences.tabbar.styleType.card"),value:"card"},{label:g("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(m,v)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":v[0]||(v[0]=T=>t.value=T),disabled:m.disabled},{default:c(()=>[E(V(e(g)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":v[1]||(v[1]=T=>o.value=T),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),b(Qa,{modelValue:f.value,"onUpdate:modelValue":v[2]||(v[2]=T=>f.value=T),disabled:!t.value,max:30,min:0,step:5,tip:e(g)("preferences.tabbar.maxCountTip")},{default:c(()=>[E(V(e(g)("preferences.tabbar.maxCount")),1)]),_:1},8,["modelValue","disabled","tip"]),b(ae,{modelValue:s.value,"onUpdate:modelValue":v[3]||(v[3]=T=>s.value=T),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:n.value,"onUpdate:modelValue":v[4]||(v[4]=T=>n.value=T),disabled:!t.value,tip:e(g)("preferences.tabbar.wheelableTip")},{default:c(()=>[E(V(e(g)("preferences.tabbar.wheelable")),1)]),_:1},8,["modelValue","disabled","tip"]),b(ae,{modelValue:p.value,"onUpdate:modelValue":v[5]||(v[5]=T=>p.value=T),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.middleClickClose")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":v[6]||(v[6]=T=>a.value=T),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:r.value,"onUpdate:modelValue":v[7]||(v[7]=T=>r.value=T),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:d.value,"onUpdate:modelValue":v[8]||(v[8]=T=>d.value=T),disabled:!t.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),b(gt,{modelValue:i.value,"onUpdate:modelValue":v[9]||(v[9]=T=>i.value=T),items:h.value},{default:c(()=>[E(V(e(g)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),ui=M({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(l){const t=y(l,"widgetGlobalSearch"),a=y(l,"widgetFullscreen"),o=y(l,"widgetLanguageToggle"),s=y(l,"widgetNotification"),n=y(l,"widgetThemeToggle"),i=y(l,"widgetSidebarToggle"),r=y(l,"widgetLockScreen"),d=y(l,"appPreferencesButtonPosition"),f=y(l,"widgetRefresh"),p=w(()=>[{label:g("preferences.position.auto"),value:"auto"},{label:g("preferences.position.header"),value:"header"},{label:g("preferences.position.fixed"),value:"fixed"}]);return(h,m)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=v=>t.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:n.value,"onUpdate:modelValue":m[1]||(m[1]=v=>n.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":m[2]||(m[2]=v=>o.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":m[3]||(m[3]=v=>a.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=v=>s.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:r.value,"onUpdate:modelValue":m[5]||(m[5]=v=>r.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:i.value,"onUpdate:modelValue":m[6]||(m[6]=v=>i.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:f.value,"onUpdate:modelValue":m[7]||(m[7]=v=>f.value=v)},{default:c(()=>[E(V(e(g)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),b(gt,{modelValue:d.value,"onUpdate:modelValue":m[8]||(m[8]=v=>d.value=v),items:p.value},{default:c(()=>[E(V(e(g)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),ci=M({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(l){const t=y(l,"shortcutKeysEnable"),a=y(l,"shortcutKeysGlobalSearch"),o=y(l,"shortcutKeysLogout"),s=y(l,"shortcutKeysLockScreen"),n=w(()=>ca()?"Alt":"⌥");return(i,r)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":r[0]||(r[0]=d=>t.value=d)},{default:c(()=>[E(V(e(g)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":r[1]||(r[1]=d=>a.value=d),disabled:!t.value},{shortcut:c(()=>[E(V(e(ca)()?"Ctrl":"⌘")+" ",1),r[4]||(r[4]=A("kbd",null," K ",-1))]),default:c(()=>[E(V(e(g)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=d=>o.value=d),disabled:!t.value},{shortcut:c(()=>[E(V(n.value)+" Q ",1)]),default:c(()=>[E(V(e(g)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=d=>s.value=d),disabled:!t.value},{shortcut:c(()=>[E(V(n.value)+" L ",1)]),default:c(()=>[E(V(e(g)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),pi={class:"flex w-full flex-wrap justify-between"},fi=["onClick"],bi={class:"flex-center relative size-5 rounded-sm"},mi=["value"],hi={class:"text-muted-foreground my-2 text-center text-xs"},vi=M({name:"PreferenceBuiltinTheme",__name:"builtin",props:ge({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(l){const t=l,a=Z(),o=y(l,"modelValue"),s=y(l,"themeColorPrimary"),n=Et(m=>{s.value=m},300,!0,!0),i=w(()=>new No(s.value||"").toHexString()),r=w(()=>[...Ro]);function d(m){switch(m){case"custom":return g("preferences.theme.builtin.custom");case"deep-blue":return g("preferences.theme.builtin.deepBlue");case"deep-green":return g("preferences.theme.builtin.deepGreen");case"default":return g("preferences.theme.builtin.default");case"gray":return g("preferences.theme.builtin.gray");case"green":return g("preferences.theme.builtin.green");case"neutral":return g("preferences.theme.builtin.neutral");case"orange":return g("preferences.theme.builtin.orange");case"pink":return g("preferences.theme.builtin.pink");case"rose":return g("preferences.theme.builtin.rose");case"sky-blue":return g("preferences.theme.builtin.skyBlue");case"slate":return g("preferences.theme.builtin.slate");case"violet":return g("preferences.theme.builtin.violet");case"yellow":return g("preferences.theme.builtin.yellow");case"zinc":return g("preferences.theme.builtin.zinc")}}function f(m){o.value=m.type}function p(m){const v=m.target;n(Fo(v.value))}function h(){var m,v,T;(T=(v=(m=a.value)==null?void 0:m[0])==null?void 0:v.click)==null||T.call(v)}return ye(()=>[o.value,t.isDark],([m,v])=>{const T=r.value.find(z=>z.type===m);if(T){const z=v&&T.darkPrimaryColor||T.primaryColor;s.value=z||T.color}}),(m,v)=>(u(),S("div",pi,[(u(!0),S(ee,null,be(r.value,T=>(u(),S("div",{key:T.type,class:"flex cursor-pointer flex-col",onClick:z=>f(T)},[A("div",{class:L([{"outline-box-active":T.type===o.value},"outline-box flex-center group cursor-pointer"])},[T.type!=="custom"?(u(),S("div",{key:0,style:he({backgroundColor:T.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(u(),S("div",{key:1,class:"size-full px-10 py-2",onClick:Pe(h,["stop"])},[A("div",bi,[b(e(zn),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),A("input",{ref_for:!0,ref_key:"colorInput",ref:a,value:i.value,class:"absolute inset-0 opacity-0",type:"color",onInput:p},null,40,mi)])]))],2),A("div",hi,V(d(T.type)),1)],8,fi))),128))]))}}),gi=M({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(l){const t=y(l,"appColorWeakMode"),a=y(l,"appColorGrayMode");return(o,s)=>(u(),S(ee,null,[b(ae,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n)},{default:c(()=>[E(V(e(g)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),b(ae,{modelValue:a.value,"onUpdate:modelValue":s[1]||(s[1]=n=>a.value=n)},{default:c(()=>[E(V(e(g)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),yi=M({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(l){const t=y(l,"themeRadius"),a=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(o,s)=>(u(),x(e(Na),{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=n=>t.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(),S(ee,null,be(a,n=>b(e(Ra),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[E(V(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),wi={class:"flex w-full flex-wrap justify-between"},xi=["onClick"],ki={class:"text-muted-foreground mt-2 text-center text-xs"},Ci=M({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(l){const t=y(l,"modelValue"),a=y(l,"themeSemiDarkSidebar"),o=y(l,"themeSemiDarkHeader"),s=[{icon:nn,name:"light"},{icon:sn,name:"dark"},{icon:rn,name:"auto"}];function n(r){return r===t.value?["outline-box-active"]:[]}function i(r){switch(r){case"auto":return g("preferences.followSystem");case"dark":return g("preferences.theme.dark");case"light":return g("preferences.theme.light")}}return(r,d)=>(u(),S("div",wi,[(u(),S(ee,null,be(s,f=>A("div",{key:f.name,class:"flex cursor-pointer flex-col",onClick:p=>t.value=f.name},[A("div",{class:L([n(f.name),"outline-box flex-center py-4"])},[(u(),x($e(f.icon),{class:"mx-9 size-5"}))],2),A("div",ki,V(i(f.name)),1)],8,xi)),64)),b(ae,{modelValue:a.value,"onUpdate:modelValue":d[0]||(d[0]=f=>a.value=f),disabled:t.value==="dark",class:"mt-6"},{default:c(()=>[E(V(e(g)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),b(ae,{modelValue:o.value,"onUpdate:modelValue":d[1]||(d[1]=f=>o.value=f),disabled:t.value==="dark"},{default:c(()=>[E(V(e(g)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),Si={class:"flex items-center"},Ti={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Mi={class:"p-1"},Bi=M({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:ge(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarExpandOnHover","update:sidebarCollapsedButton","update:sidebarFixedButton","update:headerEnable","update:headerMode","update:headerMenuAlign","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarMaxCount","update:tabbarMiddleClickToClose","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(l,{emit:t}){const a=t,o=Ba.getMessage(),s=y(l,"appLocale"),n=y(l,"appDynamicTitle"),i=y(l,"appLayout"),r=y(l,"appColorGrayMode"),d=y(l,"appColorWeakMode"),f=y(l,"appContentCompact"),p=y(l,"appWatermark"),h=y(l,"appEnableCheckUpdates"),m=y(l,"appPreferencesButtonPosition"),v=y(l,"transitionProgress"),T=y(l,"transitionName"),z=y(l,"transitionLoading"),D=y(l,"transitionEnable"),$=y(l,"themeColorPrimary"),O=y(l,"themeBuiltinType"),F=y(l,"themeMode"),P=y(l,"themeRadius"),K=y(l,"themeSemiDarkSidebar"),j=y(l,"themeSemiDarkHeader"),q=y(l,"sidebarEnable"),B=y(l,"sidebarWidth"),R=y(l,"sidebarCollapsed"),J=y(l,"sidebarCollapsedShowTitle"),se=y(l,"sidebarAutoActivateChild"),ue=y(l,"sidebarExpandOnHover"),we=y(l,"sidebarCollapsedButton"),W=y(l,"sidebarFixedButton"),Y=y(l,"headerEnable"),ve=y(l,"headerMode"),Ce=y(l,"headerMenuAlign"),Se=y(l,"breadcrumbEnable"),Be=y(l,"breadcrumbShowIcon"),Ee=y(l,"breadcrumbShowHome"),N=y(l,"breadcrumbStyleType"),le=y(l,"breadcrumbHideOnlyOne"),oe=y(l,"tabbarEnable"),ne=y(l,"tabbarShowIcon"),ie=y(l,"tabbarShowMore"),ce=y(l,"tabbarShowMaximize"),De=y(l,"tabbarPersist"),H=y(l,"tabbarDraggable"),te=y(l,"tabbarWheelable"),de=y(l,"tabbarStyleType"),X=y(l,"tabbarMaxCount"),fe=y(l,"tabbarMiddleClickToClose"),We=y(l,"navigationStyleType"),Fe=y(l,"navigationSplit"),Ae=y(l,"navigationAccordion"),yt=y(l,"footerEnable"),Dt=y(l,"footerFixed"),ul=y(l,"copyrightSettingShow"),Wt=y(l,"copyrightEnable"),Nt=y(l,"copyrightCompanyName"),Rt=y(l,"copyrightCompanySiteLink"),Ft=y(l,"copyrightDate"),Kt=y(l,"copyrightIcp"),Gt=y(l,"copyrightIcpLink"),jt=y(l,"shortcutKeysEnable"),qt=y(l,"shortcutKeysGlobalSearch"),Jt=y(l,"shortcutKeysGlobalLogout"),Xt=y(l,"shortcutKeysGlobalLockScreen"),Yt=y(l,"widgetGlobalSearch"),Zt=y(l,"widgetFullscreen"),Qt=y(l,"widgetLanguageToggle"),ea=y(l,"widgetNotification"),ta=y(l,"widgetThemeToggle"),aa=y(l,"widgetSidebarToggle"),la=y(l,"widgetLockScreen"),oa=y(l,"widgetRefresh"),{diffPreference:Ye,isDark:cl,isFullContent:wt,isHeaderNav:pl,isHeaderSidebarNav:fl,isMixedNav:na,isSideMixedNav:bl,isSideMode:ml,isSideNav:hl}=it(),{copy:vl}=Ko({legacy:!0}),[gl]=Ya(),sa=Z("appearance"),yl=w(()=>[{label:g("preferences.appearance"),value:"appearance"},{label:g("preferences.layout"),value:"layout"},{label:g("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:g("preferences.general"),value:"general"}]),wl=w(()=>!wt.value&&!na.value&&!pl.value&&U.header.enable);function xl(){return G(this,null,function*(){var xt;yield vl(JSON.stringify(Ye.value,null,2)),(xt=o.copyPreferencesSuccess)==null||xt.call(o,g("preferences.copyPreferencesSuccessTitle"),g("preferences.copyPreferencesSuccess"))})}function kl(){return G(this,null,function*(){pa(),Go(),a("clearPreferencesAndLogout")})}function Cl(){return G(this,null,function*(){Ye.value&&(pa(),yield Va(U.app.locale))})}return(xt,k)=>(u(),S("div",null,[b(e(gl),{description:e(g)("preferences.subtitle"),title:e(g)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[A("div",Si,[b(e(Mt),{disabled:!e(Ye),tooltip:e(g)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(Ye)?(u(),S("span",Ti)):I("",!0),b(e(Lt),{class:"size-4",onClick:Cl})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[b(e(Ze),{disabled:!e(Ye),class:"mx-4 w-full",size:"sm",variant:"default",onClick:xl},{default:c(()=>[b(e(Vn),{class:"mr-2 size-3"}),E(" "+V(e(g)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),b(e(Ze),{disabled:!e(Ye),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:kl},{default:c(()=>[E(V(e(g)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[A("div",Mi,[b(e(qs),{modelValue:sa.value,"onUpdate:modelValue":k[68]||(k[68]=C=>sa.value=C),tabs:yl.value},{general:c(()=>[b(e(Te),{title:e(g)("preferences.general")},{default:c(()=>[b(e(cr),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":k[0]||(k[0]=C=>n.value=C),"app-enable-check-updates":h.value,"onUpdate:appEnableCheckUpdates":k[1]||(k[1]=C=>h.value=C),"app-locale":s.value,"onUpdate:appLocale":k[2]||(k[2]=C=>s.value=C),"app-watermark":p.value,"onUpdate:appWatermark":k[3]||(k[3]=C=>p.value=C)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.animation.title")},{default:c(()=>[b(e(dr),{"transition-enable":D.value,"onUpdate:transitionEnable":k[4]||(k[4]=C=>D.value=C),"transition-loading":z.value,"onUpdate:transitionLoading":k[5]||(k[5]=C=>z.value=C),"transition-name":T.value,"onUpdate:transitionName":k[6]||(k[6]=C=>T.value=C),"transition-progress":v.value,"onUpdate:transitionProgress":k[7]||(k[7]=C=>v.value=C)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[b(e(Te),{title:e(g)("preferences.theme.title")},{default:c(()=>[b(e(Ci),{modelValue:F.value,"onUpdate:modelValue":k[8]||(k[8]=C=>F.value=C),"theme-semi-dark-header":j.value,"onUpdate:themeSemiDarkHeader":k[9]||(k[9]=C=>j.value=C),"theme-semi-dark-sidebar":K.value,"onUpdate:themeSemiDarkSidebar":k[10]||(k[10]=C=>K.value=C)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.theme.builtin.title")},{default:c(()=>[b(e(vi),{modelValue:O.value,"onUpdate:modelValue":k[11]||(k[11]=C=>O.value=C),"theme-color-primary":$.value,"onUpdate:themeColorPrimary":k[12]||(k[12]=C=>$.value=C),"is-dark":e(cl)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.theme.radius")},{default:c(()=>[b(e(yi),{modelValue:P.value,"onUpdate:modelValue":k[13]||(k[13]=C=>P.value=C)},null,8,["modelValue"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.other")},{default:c(()=>[b(e(gi),{"app-color-gray-mode":r.value,"onUpdate:appColorGrayMode":k[14]||(k[14]=C=>r.value=C),"app-color-weak-mode":d.value,"onUpdate:appColorWeakMode":k[15]||(k[15]=C=>d.value=C)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[b(e(Te),{title:e(g)("preferences.layout")},{default:c(()=>[b(e(li),{modelValue:i.value,"onUpdate:modelValue":k[16]||(k[16]=C=>i.value=C)},null,8,["modelValue"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.content")},{default:c(()=>[b(e(Jr),{modelValue:f.value,"onUpdate:modelValue":k[17]||(k[17]=C=>f.value=C)},null,8,["modelValue"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.sidebar.title")},{default:c(()=>[b(e(ii),{"sidebar-auto-activate-child":se.value,"onUpdate:sidebarAutoActivateChild":k[18]||(k[18]=C=>se.value=C),"sidebar-collapsed":R.value,"onUpdate:sidebarCollapsed":k[19]||(k[19]=C=>R.value=C),"sidebar-collapsed-show-title":J.value,"onUpdate:sidebarCollapsedShowTitle":k[20]||(k[20]=C=>J.value=C),"sidebar-enable":q.value,"onUpdate:sidebarEnable":k[21]||(k[21]=C=>q.value=C),"sidebar-expand-on-hover":ue.value,"onUpdate:sidebarExpandOnHover":k[22]||(k[22]=C=>ue.value=C),"sidebar-width":B.value,"onUpdate:sidebarWidth":k[23]||(k[23]=C=>B.value=C),"sidebar-collapsed-button":we.value,"onUpdate:sidebarCollapsedButton":k[24]||(k[24]=C=>we.value=C),"sidebar-fixed-button":W.value,"onUpdate:sidebarFixedButton":k[25]||(k[25]=C=>W.value=C),"current-layout":i.value,disabled:!e(ml)},null,8,["sidebar-auto-activate-child","sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-width","sidebar-collapsed-button","sidebar-fixed-button","current-layout","disabled"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.header.title")},{default:c(()=>[b(e(Qr),{"header-enable":Y.value,"onUpdate:headerEnable":k[26]||(k[26]=C=>Y.value=C),"header-menu-align":Ce.value,"onUpdate:headerMenuAlign":k[27]||(k[27]=C=>Ce.value=C),"header-mode":ve.value,"onUpdate:headerMode":k[28]||(k[28]=C=>ve.value=C),disabled:e(wt)},null,8,["header-enable","header-menu-align","header-mode","disabled"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.navigationMenu.title")},{default:c(()=>[b(e(oi),{"navigation-accordion":Ae.value,"onUpdate:navigationAccordion":k[29]||(k[29]=C=>Ae.value=C),"navigation-split":Fe.value,"onUpdate:navigationSplit":k[30]||(k[30]=C=>Fe.value=C),"navigation-style-type":We.value,"onUpdate:navigationStyleType":k[31]||(k[31]=C=>We.value=C),disabled:e(wt),"disabled-navigation-split":!e(na)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.breadcrumb.title")},{default:c(()=>[b(e(fr),{"breadcrumb-enable":Se.value,"onUpdate:breadcrumbEnable":k[32]||(k[32]=C=>Se.value=C),"breadcrumb-hide-only-one":le.value,"onUpdate:breadcrumbHideOnlyOne":k[33]||(k[33]=C=>le.value=C),"breadcrumb-show-home":Ee.value,"onUpdate:breadcrumbShowHome":k[34]||(k[34]=C=>Ee.value=C),"breadcrumb-show-icon":Be.value,"onUpdate:breadcrumbShowIcon":k[35]||(k[35]=C=>Be.value=C),"breadcrumb-style-type":N.value,"onUpdate:breadcrumbStyleType":k[36]||(k[36]=C=>N.value=C),disabled:!wl.value||!(e(hl)||e(bl)||e(fl))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.tabbar.title")},{default:c(()=>[b(e(di),{"tabbar-draggable":H.value,"onUpdate:tabbarDraggable":k[37]||(k[37]=C=>H.value=C),"tabbar-enable":oe.value,"onUpdate:tabbarEnable":k[38]||(k[38]=C=>oe.value=C),"tabbar-persist":De.value,"onUpdate:tabbarPersist":k[39]||(k[39]=C=>De.value=C),"tabbar-show-icon":ne.value,"onUpdate:tabbarShowIcon":k[40]||(k[40]=C=>ne.value=C),"tabbar-show-maximize":ce.value,"onUpdate:tabbarShowMaximize":k[41]||(k[41]=C=>ce.value=C),"tabbar-show-more":ie.value,"onUpdate:tabbarShowMore":k[42]||(k[42]=C=>ie.value=C),"tabbar-style-type":de.value,"onUpdate:tabbarStyleType":k[43]||(k[43]=C=>de.value=C),"tabbar-wheelable":te.value,"onUpdate:tabbarWheelable":k[44]||(k[44]=C=>te.value=C),"tabbar-max-count":X.value,"onUpdate:tabbarMaxCount":k[45]||(k[45]=C=>X.value=C),"tabbar-middle-click-to-close":fe.value,"onUpdate:tabbarMiddleClickToClose":k[46]||(k[46]=C=>fe.value=C)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type","tabbar-wheelable","tabbar-max-count","tabbar-middle-click-to-close"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.widget.title")},{default:c(()=>[b(e(ui),{"app-preferences-button-position":m.value,"onUpdate:appPreferencesButtonPosition":k[47]||(k[47]=C=>m.value=C),"widget-fullscreen":Zt.value,"onUpdate:widgetFullscreen":k[48]||(k[48]=C=>Zt.value=C),"widget-global-search":Yt.value,"onUpdate:widgetGlobalSearch":k[49]||(k[49]=C=>Yt.value=C),"widget-language-toggle":Qt.value,"onUpdate:widgetLanguageToggle":k[50]||(k[50]=C=>Qt.value=C),"widget-lock-screen":la.value,"onUpdate:widgetLockScreen":k[51]||(k[51]=C=>la.value=C),"widget-notification":ea.value,"onUpdate:widgetNotification":k[52]||(k[52]=C=>ea.value=C),"widget-refresh":oa.value,"onUpdate:widgetRefresh":k[53]||(k[53]=C=>oa.value=C),"widget-sidebar-toggle":aa.value,"onUpdate:widgetSidebarToggle":k[54]||(k[54]=C=>aa.value=C),"widget-theme-toggle":ta.value,"onUpdate:widgetThemeToggle":k[55]||(k[55]=C=>ta.value=C)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),b(e(Te),{title:e(g)("preferences.footer.title")},{default:c(()=>[b(e(Zr),{"footer-enable":yt.value,"onUpdate:footerEnable":k[56]||(k[56]=C=>yt.value=C),"footer-fixed":Dt.value,"onUpdate:footerFixed":k[57]||(k[57]=C=>Dt.value=C)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),ul.value?(u(),x(e(Te),{key:0,title:e(g)("preferences.copyright.title")},{default:c(()=>[b(e(Yr),{"copyright-company-name":Nt.value,"onUpdate:copyrightCompanyName":k[58]||(k[58]=C=>Nt.value=C),"copyright-company-site-link":Rt.value,"onUpdate:copyrightCompanySiteLink":k[59]||(k[59]=C=>Rt.value=C),"copyright-date":Ft.value,"onUpdate:copyrightDate":k[60]||(k[60]=C=>Ft.value=C),"copyright-enable":Wt.value,"onUpdate:copyrightEnable":k[61]||(k[61]=C=>Wt.value=C),"copyright-icp":Kt.value,"onUpdate:copyrightIcp":k[62]||(k[62]=C=>Kt.value=C),"copyright-icp-link":Gt.value,"onUpdate:copyrightIcpLink":k[63]||(k[63]=C=>Gt.value=C),disabled:!yt.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):I("",!0)]),shortcutKey:c(()=>[b(e(Te),{title:e(g)("preferences.shortcutKeys.global")},{default:c(()=>[b(e(ci),{"shortcut-keys-enable":jt.value,"onUpdate:shortcutKeysEnable":k[64]||(k[64]=C=>jt.value=C),"shortcut-keys-global-search":qt.value,"onUpdate:shortcutKeysGlobalSearch":k[65]||(k[65]=C=>qt.value=C),"shortcut-keys-lock-screen":Xt.value,"onUpdate:shortcutKeysLockScreen":k[66]||(k[66]=C=>Xt.value=C),"shortcut-keys-logout":Jt.value,"onUpdate:shortcutKeysLogout":k[67]||(k[67]=C=>Jt.value=C)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),_i=M({__name:"preferences",setup(l){const[t]=Ya({connectedComponent:Bi}),a=w(()=>{const s={};for(const[n,i]of Object.entries(U))for(const[r,d]of Object.entries(i))s[`${n}${fa(r)}`]=d;return s}),o=w(()=>{const s={};for(const[n,i]of Object.entries(U))if(typeof i=="object")for(const r of Object.keys(i))s[`update:${n}${fa(r)}`]=d=>{Ke({[n]:{[r]:d}}),n==="app"&&r==="locale"&&Va(d)};else s[n]=i;return s});return(s,n)=>(u(),S("div",null,[b(e(t),pe(Q(Q({},s.$attrs),a.value),La(o.value)),null,16)]))}}),$i=M({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(l){const t=l,{contentElement:a,overlayStyle:o}=wo(),s=w(()=>{const{contentCompact:n,padding:i,paddingBottom:r,paddingLeft:d,paddingRight:f,paddingTop:p}=t,h=n==="compact"?{margin:"0 auto",width:`${t.contentCompactWidth}px`}:{};return ke(Q({},h),{flex:1,padding:`${i}px`,paddingBottom:`${r}px`,paddingLeft:`${d}px`,paddingRight:`${f}px`,paddingTop:`${p}px`})});return(n,i)=>(u(),S("main",{ref_key:"contentElement",ref:a,style:he(s.value),class:"bg-background-deep relative"},[b(e(xo),{style:he(e(o))},{default:c(()=>[_(n.$slots,"overlay")]),_:3},8,["style"]),_(n.$slots,"default")],4))}}),xa=M({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(l){const t=y(l,"collapsed");function a(){t.value=!t.value}return(o,s)=>(u(),S("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:Pe(a,["stop"])},[t.value?(u(),x(e(ko),{key:0,class:"size-4"})):(u(),x(e(Co),{key:1,class:"size-4"}))]))}}),ka=M({__name:"sidebar-fixed-button",setup(l){return(t,a)=>null}}),Vi=M({__name:"layout-sidebar",props:ge({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},showFixedButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:ge(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(l,{emit:t}){const a=l,o=t,s=y(l,"collapse"),n=y(l,"extraCollapse"),i=y(l,"expandOnHovering"),r=y(l,"expandOnHover"),d=y(l,"extraVisible"),f=jo(document.body),p=He(),h=Bt(),m=w(()=>K(!0)),v=w(()=>{const{isSidebarMixed:B,marginTop:R,paddingTop:J,zIndex:se}=a;return Q(ke(Q({"--scroll-shadow":"var(--sidebar)"},K(!1)),{height:`calc(100% - ${R}px)`,marginTop:`${R}px`,paddingTop:`${J}px`,zIndex:se}),B&&d.value?{transition:"none"}:{})}),T=w(()=>{const{extraWidth:B,show:R,width:J,zIndex:se}=a;return{left:`${J}px`,width:d.value&&R?`${B}px`:0,zIndex:se}}),z=w(()=>{const{headerHeight:B}=a;return{height:`${B-1}px`}}),D=w(()=>{const{collapseWidth:B,fixedExtra:R,isSidebarMixed:J,mixedWidth:se}=a;return J&&R?{width:`${s.value?B:se}px`}:{}}),$=w(()=>{const{collapseHeight:B,headerHeight:R}=a;return Q({height:`calc(100% - ${R+B}px)`,paddingTop:"8px"},D.value)}),O=w(()=>{const{headerHeight:B,isSidebarMixed:R}=a;return Q(ke(Q({},R?{display:"flex",justifyContent:"center"}:{}),{height:`${B-1}px`}),D.value)}),F=w(()=>{const{collapseHeight:B,headerHeight:R}=a;return{height:`calc(100% - ${R+B}px)`}}),P=w(()=>({height:`${a.collapseHeight}px`}));za(()=>{d.value=a.fixedExtra?!0:d.value});function K(B){const{extraWidth:R,fixedExtra:J,isSidebarMixed:se,show:ue,width:we}=a;let W=we===0?"0px":`${we+(se&&J&&d.value?R:0)}px`;const{collapseWidth:Y}=a;return B&&i.value&&!r.value&&(W=`${Y}px`),ke(Q({},W==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${W}`,marginLeft:ue?0:`-${W}`,maxWidth:W,minWidth:W,width:W})}function j(B){(B==null?void 0:B.offsetX)<10||r.value||(i.value||(s.value=!1),a.isSidebarMixed&&(f.value=!0),i.value=!0)}function q(){o("leave"),a.isSidebarMixed&&(f.value=!1),!r.value&&(i.value=!1,s.value=!0,d.value=!1)}return(B,R)=>(u(),S(ee,null,[B.domVisible?(u(),S("div",{key:0,class:L([B.theme,"h-full transition-all duration-150"]),style:he(m.value)},null,6)):I("",!0),A("aside",{class:L([[B.theme,{"bg-sidebar-deep":B.isSidebarMixed,"bg-sidebar border-border border-r":!B.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:he(v.value),onMouseenter:j,onMouseleave:q},[!s.value&&!B.isSidebarMixed&&B.showFixedButton?(u(),x(e(ka),{key:0,"expand-on-hover":r.value,"onUpdate:expandOnHover":R[0]||(R[0]=J=>r.value=J)},null,8,["expand-on-hover"])):I("",!0),e(p).logo?(u(),S("div",{key:1,style:he(O.value)},[_(B.$slots,"logo")],4)):I("",!0),b(e($t),{style:he($.value),shadow:"","shadow-border":""},{default:c(()=>[_(B.$slots,"default")]),_:3},8,["style"]),A("div",{style:he(P.value)},null,4),B.showCollapseButton&&!B.isSidebarMixed?(u(),x(e(xa),{key:2,collapsed:s.value,"onUpdate:collapsed":R[1]||(R[1]=J=>s.value=J)},null,8,["collapsed"])):I("",!0),B.isSidebarMixed?(u(),S("div",{key:3,ref_key:"asideRef",ref:h,class:L([{"border-l":d.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:he(T.value)},[B.isSidebarMixed&&r.value?(u(),x(e(xa),{key:0,collapsed:n.value,"onUpdate:collapsed":R[2]||(R[2]=J=>n.value=J)},null,8,["collapsed"])):I("",!0),n.value?I("",!0):(u(),x(e(ka),{key:1,"expand-on-hover":r.value,"onUpdate:expandOnHover":R[3]||(R[3]=J=>r.value=J)},null,8,["expand-on-hover"])),n.value?I("",!0):(u(),S("div",{key:2,style:he(z.value),class:"pl-2"},[_(B.$slots,"extra-title")],4)),b(e($t),{style:he(F.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[_(B.$slots,"extra")]),_:3},8,["style"])],6)):I("",!0)],38)],64))}});function Ei(l){const t=w(()=>l.isMobile?"sidebar-nav":l.layout),a=w(()=>t.value==="full-content"),o=w(()=>t.value==="sidebar-mixed-nav"),s=w(()=>t.value==="header-nav"),n=w(()=>t.value==="mixed-nav"||t.value==="header-sidebar-nav"),i=w(()=>t.value==="header-mixed-nav");return{currentLayout:t,isFullContent:a,isHeaderMixedNav:i,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:o}}const Pi={class:"relative flex min-h-full w-full"},Ii=M({name:"VbenLayout",__name:"vben-layout",props:ge({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:-40},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!1},headerVisible:{type:Boolean,default:!1},isMobile:{type:Boolean,default:!0},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapsedButton:{type:Boolean,default:!0},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarFixedButton:{type:Boolean,default:!0},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!1},tabbarHeight:{default:0},zIndex:{default:200}},{sidebarCollapse:{type:Boolean,default:!1},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean,default:!1},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean,default:!1},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:ge(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(l,{emit:t}){const a=l,o=t,s=y(l,"sidebarCollapse"),n=y(l,"sidebarExtraVisible"),i=y(l,"sidebarExtraCollapse"),r=y(l,"sidebarExpandOnHover"),d=y(l,"sidebarEnable"),f=Z(!1),p=Z(!1),h=Z(),{arrivedState:m,directions:v,isScrolling:T,y:z}=qo(document),{setLayoutHeaderHeight:D}=So(),{setLayoutFooterHeight:$}=To(),{y:O}=Jo({target:h,type:"client"}),{currentLayout:F,isFullContent:P,isHeaderMixedNav:K,isHeaderNav:j,isMixedNav:q,isSidebarMixedNav:B}=Ei(a),R=w(()=>a.headerMode==="auto"),J=w(()=>{let H=0;return a.headerVisible&&!a.headerHidden&&(H+=a.headerHeight),a.tabbarEnable&&(H+=a.tabbarHeight),H}),se=w(()=>{const{sidebarCollapseShowTitle:H,sidebarMixedWidth:te,sideCollapseWidth:de}=a;return H||B.value||K.value?te:de}),ue=w(()=>!j.value&&d.value),we=w(()=>{const{headerHeight:H,isMobile:te}=a;return q.value&&!te?H:0}),W=w(()=>{const{isMobile:H,sidebarHidden:te,sidebarMixedWidth:de,sidebarWidth:X}=a;let fe=0;return te||!ue.value||te&&!B.value&&!q.value&&!K.value||((K.value||B.value)&&!H?fe=de:s.value?fe=H?0:se.value:fe=X),fe}),Y=w(()=>{const{sidebarExtraCollapsedWidth:H,sidebarWidth:te}=a;return i.value?H:te}),ve=w(()=>F.value==="mixed-nav"||F.value==="sidebar-mixed-nav"||F.value==="sidebar-nav"||F.value==="header-mixed-nav"||F.value==="header-sidebar-nav"),Ce=w(()=>{const{headerMode:H}=a;return q.value||H==="fixed"||H==="auto-scroll"||H==="auto"}),Se=w(()=>ve.value&&d.value&&!a.sidebarHidden),Be=w(()=>!s.value&&a.isMobile),Ee=w(()=>{let H="100%",te="unset";if(Ce.value&&F.value!=="header-nav"&&F.value!=="mixed-nav"&&F.value!=="header-sidebar-nav"&&Se.value&&!a.isMobile)if((B.value||K.value)&&r.value&&n.value){const X=s.value?se.value:a.sidebarMixedWidth,fe=i.value?a.sidebarExtraCollapsedWidth:a.sidebarWidth;te=`${X+fe}px`,H=`calc(100% - ${te})`}else te=f.value&&!r.value?`${se.value}px`:`${W.value}px`,H=`calc(100% - ${te})`;return{sidebarAndExtraWidth:te,width:H}});w(()=>{let H="",te=0;if(!q.value||a.sidebarHidden)H="100%";else if(d.value){const de=r.value?a.sidebarWidth:se.value;te=s.value?se.value:de,H=`calc(100% - ${s.value?W.value:de}px)`}else H="100%";return{marginLeft:`${te}px`,width:H}});const N=w(()=>{const H=Ce.value,{footerEnable:te,footerFixed:de,footerHeight:X}=a;return{marginTop:H&&!P.value&&!p.value&&(!R.value||z.value<J.value)?`${J.value}px`:0,paddingBottom:`${te&&de?X:0}px`}}),le=w(()=>{const{zIndex:H}=a,te=q.value?1:0;return H+te}),oe=w(()=>{const H=Ce.value;return{height:P.value?"0":`${J.value}px`,left:q.value?0:Ee.value.sidebarAndExtraWidth,position:H?"fixed":"static",top:p.value||P.value?`-${J.value}px`:0,width:Ee.value.width,"z-index":le.value}}),ne=w(()=>{const{isMobile:H,zIndex:te}=a;let de=H||ve.value?1:-1;return q.value&&(de+=1),te+de});w(()=>a.footerFixed?Ee.value.width:"100%");const ie=w(()=>({zIndex:a.zIndex}));w(()=>a.isMobile||a.headerToggleSidebarButton&&ve.value&&!B.value&&!q.value&&!a.isMobile),w(()=>!ve.value||q.value||a.isMobile),ye(()=>a.isMobile,H=>{H&&(s.value=!0)},{immediate:!0}),ye([()=>J.value,()=>P.value],([H])=>{D(P.value?0:H)},{immediate:!0}),ye(()=>a.footerHeight,H=>{$(H)},{immediate:!0});{const H=()=>{O.value>J.value?p.value=!0:p.value=!1};ye([()=>a.headerMode,()=>O.value],()=>{if(!R.value||q.value||P.value){a.headerMode!=="auto-scroll"&&(p.value=!1);return}p.value=!0,H()},{immediate:!0})}{const H=Et((te,de,X)=>{if(z.value<J.value){p.value=!1;return}if(X){p.value=!1;return}te?p.value=!1:de&&(p.value=!0)},300);ye(()=>z.value,()=>{a.headerMode!=="auto-scroll"||q.value||P.value||T.value&&H(v.top,v.bottom,m.top)})}function ce(){s.value=!0}const De=$a;return(H,te)=>(u(),S("div",Pi,[ue.value?(u(),x(e(Vi),{key:0,collapse:s.value,"onUpdate:collapse":te[0]||(te[0]=de=>s.value=de),"expand-on-hover":r.value,"onUpdate:expandOnHover":te[1]||(te[1]=de=>r.value=de),"expand-on-hovering":f.value,"onUpdate:expandOnHovering":te[2]||(te[2]=de=>f.value=de),"extra-collapse":i.value,"onUpdate:extraCollapse":te[3]||(te[3]=de=>i.value=de),"extra-visible":n.value,"onUpdate:extraVisible":te[4]||(te[4]=de=>n.value=de),"show-collapse-button":H.sidebarCollapsedButton,"show-fixed-button":H.sidebarFixedButton,"collapse-width":se.value,"dom-visible":!H.isMobile,"extra-width":Y.value,"fixed-extra":r.value,"header-height":e(q)?0:H.headerHeight,"is-sidebar-mixed":e(B)||e(K),"margin-top":we.value,"mixed-width":H.sidebarMixedWidth,show:Se.value,theme:H.sidebarTheme,width:W.value,"z-index":ne.value,onLeave:te[5]||(te[5]=()=>o("sideMouseLeave"))},ut({extra:c(()=>[_(H.$slots,"side-extra")]),"extra-title":c(()=>[_(H.$slots,"side-extra-title")]),default:c(()=>[e(B)||e(K)?_(H.$slots,"mixed-menu",{key:0}):_(H.$slots,"menu",{key:1})]),_:2},[ve.value&&!e(q)?{name:"logo",fn:c(()=>[_(H.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","show-collapse-button","show-fixed-button","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):I("",!0),A("div",{ref_key:"contentRef",ref:h,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[A("div",{class:L([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(z)>20},e(Mo)],"overflow-hidden transition-all duration-200"]),style:he(oe.value)},null,6),b(e($i),{id:e(De),"content-compact":H.contentCompact,"content-compact-width":H.contentCompactWidth,padding:H.contentPadding,"padding-bottom":H.contentPaddingBottom,"padding-left":H.contentPaddingLeft,"padding-right":H.contentPaddingRight,"padding-top":H.contentPaddingTop,style:he(N.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[_(H.$slots,"content-overlay")]),default:c(()=>[_(H.$slots,"content")]),_:3},8,["id","content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"])],512),_(H.$slots,"extra"),Be.value?(u(),S("div",{key:1,style:he(ie.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:ce},null,4)):I("",!0)]))}});function Li(){const l=Z(!1),t=Z(0),a=et(),o=500,s=w(()=>U.transition.loading),n=()=>{if(!s.value)return;const i=performance.now()-t.value;i<o?setTimeout(()=>{l.value=!1},o-i):l.value=!1};return a.beforeEach(i=>(i.meta.loaded||!s.value||i.meta.iframeSrc||(t.value=performance.now(),l.value=!0),!0)),a.afterEach(i=>(i.meta.loaded||!s.value||i.meta.iframeSrc||n(),!0)),{spinning:l}}const zi=M({name:"LayoutContentSpinner",__name:"content-spinner",setup(l){const{spinning:t}=Li();return(a,o)=>(u(),x(e(Ea),{spinning:e(t)},null,8,["spinning"]))}}),Ai={key:0,class:"relative size-full"},Ui=["src","onLoad"],Oi=M({name:"IFrameRouterView",__name:"iframe-router-view",setup(l){const t=Z([]),a=at(),o=Re(),s=w(()=>U.tabbar.enable),n=w(()=>s.value?a.getTabs.filter(m=>{var v;return!!((v=m.meta)!=null&&v.iframeSrc)}):o.meta.iframeSrc?[o]:[]),i=w(()=>new Set(n.value.map(m=>m.name))),r=w(()=>n.value.length>0);function d(m){return m.name===o.name}function f(m){const{meta:v,name:T}=m;return!T||!a.renderRouteView?!1:s.value?!(v!=null&&v.keepAlive)&&i.value.has(T)&&T!==o.name?!1:a.getTabs.some(z=>z.name===T):d(m)}function p(m){t.value[m]=!1}function h(m){const v=t.value[m];return v===void 0?!0:v}return(m,v)=>r.value?(u(!0),S(ee,{key:0},be(n.value,(T,z)=>(u(),S(ee,{key:T.fullPath},[f(T)?ze((u(),S("div",Ai,[b(e(Ea),{spinning:h(z)},null,8,["spinning"]),A("iframe",{src:T.meta.iframeSrc,class:"size-full",onLoad:D=>p(z)},null,40,Ui)],512)),[[Le,d(T)]]):I("",!0)],64))),128)):I("",!0)}}),Hi={class:"relative h-full"},Di=M({name:"LayoutContent",__name:"content",setup(l){const t=at(),{keepAlive:a}=it(),{getCachedTabs:o,getExcludeCachedTabs:s,renderRouteView:n}=Bo(t),i=w(()=>{const{transition:f}=U;return f.name&&f.enable});function r(f){const{tabbar:p,transition:h}=U,m=h.name;if(!(!m||!h.enable))return!p.enable||!a,m}function d(f,p){var v;if(!f){console.error("Component view not found，please check the route configuration");return}const h=p.name;if(!h)return f;const m=(v=f==null?void 0:f.type)==null?void 0:v.name;return m||m===h||(f.type||(f.type={}),f.type.name=h),f}return(f,p)=>(u(),S("div",Hi,[b(e(Oi)),b(e(_o),null,{default:c(({Component:h,route:m})=>[i.value?(u(),x(rt,{key:0,name:r(m),appear:"",mode:"out-in"},{default:c(()=>[e(a)?(u(),x(ba,{key:0,exclude:e(s),include:e(o)},[e(n)?ze((u(),x($e(d(h,m)),{key:m.fullPath})),[[Le,!m.meta.iframeSrc]]):I("",!0)],1032,["exclude","include"])):e(n)?(u(),x($e(h),{key:m.fullPath})):I("",!0)]),_:2},1032,["name"])):(u(),S(ee,{key:1},[e(a)?(u(),x(ba,{key:0,exclude:e(s),include:e(o)},[e(n)?ze((u(),x($e(d(h,m)),{key:m.fullPath})),[[Le,!m.meta.iframeSrc]]):I("",!0)],1032,["exclude","include"])):e(n)?(u(),x($e(h),{key:m.fullPath})):I("",!0)],64))]),_:1})]))}}),Wi={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Ni=M({name:"LayoutFooter",__name:"footer",setup(l){return(t,a)=>(u(),S("div",Wi,[_(t.$slots,"default")]))}}),Ri={class:"flex-center hidden lg:block"},Ca=50,Fi=M({name:"LayoutHeader",__name:"header",setup(l){const t=He(),{refresh:a}=Ja(),o=w(()=>{const s=[];return U.widget.refresh&&s.push({index:0,name:"refresh"}),Object.keys(t).forEach(n=>{const i=n.split("-");n.startsWith("header-left")&&s.push({index:Number(i[2]),name:n})}),s.sort((n,i)=>n.index-i.index)});return(s,n)=>{const i=Xo("VbenIconButton");return u(),S(ee,null,[(u(!0),S(ee,null,be(o.value.filter(r=>r.index<Ca),r=>_(s.$slots,r.name,{key:r.name},()=>[r.name==="refresh"?(u(),x(i,{key:0,class:"my-0 mr-1 rounded-md",onClick:e(a)},{default:c(()=>[b(e(Lt),{class:"size-4"})]),_:1},8,["onClick"])):I("",!0)],!0)),128)),A("div",Ri,[_(s.$slots,"breadcrumb",{},void 0,!0)]),(u(!0),S(ee,null,be(o.value.filter(r=>r.index>Ca),r=>_(s.$slots,r.name,{key:r.name},void 0,!0)),128)),A("div",{class:L([`menu-align-${e(U).header.menuAlign}`,"flex h-full min-w-0 flex-1 items-center"])},[_(s.$slots,"menu",{},void 0,!0)],2)],64)}}}),Ki=Me(Fi,[["__scopeId","data-v-b29e2dcb"]]),Gi={class:"relative mr-1 flex size-1.5"},ji=M({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(l){return(t,a)=>(u(),S("span",Gi,[A("span",{class:L([t.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:he(t.dotStyle)},null,6),A("span",{class:L([t.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:he(t.dotStyle)},null,6)]))}}),el=M({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(l){const t=l,a={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},o=w(()=>t.badgeType==="dot"),s=w(()=>{const{badgeVariants:i}=t;return i?a[i]||i:a.default}),n=w(()=>s.value&&Yo(s.value)?{backgroundColor:s.value}:{});return(i,r)=>o.value||i.badge?(u(),S("span",{key:0,class:L([i.$attrs.class,"absolute"])},[o.value?(u(),x(ji,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(u(),S("div",{key:1,class:L([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:he(n.value)},V(i.badge),7))],2)):I("",!0)}}),qi=["onClick","onMouseenter"],Ji=M({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(l,{emit:t}){const a=l,o=t,{b:s,e:n,is:i}=je("normal-menu");function r(d){return a.activePath===d.path&&d.activeIcon||d.icon}return(d,f)=>(u(),S("ul",{class:L([[d.theme,e(s)(),e(i)("collapse",d.collapse),e(i)(d.theme,!0),e(i)("rounded",d.rounded)],"relative"])},[(u(!0),S(ee,null,be(d.menus,p=>(u(),S("li",{key:p.path,class:L([e(n)("item"),e(i)("active",d.activePath===p.path)]),onClick:()=>o("select",p),onMouseenter:()=>o("enter",p)},[b(e(Oe),{class:L(e(n)("icon")),icon:r(p),fallback:""},null,8,["class","icon"]),A("span",{class:L([e(n)("name"),"truncate"])},V(p.name),3)],42,qi))),128))],2))}}),Xi=Me(Ji,[["__scopeId","data-v-3ebda870"]]);function tl(l,t){var o,s;let a=l.parent;for(;a&&!t.includes((s=(o=a==null?void 0:a.type)==null?void 0:o.name)!=null?s:"");)a=a.parent;return a}const ct=l=>{const t=Array.isArray(l)?l:[l],a=[];return t.forEach(o=>{var s;Array.isArray(o)?a.push(...ct(o)):ma(o)&&Array.isArray(o.children)?a.push(...ct(o.children)):(a.push(o),ma(o)&&((s=o.component)!=null&&s.subTree)&&a.push(...ct(o.component.subTree)))}),a};function al(){const l=vt();if(!l)throw new Error("instance is required");const t=w(()=>{var n;let o=l.parent;const s=[l.props.path];for(;(o==null?void 0:o.type.name)!=="Menu";)o!=null&&o.props.path&&s.unshift(o.props.path),o=(n=o==null?void 0:o.parent)!=null?n:null;return s});return{parentMenu:w(()=>tl(l,["Menu","SubMenu"])),parentPaths:t}}function ll(l){return w(()=>{var a;return{"--menu-level":l?(a=l==null?void 0:l.level)!=null?a:1:0}})}const ol=Symbol("menuContext");function Yi(l){mt(ol,l)}function nl(l){const t=vt();mt(`subMenu:${t==null?void 0:t.uid}`,l)}function Ot(){if(!vt())throw new Error("instance is required");return bt(ol)}function sl(){const l=vt();if(!l)throw new Error("instance is required");const t=tl(l,["Menu","SubMenu"]);return bt(`subMenu:${t==null?void 0:t.uid}`)}const Zi=M({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(l,{emit:t}){const a=l,o=t,s=He(),{b:n,e:i,is:r}=je("menu-item"),d=je("menu"),f=Ot(),p=sl(),{parentMenu:h,parentPaths:m}=al(),v=w(()=>a.path===(f==null?void 0:f.activePath)),T=w(()=>v.value&&a.activeIcon||a.icon),z=w(()=>{var P;return((P=h.value)==null?void 0:P.type.name)==="Menu"}),D=w(()=>{var P;return((P=f.props)==null?void 0:P.collapseShowTitle)&&z.value&&f.props.collapse}),$=w(()=>{var P;return f.props.mode==="vertical"&&z.value&&((P=f.props)==null?void 0:P.collapse)&&s.title}),O=ht({active:v,parentPaths:m.value,path:a.path||""});function F(){var P;a.disabled||((P=f==null?void 0:f.handleMenuItemClick)==null||P.call(f,{parentPaths:m.value,path:a.path}),o("click",O))}return Je(()=>{var P,K;(P=p==null?void 0:p.addSubMenu)==null||P.call(p,O),(K=f==null?void 0:f.addMenuItem)==null||K.call(f,O)}),Aa(()=>{var P,K;(P=p==null?void 0:p.removeSubMenu)==null||P.call(p,O),(K=f==null?void 0:f.removeMenuItem)==null||K.call(f,O)}),(P,K)=>(u(),S("li",{class:L([e(f).theme,e(n)(),e(r)("active",v.value),e(r)("disabled",P.disabled),e(r)("collapse-show-title",D.value)]),role:"menuitem",onClick:Pe(F,["stop"])},[$.value?(u(),x(e(qe),{key:0,"content-class":[e(f).theme],side:"right"},{trigger:c(()=>[A("div",{class:L([e(d).be("tooltip","trigger")])},[b(e(Oe),{class:L(e(d).e("icon")),icon:T.value,fallback:""},null,8,["class","icon"]),_(P.$slots,"default"),D.value?(u(),S("span",{key:0,class:L(e(d).e("name"))},[_(P.$slots,"title")],2)):I("",!0)],2)]),default:c(()=>[_(P.$slots,"title")]),_:3},8,["content-class"])):I("",!0),ze(A("div",{class:L([e(i)("content")])},[e(f).props.mode!=="horizontal"?(u(),x(e(el),pe({key:0,class:"right-2"},a),null,16)):I("",!0),b(e(Oe),{class:L(e(d).e("icon")),icon:T.value},null,8,["class","icon"]),_(P.$slots,"default"),_(P.$slots,"title")],2),[[Le,!$.value]])],2))}});function Qi(l,t={}){const{enable:a=!0,delay:o=320}=t;function s(){if(!(typeof a=="boolean"?a:a.value))return;const r=document.querySelector("aside li[role=menuitem].is-active");r&&r.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}const n=_t(s,o);return ye(l,()=>{(typeof a=="boolean"?a:a.value)&&n()}),{scrollToActiveItem:s}}const ed=M({name:"CollapseTransition",__name:"collapse-transition",setup(l){const t=o=>{o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom},a={afterEnter(o){o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow},afterLeave(o){t(o)},beforeEnter(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.style.height&&(o.dataset.elExistsHeight=o.style.height),o.style.maxHeight=0,o.style.paddingTop=0,o.style.marginTop=0,o.style.paddingBottom=0,o.style.marginBottom=0},beforeLeave(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.dataset.oldOverflow=o.style.overflow,o.style.maxHeight=`${o.scrollHeight}px`,o.style.overflow="hidden"},enter(o){requestAnimationFrame(()=>{o.dataset.oldOverflow=o.style.overflow,o.dataset.elExistsHeight?o.style.maxHeight=o.dataset.elExistsHeight:o.scrollHeight===0?o.style.maxHeight=0:o.style.maxHeight=`${o.scrollHeight}px`,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom,o.style.marginTop=o.dataset.oldMarginTop,o.style.marginBottom=o.dataset.oldMarginBottom,o.style.overflow="hidden"})},enterCancelled(o){t(o)},leave(o){o.scrollHeight!==0&&(o.style.maxHeight=0,o.style.paddingTop=0,o.style.paddingBottom=0,o.style.marginTop=0,o.style.marginBottom=0)},leaveCancelled(o){t(o)}};return(o,s)=>(u(),x(rt,pe({name:"collapse-transition"},La(a)),{default:c(()=>[_(o.$slots,"default")]),_:3},16))}}),Sa=M({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){const t=l,a=Ot(),{b:o,e:s,is:n}=je("sub-menu-content"),i=je("menu"),r=w(()=>a==null?void 0:a.openedMenus.includes(t.path)),d=w(()=>a.props.collapse),f=w(()=>t.level===1),p=w(()=>a.props.collapseShowTitle&&f.value&&d.value),h=w(()=>a==null?void 0:a.props.mode),m=w(()=>h.value==="horizontal"||!(f.value&&d.value)),v=w(()=>h.value==="vertical"&&f.value&&d.value&&!p.value),T=w(()=>h.value==="horizontal"&&!f.value||h.value==="vertical"&&d.value?Vt:It),z=w(()=>r.value?{transform:"rotate(180deg)"}:{});return(D,$)=>(u(),S("div",{class:L([e(o)(),e(n)("collapse-show-title",p.value),e(n)("more",D.isMenuMore)])},[_(D.$slots,"default"),D.isMenuMore?I("",!0):(u(),x(e(Oe),{key:0,class:L(e(i).e("icon")),icon:D.icon,fallback:""},null,8,["class","icon"])),v.value?I("",!0):(u(),S("div",{key:1,class:L([e(s)("title")])},[_(D.$slots,"title")],2)),D.isMenuMore?I("",!0):ze((u(),x($e(T.value),{key:2,class:L([[e(s)("icon-arrow")],"size-4"]),style:he(z.value)},null,8,["class","style"])),[[Le,m.value]])],2))}}),rl=M({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){var we;const t=l,{parentMenu:a,parentPaths:o}=al(),{b:s,is:n}=je("sub-menu"),i=je("menu"),r=Ot(),d=sl(),f=ll(d),p=Z(!1),h=Z({}),m=Z({}),v=Z(null);nl({addSubMenu:j,handleMouseleave:J,level:((we=d==null?void 0:d.level)!=null?we:0)+1,mouseInChild:p,removeSubMenu:q});const T=w(()=>r==null?void 0:r.openedMenus.includes(t.path)),z=w(()=>{var W;return((W=a.value)==null?void 0:W.type.name)==="Menu"}),D=w(()=>{var W;return(W=r==null?void 0:r.props.mode)!=null?W:"vertical"}),$=w(()=>r==null?void 0:r.props.rounded),O=w(()=>{var W;return(W=d==null?void 0:d.level)!=null?W:0}),F=w(()=>O.value===1),P=w(()=>{const W=D.value==="horizontal",Y=W&&F.value?"bottom":"right";return{collisionPadding:{top:20},side:Y,sideOffset:W?5:10}}),K=w(()=>{let W=!1;return Object.values(h.value).forEach(Y=>{Y.active&&(W=!0)}),Object.values(m.value).forEach(Y=>{Y.active&&(W=!0)}),W});function j(W){m.value[W.path]=W}function q(W){Reflect.deleteProperty(m.value,W.path)}function B(){const W=r==null?void 0:r.props.mode;t.disabled||r!=null&&r.props.collapse&&W==="vertical"||W==="horizontal"||r==null||r.handleSubMenuClick({active:K.value,parentPaths:o.value,path:t.path})}function R(W,Y=300){var ve,Ce;if(W.type!=="focus"){if(!(r!=null&&r.props.collapse)&&(r==null?void 0:r.props.mode)==="vertical"||t.disabled){d&&(d.mouseInChild.value=!0);return}d&&(d.mouseInChild.value=!0),v.value&&window.clearTimeout(v.value),v.value=setTimeout(()=>{r==null||r.openMenu(t.path,o.value)},Y),(Ce=(ve=a.value)==null?void 0:ve.vnode.el)==null||Ce.dispatchEvent(new MouseEvent("mouseenter"))}}function J(W=!1){var Y;if(!(r!=null&&r.props.collapse)&&(r==null?void 0:r.props.mode)==="vertical"&&d){d.mouseInChild.value=!1;return}v.value&&window.clearTimeout(v.value),d&&(d.mouseInChild.value=!1),v.value=setTimeout(()=>{!p.value&&(r==null||r.closeMenu(t.path,o.value))},300),W&&((Y=d==null?void 0:d.handleMouseleave)==null||Y.call(d,!0))}const se=w(()=>K.value&&t.activeIcon||t.icon),ue=ht({active:K,parentPaths:o,path:t.path});return Je(()=>{var W,Y;(W=d==null?void 0:d.addSubMenu)==null||W.call(d,ue),(Y=r==null?void 0:r.addSubMenu)==null||Y.call(r,ue)}),Aa(()=>{var W,Y;(W=d==null?void 0:d.removeSubMenu)==null||W.call(d,ue),(Y=r==null?void 0:r.removeSubMenu)==null||Y.call(r,ue)}),(W,Y)=>(u(),S("li",{class:L([e(s)(),e(n)("opened",T.value),e(n)("active",K.value),e(n)("disabled",W.disabled)]),onFocus:R,onMouseenter:R,onMouseleave:Y[3]||(Y[3]=()=>J())},[e(r).isMenuPopup?(u(),x(e(Ws),{key:0,"content-class":[e(r).theme,e(i).e("popup-container"),e(n)(e(r).theme,!0),T.value?"":"hidden","overflow-auto","max-h-[calc(var(--radix-hover-card-content-available-height)-20px)]"],"content-props":P.value,open:!0,"open-delay":0},{trigger:c(()=>[b(Sa,{class:L(e(n)("active",K.value)),icon:se.value,"is-menu-more":W.isSubMenuMore,"is-top-level-menu-submenu":z.value,level:O.value,path:W.path,onClick:Pe(B,["stop"])},{title:c(()=>[_(W.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[A("div",{class:L([e(i).is(D.value,!0),e(i).e("popup")]),onFocus:Y[0]||(Y[0]=ve=>R(ve,100)),onMouseenter:Y[1]||(Y[1]=ve=>R(ve,100)),onMouseleave:Y[2]||(Y[2]=()=>J(!0))},[A("ul",{class:L([e(i).b(),e(n)("rounded",$.value)]),style:he(e(f))},[_(W.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(u(),S(ee,{key:1},[b(Sa,{class:L(e(n)("active",K.value)),icon:se.value,"is-menu-more":W.isSubMenuMore,"is-top-level-menu-submenu":z.value,level:O.value,path:W.path,onClick:Pe(B,["stop"])},{title:c(()=>[_(W.$slots,"title")]),default:c(()=>[_(W.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),b(ed,null,{default:c(()=>[ze(A("ul",{class:L([e(i).b(),e(n)("rounded",$.value)]),style:he(e(f))},[_(W.$slots,"default")],6),[[Le,T.value]])]),_:3})],64))],34))}}),td=M({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},scrollToActive:{type:Boolean,default:!1},theme:{default:"dark"}},emits:["close","open","select"],setup(l,{emit:t}){const a=l,o=t,{b:s,is:n}=je("menu"),i=ll(),r=He(),d=Z(),f=Z(-1),p=Z(a.defaultOpeneds&&!a.collapse?[...a.defaultOpeneds]:[]),h=Z(a.defaultActive),m=Z({}),v=Z({}),T=Z(!1),z=w(()=>a.mode==="horizontal"||a.mode==="vertical"&&a.collapse),D=w(()=>{var ie,ce;const N=(ce=(ie=r.default)==null?void 0:ie.call(r))!=null?ce:[],le=ct(N),oe=f.value===-1?le:le.slice(0,f.value),ne=f.value===-1?[]:le.slice(f.value);return{showSlotMore:ne.length>0,slotDefault:oe,slotMore:ne}});ye(()=>a.collapse,N=>{N&&(p.value=[])}),ye(m.value,R),ye(()=>a.defaultActive,(N="")=>{m.value[N]||(h.value=""),J(N)});let $;za(()=>{a.mode==="horizontal"?$=Zo(d,j).stop:$==null||$()}),Yi(ht({activePath:h,addMenuItem:ve,addSubMenu:Ce,closeMenu:W,handleMenuItemClick:se,handleSubMenuClick:ue,isMenuPopup:z,openedMenus:p,openMenu:Y,props:a,removeMenuItem:Be,removeSubMenu:Se,subMenus:v,theme:Qo(a,"theme"),items:m})),nl({addSubMenu:Ce,level:1,mouseInChild:T,removeSubMenu:Se});function O(N){const le=getComputedStyle(N),oe=Number.parseInt(le.marginLeft,10),ne=Number.parseInt(le.marginRight,10);return N.offsetWidth+oe+ne||0}function F(){var te,de,X;if(!d.value)return-1;const N=[...(de=(te=d.value)==null?void 0:te.childNodes)!=null?de:[]].filter(fe=>fe.nodeName!=="#comment"&&(fe.nodeName!=="#text"||fe.nodeValue)),le=46,oe=getComputedStyle(d==null?void 0:d.value),ne=Number.parseInt(oe.paddingLeft,10),ie=Number.parseInt(oe.paddingRight,10),ce=((X=d.value)==null?void 0:X.clientWidth)-ne-ie;let De=0,H=0;return N.forEach((fe,We)=>{De+=O(fe),De<=ce-le&&(H=We+1)}),H===N.length?-1:H}function P(N,le=33.34){let oe;return()=>{oe&&clearTimeout(oe),oe=setTimeout(()=>{N()},le)}}let K=!0;function j(){if(f.value===F())return;const N=()=>{f.value=-1,Ge(()=>{f.value=F()})};N(),K?N():P(N)(),K=!1}const q=w(()=>a.scrollToActive&&a.mode==="vertical"&&!a.collapse),{scrollToActiveItem:B}=Qi(h,{enable:q,delay:320});ye(h,()=>{B()});function R(){Ee().forEach(le=>{const oe=v.value[le];oe&&Y(le,oe.parentPaths)})}function J(N){const le=m.value,oe=le[N]||h.value&&le[h.value]||le[a.defaultActive||""];h.value=oe?oe.path:N}function se(N){const{collapse:le,mode:oe}=a;(oe==="horizontal"||le)&&(p.value=[]);const{parentPaths:ne,path:ie}=N;!ie||!ne||o("select",ie,ne)}function ue({parentPaths:N,path:le}){p.value.includes(le)?W(le,N):Y(le,N)}function we(N){const le=p.value.indexOf(N);le!==-1&&p.value.splice(le,1)}function W(N,le){var oe,ne;a.accordion&&(p.value=(ne=(oe=v.value[N])==null?void 0:oe.parentPaths)!=null?ne:[]),we(N),o("close",N,le)}function Y(N,le){if(!p.value.includes(N)){if(a.accordion){const oe=Ee();oe.includes(N)&&(le=oe),p.value=p.value.filter(ne=>le.includes(ne))}p.value.push(N),o("open",N,le)}}function ve(N){m.value[N.path]=N}function Ce(N){v.value[N.path]=N}function Se(N){Reflect.deleteProperty(v.value,N.path)}function Be(N){Reflect.deleteProperty(m.value,N.path)}function Ee(){const N=h.value&&m.value[h.value];return!N||a.mode==="horizontal"||a.collapse?[]:N.parentPaths}return(N,le)=>(u(),S("ul",{ref_key:"menu",ref:d,class:L([N.theme,e(s)(),e(n)(N.mode,!0),e(n)(N.theme,!0),e(n)("rounded",N.rounded),e(n)("collapse",N.collapse),e(n)("menu-align",N.mode==="horizontal")]),style:he(e(i)),role:"menu"},[N.mode==="horizontal"&&D.value.showSlotMore?(u(),S(ee,{key:0},[(u(!0),S(ee,null,be(D.value.slotDefault,oe=>(u(),x($e(oe),{key:oe.key}))),128)),b(rl,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[b(e($o),{class:"size-4"})]),default:c(()=>[(u(!0),S(ee,null,be(D.value.slotMore,oe=>(u(),x($e(oe),{key:oe.key}))),128))]),_:1})],64)):_(N.$slots,"default",{key:1})],6))}}),il=M({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(l){const t=l,a=w(()=>{const{menu:o}=t;return Reflect.has(o,"children")&&!!o.children&&o.children.length>0});return(o,s)=>a.value?(u(),x(e(rl),{key:`${o.menu.path}_sub`,"active-icon":o.menu.activeIcon,icon:o.menu.icon,path:o.menu.path},{content:c(()=>[b(e(el),{badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[A("span",null,V(o.menu.name),1)]),default:c(()=>[(u(!0),S(ee,null,be(o.menu.children||[],n=>(u(),x(il,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(u(),x(e(Zi),{key:o.menu.path,"active-icon":o.menu.activeIcon,badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,icon:o.menu.icon,path:o.menu.path},{title:c(()=>[A("span",null,V(o.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),dl=M({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const a=Qe(l);return(o,s)=>(u(),x(e(td),Ve(Ue(e(a))),{default:c(()=>[(u(!0),S(ee,null,be(o.menus,n=>(u(),x(il,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function Ht(){const l=et(),t=new Map,a=()=>{l.getRoutes().forEach(r=>{t.set(r.path,r)})};a(),l.afterEach(()=>{a()});const o=i=>{var d,f;if(ha(i))return!0;const r=t.get(i);return(f=(d=r==null?void 0:r.meta)==null?void 0:d.openInNewWindow)!=null?f:!1};return{navigation:i=>G(null,null,function*(){var r;try{const d=t.get(i),{openInNewWindow:f=!1,query:p={}}=(r=d==null?void 0:d.meta)!=null?r:{};ha(i)?en(i,{target:"_blank"}):f?Pa(i):yield l.push({path:i,query:p})}catch(d){throw console.error("Navigation failed:",d),d}}),willOpenedByWindow:i=>o(i)}}const ad=M({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const t=Re(),{navigation:a}=Ht();function o(s){return G(this,null,function*(){yield a(s)})}return(s,n)=>{var i;return u(),x(e(dl),{accordion:s.accordion,collapse:s.collapse,"default-active":((i=e(t).meta)==null?void 0:i.activePath)||e(t).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:o},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),Ta=M({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},emits:["open","select"],setup(l,{emit:t}){const a=l,o=t;function s(i){o("select",i,a.mode)}function n(i,r){o("open",i,r)}return(i,r)=>(u(),x(e(dl),{accordion:i.accordion,collapse:i.collapse,"collapse-show-title":i.collapseShowTitle,"default-active":i.defaultActive,menus:i.menus,mode:i.mode,rounded:i.rounded,"scroll-to-active":"",theme:i.theme,onOpen:n,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),ld=M({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(l,{emit:t}){const a=l,o=t,s=Re();return Ua(()=>{const n=zt(a.menus||[],s.path);if(n){const i=(a.menus||[]).find(r=>{var d;return r.path===((d=n.parents)==null?void 0:d[0])});o("defaultSelect",n,i)}}),(n,i)=>(u(),x(e(Xi),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:i[0]||(i[0]=r=>o("enter",r)),onSelect:i[1]||(i[1]=r=>o("select",r))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function od(l){const t=ft(),{navigation:a,willOpenedByWindow:o}=Ht(),s=w(()=>{var $;return($=l==null?void 0:l.value)!=null?$:t.accessMenus}),n=new Map,i=Z([]),r=Re(),d=Z([]),f=Z(!1),p=Z(""),h=w(()=>U.app.layout==="header-mixed-nav"?1:0),m=$=>G(null,null,function*(){var P,K,j;const O=(P=$==null?void 0:$.children)!=null?P:[],F=O.length>0;o($.path)||(d.value=O!=null?O:[],p.value=(j=(K=$.parents)==null?void 0:K[h.value])!=null?j:$.path,f.value=F),F?U.sidebar.autoActivateChild&&(yield a(n.has($.path)?n.get($.path):$.path)):yield a($.path)}),v=($,O)=>G(null,null,function*(){var F,P,K,j;d.value=(P=(F=O==null?void 0:O.children)!=null?F:i.value)!=null?P:[],p.value=(j=(K=$.parents)==null?void 0:K[h.value])!=null?j:$.path,U.sidebar.expandOnHover&&(f.value=d.value.length>0)}),T=()=>{var P,K;if(U.sidebar.expandOnHover)return;const{findMenu:$,rootMenu:O,rootMenuPath:F}=nt(s.value,r.path);p.value=(P=F!=null?F:$==null?void 0:$.path)!=null?P:"",d.value=(K=O==null?void 0:O.children)!=null?K:[]},z=$=>{var O,F,P;if(!U.sidebar.expandOnHover){const{findMenu:K}=nt(s.value,$.path);d.value=(O=K==null?void 0:K.children)!=null?O:[],p.value=(P=(F=$.parents)==null?void 0:F[h.value])!=null?P:$.path,f.value=d.value.length>0}};function D($){var j,q,B,R;const O=((j=r.meta)==null?void 0:j.activePath)||$,{findMenu:F,rootMenu:P,rootMenuPath:K}=nt(s.value,O,h.value);i.value=(q=P==null?void 0:P.children)!=null?q:[],K&&n.set(K,O),p.value=(B=K!=null?K:F==null?void 0:F.path)!=null?B:"",d.value=(R=P==null?void 0:P.children)!=null?R:[],U.sidebar.expandOnHover&&(f.value=d.value.length>0)}return ye(()=>[r.path,U.app.layout],([$])=>{D($||"")},{immediate:!0}),{extraActiveMenu:p,extraMenus:d,handleDefaultSelect:v,handleMenuMouseEnter:z,handleMixedMenuSelect:m,handleSideMouseLeave:T,sidebarExtraVisible:f}}function nd(){const{navigation:l,willOpenedByWindow:t}=Ht(),a=ft(),o=Re(),s=Z([]),n=Z(""),i=Z(""),r=Z([]),d=new Map,{isMixedNav:f,isHeaderMixedNav:p}=it(),h=w(()=>U.navigation.split&&f.value||p.value),m=w(()=>{const j=U.sidebar.enable;return h.value?j&&s.value.length>0:j}),v=w(()=>a.accessMenus),T=w(()=>h.value?v.value.map(j=>ke(Q({},j),{children:[]})):v.value),z=w(()=>h.value?s.value:v.value),D=w(()=>p.value?z.value:T.value),$=w(()=>{var j,q;return(q=(j=o==null?void 0:o.meta)==null?void 0:j.activePath)!=null?q:o.path}),O=w(()=>{var j,q;return h.value?n.value:(q=(j=o.meta)==null?void 0:j.activePath)!=null?q:o.path}),F=(j,q)=>{var J,se;if(!h.value||q==="vertical"){l(j);return}const B=v.value.find(ue=>ue.path===j),R=(J=B==null?void 0:B.children)!=null?J:[];t(j)||(n.value=(se=B==null?void 0:B.path)!=null?se:"",s.value=R),R.length===0?l(j):B&&U.sidebar.autoActivateChild&&l(d.has(B.path)?d.get(B.path):B.path)},P=(j,q)=>{q.length<=1&&U.sidebar.autoActivateChild&&l(d.has(j)?d.get(j):j)};function K(j=o.path){var R,J,se,ue,we;let{rootMenu:q}=nt(v.value,j);q||(q=v.value.find(W=>W.path===j));const B=nt((q==null?void 0:q.children)||[],j,1);i.value=(R=B.rootMenuPath)!=null?R:"",r.value=(se=(J=B.rootMenu)==null?void 0:J.children)!=null?se:[],n.value=(ue=q==null?void 0:q.path)!=null?ue:"",s.value=(we=q==null?void 0:q.children)!=null?we:[]}return ye(()=>o.path,j=>{var B,R;const q=(R=(B=o==null?void 0:o.meta)==null?void 0:B.activePath)!=null?R:j;K(q),n.value&&d.set(n.value,q)},{immediate:!0}),Ua(()=>{var j;K(((j=o.meta)==null?void 0:j.activePath)||o.path)}),{handleMenuSelect:F,handleMenuOpen:P,headerActive:O,headerMenus:T,sidebarActive:$,sidebarMenus:z,mixHeaderMenus:D,mixExtraMenus:r,sidebarVisible:m}}const sd={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},rd=M({__name:"tool-more",props:{menus:{}},setup(l){return(t,a)=>(u(),x(e(Hs),{menus:t.menus,modal:!1},{default:c(()=>[A("div",sd,[b(e(It),{class:"size-4"})])]),_:1},8,["menus"]))}}),id=M({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(l){const t=y(l,"screen");function a(){t.value=!t.value}return(o,s)=>(u(),S("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:a},[t.value?(u(),x(e(Ka),{key:0,class:"size-4"})):(u(),x(e(Fa),{key:1,class:"size-4"}))]))}}),dd=["data-active-tab","data-index","onClick","onMousedown"],ud={class:"relative size-full px-1"},cd={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},pd={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},fd={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},bd={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},md=M({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:ge({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:ge(["close","unpin"],["update:active"]),setup(l,{emit:t}){const a=l,o=t,s=y(l,"active"),n=Z(),i=Z(),r=w(()=>{const{gap:p}=a;return{"--gap":`${p}px`}}),d=w(()=>a.tabs.map(p=>{const{fullPath:h,meta:m,name:v,path:T}=p||{},{affixTab:z,icon:D,newTabTitle:$,tabClosable:O,title:F}=m||{};return{affixTab:!!z,closable:Reflect.has(m,"tabClosable")?!!O:!0,fullPath:h,icon:D,key:h||T,meta:m,name:v,path:T,title:$||F||v}}));function f(p,h){p.button===1&&h.closable&&!h.affixTab&&d.value.length>1&&a.middleClickToClose&&(p.preventDefault(),p.stopPropagation(),o("close",h.key))}return(p,h)=>(u(),S("div",{ref_key:"contentRef",ref:n,class:L([p.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:he(r.value)},[b(pt,{name:"slide-left"},{default:c(()=>[(u(!0),S(ee,null,be(d.value,(m,v)=>(u(),S("div",{key:m.key,ref_for:!0,ref_key:"tabRef",ref:i,class:L([[{"is-active":m.key===s.value,draggable:!m.affixTab,"affix-tab":m.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":v,"data-tab-item":"true",onClick:T=>s.value=m.key,onMousedown:T=>f(T,m)},[b(e(ja),{"handler-data":m,menus:p.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[A("div",ud,[v!==0&&m.key!==s.value?(u(),S("div",cd)):I("",!0),h[0]||(h[0]=A("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[A("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),A("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[A("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),A("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[A("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),A("div",pd,[ze(b(e(st),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:Pe(()=>o("close",m.key),["stop"])},null,8,["onClick"]),[[Le,!m.affixTab&&d.value.length>1&&m.closable]]),ze(b(e(At),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Pe(()=>o("unpin",m),["stop"])},null,8,["onClick"]),[[Le,m.affixTab&&d.value.length>1&&m.closable]])]),A("div",fd,[p.showIcon?(u(),x(e(Oe),{key:0,icon:m.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):I("",!0),A("span",bd,V(m.title),1)])])]),_:2},1032,["handler-data","menus"])],42,dd))),128))]),_:1})],6))}}),hd=Me(md,[["__scopeId","data-v-4c849e93"]]),vd=["data-index","onClick","onMousedown"],gd={class:"relative flex size-full items-center"},yd={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},wd={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},xd={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},kd=M({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:ge({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:ge(["close","unpin"],["update:active"]),setup(l,{emit:t}){const a=l,o=t,s=y(l,"active"),n=w(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[a.styleType||"plain"]||{content:""}),i=w(()=>a.tabs.map(d=>{const{fullPath:f,meta:p,name:h,path:m}=d||{},{affixTab:v,icon:T,newTabTitle:z,tabClosable:D,title:$}=p||{};return{affixTab:!!v,closable:Reflect.has(p,"tabClosable")?!!D:!0,fullPath:f,icon:T,key:f||m,meta:p,name:h,path:m,title:z||$||h}}));function r(d,f){d.button===1&&f.closable&&!f.affixTab&&i.value.length>1&&a.middleClickToClose&&(d.preventDefault(),d.stopPropagation(),o("close",f.key))}return(d,f)=>(u(),S("div",{class:L([d.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[b(pt,{name:"slide-left"},{default:c(()=>[(u(!0),S(ee,null,be(i.value,(p,h)=>(u(),S("div",{key:p.key,class:L([[{"is-active dark:bg-accent bg-primary/15":p.key===s.value,draggable:!p.affixTab,"affix-tab":p.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":h,"data-tab-item":"true",onClick:m=>s.value=p.key,onMousedown:m=>r(m,p)},[b(e(ja),{"handler-data":p,menus:d.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[A("div",gd,[A("div",yd,[ze(b(e(st),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:Pe(()=>o("close",p.key),["stop"])},null,8,["onClick"]),[[Le,!p.affixTab&&i.value.length>1&&p.closable]]),ze(b(e(At),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Pe(()=>o("unpin",p),["stop"])},null,8,["onClick"]),[[Le,p.affixTab&&i.value.length>1&&p.closable]])]),A("div",wd,[d.showIcon?(u(),x(e(Oe),{key:0,icon:p.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):I("",!0),A("span",xd,V(p.title),1)])])]),_:2},1032,["handler-data","menus"])],42,vd))),128))]),_:1})],2))}});function Tt(l){const t="group";return l.classList.contains(t)?l:l.closest(`.${t}`)}function Cd(l,t){const a=Z(null);function o(){return G(this,null,function*(){var d;yield Ge();const n=(d=document.querySelectorAll(`.${l.contentClass}`))==null?void 0:d[0];if(!n){console.warn("Element not found for sortable initialization");return}const i=()=>G(null,null,function*(){var f;n.style.cursor="default",(f=n.querySelector(".draggable"))==null||f.classList.remove("dragging")}),{initializeSortable:r}=Vo(n,{filter:(f,p)=>{const h=Tt(p);return!(h==null?void 0:h.classList.contains("draggable"))||!l.draggable},onEnd(f){const{newIndex:p,oldIndex:h}=f,{srcElement:m}=f.originalEvent;if(!m){i();return}const v=Tt(m);if(!v){i();return}if(!v.classList.contains("draggable")){i();return}h!==void 0&&p!==void 0&&!Number.isNaN(h)&&!Number.isNaN(p)&&h!==p&&t("sortTabs",h,p),i()},onMove(f){const p=Tt(f.related);if(p!=null&&p.classList.contains("draggable")&&l.draggable){const h=f.dragged.classList.contains("affix-tab"),m=f.related.classList.contains("affix-tab");return h===m}else return!1},onStart:()=>{var f;n.style.cursor="grabbing",(f=n.querySelector(".draggable"))==null||f.classList.add("dragging")}});a.value=yield r()})}function s(){return G(this,null,function*(){const{isMobile:n}=_a();n.value||(yield Ge(),o())})}Je(s),ye(()=>l.styleType,()=>{var n;(n=a.value)==null||n.destroy(),s()}),Pt(()=>{var n;(n=a.value)==null||n.destroy()})}function Sd(l){let t=null,a=null,o=0;const s=Z(null),n=Z(null),i=Z(!1),r=Z(!0),d=Z(!1);function f(){var F;const D=(F=s.value)==null?void 0:F.$el;if(!D||!n.value)return{};const $=D.clientWidth,O=n.value.clientWidth;return{scrollbarWidth:$,scrollViewWidth:O}}function p(D,$=150){var P;const{scrollbarWidth:O,scrollViewWidth:F}=f();!O||!F||O>F||(P=n.value)==null||P.scrollBy({behavior:"smooth",left:D==="left"?-(O-$):+(O-$)})}function h(){return G(this,null,function*(){var O,F;yield Ge();const D=(O=s.value)==null?void 0:O.$el;if(!D)return;const $=D==null?void 0:D.querySelector("div[data-radix-scroll-area-viewport]");n.value=$,v(),yield Ge(),m(),t==null||t.disconnect(),t=new ResizeObserver(_t(P=>{v(),m()},100)),t.observe($),o=((F=l.tabs)==null?void 0:F.length)||0,a==null||a.disconnect(),a=new MutationObserver(()=>{const P=$.querySelectorAll('div[data-tab-item="true"]').length;P>o&&m(),P!==o&&(v(),o=P)}),a.observe($,{attributes:!1,childList:!0,subtree:!0})})}function m(){return G(this,null,function*(){if(!n.value)return;yield Ge();const D=n.value,{scrollbarWidth:$}=f(),{scrollWidth:O}=D;$>=O||requestAnimationFrame(()=>{const F=D==null?void 0:D.querySelector(".is-active");F==null||F.scrollIntoView({behavior:"smooth",inline:"start"})})})}function v(){return G(this,null,function*(){if(!n.value)return;const{scrollbarWidth:D}=f();i.value=n.value.scrollWidth>D})}const T=_t(({left:D,right:$})=>{r.value=D,d.value=$},100);function z({deltaY:D}){var $;($=n.value)==null||$.scrollBy({left:D*3})}return ye(()=>l.active,()=>G(null,null,function*(){m()}),{flush:"post"}),ye(()=>l.styleType,()=>{h()}),Je(h),Pt(()=>{t==null||t.disconnect(),a==null||a.disconnect(),t=null,a=null}),{handleScrollAt:T,handleWheel:z,initScrollbar:h,scrollbarRef:s,scrollDirection:p,scrollIsAtLeft:r,scrollIsAtRight:d,showScrollButton:i}}const Td={class:"flex h-full flex-1 overflow-hidden"},Md=M({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{},wheelable:{type:Boolean,default:!0}},emits:["close","sortTabs","unpin"],setup(l,{emit:t}){const a=l,o=t,s=Ie(a,o),{handleScrollAt:n,handleWheel:i,scrollbarRef:r,scrollDirection:d,scrollIsAtLeft:f,scrollIsAtRight:p,showScrollButton:h}=Sd(a);function m(v){a.wheelable&&(i(v),v.stopPropagation(),v.preventDefault())}return Cd(a,o),(v,T)=>(u(),S("div",Td,[ze(A("span",{class:L([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(f),"pointer-events-none opacity-30":e(f)},"border-r px-2"]),onClick:T[0]||(T[0]=z=>e(d)("left"))},[b(e(Eo),{class:"size-4 h-full"})],2),[[Le,e(h)]]),A("div",{class:L([{"pt-[3px]":v.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[b(e($t),{ref_key:"scrollbarRef",ref:r,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n),onWheel:m},{default:c(()=>[v.styleType==="chrome"?(u(),x(e(hd),Ve(pe({key:0},Q(Q(Q({},e(s)),v.$attrs),v.$props))),null,16)):(u(),x(e(kd),Ve(pe({key:1},Q(Q(Q({},e(s)),v.$attrs),v.$props))),null,16))]),_:1},8,["onScrollAt"])],2),ze(A("span",{class:L([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(p),"pointer-events-none opacity-30":e(p)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:T[1]||(T[1]=z=>e(d)("right"))},[b(e(Vt),{class:"size-4 h-full"})],2),[[Le,e(h)]])]))}});function Bd(){const l=et(),t=Re(),a=ft(),o=at(),{contentIsMaximize:s,toggleMaximize:n}=qa(),{closeAllTabs:i,closeCurrentTab:r,closeLeftTabs:d,closeOtherTabs:f,closeRightTabs:p,closeTabByKey:h,getTabDisableState:m,openTabInNewWindow:v,refreshTab:T,toggleTabPin:z}=Xa(),D=w(()=>t.fullPath),{locale:$}=Po(),O=Z();ye([()=>o.getTabs,()=>o.updateTime,()=>$.value],([B])=>{O.value=B.map(R=>j(R))});const F=()=>{const B=tn(l.getRoutes(),R=>{var J;return!!((J=R.meta)!=null&&J.affixTab)});o.setAffixTabs(B)},P=B=>{l.push(B)},K=B=>G(null,null,function*(){yield h(B)});function j(B){var R;return ke(Q({},B),{meta:ke(Q({},B==null?void 0:B.meta),{title:g((R=B==null?void 0:B.meta)==null?void 0:R.title)})})}return ye(()=>a.accessMenus,()=>{F()},{immediate:!0}),ye(()=>t.path,()=>{var R,J;const B=(J=(R=t.matched)==null?void 0:R[t.matched.length-1])==null?void 0:J.meta;o.addTab(ke(Q({},t),{meta:B||t.meta}))},{immediate:!0}),{createContextMenus:B=>{var Ce,Se;const{disabledCloseAll:R,disabledCloseCurrent:J,disabledCloseLeft:se,disabledCloseOther:ue,disabledCloseRight:we,disabledRefresh:W}=m(B),Y=(Se=(Ce=B==null?void 0:B.meta)==null?void 0:Ce.affixTab)!=null?Se:!1;return[{disabled:J,handler:()=>G(null,null,function*(){yield r(B)}),icon:st,key:"close",text:g("preferences.tabbar.contextMenu.close")},{handler:()=>G(null,null,function*(){yield z(B)}),icon:Y?In:At,key:"affix",text:Y?g("preferences.tabbar.contextMenu.unpin"):g("preferences.tabbar.contextMenu.pin")},{handler:()=>G(null,null,function*(){s.value||(yield l.push(B.fullPath)),n()}),icon:s.value?Ka:Fa,key:s.value?"restore-maximize":"maximize",text:s.value?g("preferences.tabbar.contextMenu.restoreMaximize"):g("preferences.tabbar.contextMenu.maximize")},{disabled:W,handler:T,icon:Lt,key:"reload",text:g("preferences.tabbar.contextMenu.reload")},{handler:()=>G(null,null,function*(){yield v(B)}),icon:En,key:"open-in-new-window",separator:!0,text:g("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:se,handler:()=>G(null,null,function*(){yield d(B)}),icon:Sn,key:"close-left",text:g("preferences.tabbar.contextMenu.closeLeft")},{disabled:we,handler:()=>G(null,null,function*(){yield p(B)}),icon:Mn,key:"close-right",separator:!0,text:g("preferences.tabbar.contextMenu.closeRight")},{disabled:ue,handler:()=>G(null,null,function*(){yield f(B)}),icon:Pn,key:"close-other",text:g("preferences.tabbar.contextMenu.closeOther")},{disabled:R,handler:i,icon:Tn,key:"close-all",text:g("preferences.tabbar.contextMenu.closeAll")}].filter(Be=>o.getMenuList.includes(Be.key))},currentActive:D,currentTabs:O,handleClick:P,handleClose:K}}const _d={class:"flex-center h-full"},$d=M({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(l){const t=Re(),a=at(),{contentIsMaximize:o,toggleMaximize:s}=qa(),{unpinTab:n}=Xa(),{createContextMenus:i,currentActive:r,currentTabs:d,handleClick:f,handleClose:p}=Bd(),h=w(()=>{const m=a.getTabByPath(r.value);return i(m).map(T=>ke(Q({},T),{label:T.text,value:T.key}))});return U.tabbar.persist||a.closeOtherTabs(t),(m,v)=>(u(),S(ee,null,[b(e(Md),{active:e(r),class:L(m.theme),"context-menus":e(i),draggable:e(U).tabbar.draggable,"show-icon":m.showIcon,"style-type":e(U).tabbar.styleType,tabs:e(d),wheelable:e(U).tabbar.wheelable,"middle-click-to-close":e(U).tabbar.middleClickToClose,onClose:e(p),onSortTabs:e(a).sortTabs,onUnpin:e(n),"onUpdate:active":e(f)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","wheelable","middle-click-to-close","onClose","onSortTabs","onUnpin","onUpdate:active"]),A("div",_d,[e(U).tabbar.showMore?(u(),x(e(rd),{key:0,menus:h.value},null,8,["menus"])):I("",!0),e(U).tabbar.showMaximize?(u(),x(e(id),{key:1,screen:e(o),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):I("",!0)])],64))}}),Vd=M({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout","clickLogo"],setup(l,{emit:t}){const a=t,{isDark:o,isHeaderNav:s,isMixedNav:n,isMobile:i,isSideMixedNav:r,isHeaderMixedNav:d,isHeaderSidebarNav:f,layout:p,preferencesButtonPosition:h,sidebarCollapsed:m,theme:v}=it(),T=ft(),{refresh:z}=Ja(),D=w(()=>o.value||U.theme.semiDarkSidebar?"dark":"light"),$=w(()=>o.value||U.theme.semiDarkHeader?"dark":"light");w(()=>{const{collapsedShowTitle:ne}=U.sidebar,ie=[];return ne&&m.value&&!n.value&&ie.push("mx-auto"),r.value&&ie.push("flex-center"),ie.join(" ")});const O=w(()=>U.navigation.styleType==="rounded");w(()=>i.value&&m.value?!0:s.value||n.value||f.value?!1:m.value||r.value||d.value);const F=w(()=>!i.value&&(s.value||n.value||d.value)),{handleMenuSelect:P,handleMenuOpen:K,headerActive:j,headerMenus:q,sidebarActive:B,sidebarMenus:R,mixHeaderMenus:J,sidebarVisible:se}=nd(),{extraActiveMenu:ue,extraMenus:we,handleDefaultSelect:W,handleMenuMouseEnter:Y,handleMixedMenuSelect:ve,handleSideMouseLeave:Ce,sidebarExtraVisible:Se}=od(J);function Be(ne,ie=!0){return ie?ln(ne,ce=>ke(Q({},va(ce)),{name:g(ce.name)})):ne.map(ce=>ke(Q({},va(ce)),{name:g(ce.name)}))}function Ee(){Ke({sidebar:{hidden:!U.sidebar.hidden}})}function N(){a("clearPreferencesAndLogout")}ye(()=>U.app.layout,ne=>G(null,null,function*(){ne==="sidebar-mixed-nav"&&U.sidebar.hidden&&Ke({sidebar:{hidden:!1}})})),ye(Io.global.locale,z,{flush:"post"});const le=He(),oe=w(()=>Object.keys(le).filter(ne=>ne.startsWith("header-")));return(ne,ie)=>(u(),x(e(Ii),{"sidebar-extra-visible":e(Se),"onUpdate:sidebarExtraVisible":ie[0]||(ie[0]=ce=>an(Se)?Se.value=ce:null),"content-compact":e(U).app.contentCompact,"footer-enable":e(U).footer.enable,"footer-fixed":e(U).footer.fixed,"header-hidden":e(U).header.hidden,"header-mode":e(U).header.mode,"header-theme":$.value,"header-toggle-sidebar-button":e(U).widget.sidebarToggle,"header-visible":e(U).header.enable,"is-mobile":e(U).app.isMobile,layout:e(p),"sidebar-collapse":e(U).sidebar.collapsed,"sidebar-collapse-show-title":e(U).sidebar.collapsedShowTitle,"sidebar-enable":e(se),"sidebar-collapsed-button":e(U).sidebar.collapsedButton,"sidebar-fixed-button":e(U).sidebar.fixedButton,"sidebar-expand-on-hover":e(U).sidebar.expandOnHover,"sidebar-extra-collapse":e(U).sidebar.extraCollapse,"sidebar-hidden":e(U).sidebar.hidden,"sidebar-theme":D.value,"sidebar-width":e(U).sidebar.width,"tabbar-enable":e(U).tabbar.enable,"tabbar-height":e(U).tabbar.height,onSideMouseLeave:e(Ce),onToggleSidebar:Ee,"onUpdate:sidebarCollapse":ie[1]||(ie[1]=ce=>e(Ke)({sidebar:{collapsed:ce}})),"onUpdate:sidebarEnable":ie[2]||(ie[2]=ce=>e(Ke)({sidebar:{enable:ce}})),"onUpdate:sidebarExpandOnHover":ie[3]||(ie[3]=ce=>e(Ke)({sidebar:{expandOnHover:ce}})),"onUpdate:sidebarExtraCollapse":ie[4]||(ie[4]=ce=>e(Ke)({sidebar:{extraCollapse:ce}}))},ut({header:c(()=>[b(e(Ki),{theme:e(v),onClearPreferencesAndLogout:N},ut({"user-dropdown":c(()=>[_(ne.$slots,"user-dropdown")]),notification:c(()=>[_(ne.$slots,"notification")]),_:2},[!F.value&&e(U).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[b(e(tr),{"hide-when-only-one":e(U).breadcrumb.hideOnlyOne,"show-home":e(U).breadcrumb.showHome,"show-icon":e(U).breadcrumb.showIcon,type:e(U).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,F.value?{name:"menu",fn:c(()=>[b(e(Ta),{"default-active":e(j),menus:Be(e(q)),rounded:O.value,theme:$.value,class:"w-full",mode:"horizontal",onSelect:e(P)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,be(oe.value,ce=>({name:ce,fn:c(()=>[_(ne.$slots,ce)])}))]),1032,["theme"])]),menu:c(()=>[b(e(Ta),{accordion:e(U).navigation.accordion,collapse:e(U).sidebar.collapsed,"collapse-show-title":e(U).sidebar.collapsedShowTitle,"default-active":e(B),menus:Be(e(R)),rounded:O.value,theme:D.value,mode:"vertical",onOpen:e(K),onSelect:e(P)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onOpen","onSelect"])]),"mixed-menu":c(()=>[b(e(ld),{"active-path":e(ue),menus:Be(e(J),!1),rounded:O.value,theme:D.value,onDefaultSelect:e(W),onEnter:e(Y),onSelect:e(ve)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[b(e(ad),{accordion:e(U).navigation.accordion,collapse:e(U).sidebar.extraCollapse,menus:Be(e(we)),rounded:O.value,theme:D.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(U).logo.enable?(u(),x(e(Fs),{key:0,text:e(U).app.name,theme:e(v)},ut({_:2},[ne.$slots["logo-text"]?{name:"text",fn:c(()=>[_(ne.$slots,"logo-text")]),key:"0"}:void 0]),1032,["text","theme"])):I("",!0)]),tabbar:c(()=>[e(U).tabbar.enable?(u(),x(e($d),{key:0,"show-icon":e(U).tabbar.showIcon,theme:e(v)},null,8,["show-icon","theme"])):I("",!0)]),content:c(()=>[b(e(Di))]),extra:c(()=>[_(ne.$slots,"extra"),e(U).app.enableCheckUpdates?(u(),x(e(ar),{key:0,"check-updates-interval":e(U).app.checkUpdatesInterval},null,8,["check-updates-interval"])):I("",!0),e(U).widget.lockScreen?(u(),x(rt,{key:1,name:"slide-up"},{default:c(()=>[e(T).isLockScreen?_(ne.$slots,"lock-screen",{key:0}):I("",!0)]),_:3})):I("",!0),e(h).fixed?(u(),x(e(_i),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:N})):I("",!0),b(e(xs))]),_:2},[e(U).transition.loading?{name:"content-overlay",fn:c(()=>[b(e(zi))]),key:"0"}:void 0,e(U).footer.enable?{name:"footer",fn:c(()=>[b(e(Ni),null,{default:c(()=>[e(U).copyright.enable?(u(),x(e(dn),Ve(pe({key:0},e(U).copyright)),null,16)):I("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","footer-enable","footer-fixed","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-collapsed-button","sidebar-fixed-button","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-hidden","sidebar-theme","sidebar-width","tabbar-enable","tabbar-height","onSideMouseLeave"]))}}),Hd=M({__name:"basic",setup(l){return(t,a)=>(u(),x(e(Vd)))}});export{Hd as default};
