import{d as s,A as a,e as n,o as r,v as c,s as p,g as _,q as d}from"../jse/index-index-Y3_OtjO-.js";/* empty css                                                                */import{_ as l}from"./bootstrap-MyT3sENS.js";const m={class:"statbar-container"},i=s({__name:"statbar",props:{message:{type:String,default:""}},setup(e){const t=e;return(f,u)=>{const o=a("coord");return r(),n("div",m,[c(o,null,{default:p(()=>[_("span",null,d(t.message),1)]),_:1})])}}}),x=l(i,[["__scopeId","data-v-bfb505e3"]]);export{x as default};
