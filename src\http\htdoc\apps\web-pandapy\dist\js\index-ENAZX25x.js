var rn=Object.defineProperty,sn=Object.defineProperties;var on=Object.getOwnPropertyDescriptors;var _=Object.getOwnPropertySymbols;var Ut=Object.prototype.hasOwnProperty,$t=Object.prototype.propertyIsEnumerable;var ut=(t,e,n)=>e in t?rn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,T=(t,e)=>{for(var n in e||(e={}))Ut.call(e,n)&&ut(t,n,e[n]);if(_)for(var n of _(e))$t.call(e,n)&&ut(t,n,e[n]);return t},P=(t,e)=>sn(t,on(e));var ft=(t,e)=>{var n={};for(var r in t)Ut.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&_)for(var r of _(t))e.indexOf(r)<0&&$t.call(t,r)&&(n[r]=t[r]);return n};var A=(t,e,n)=>ut(t,typeof e!="symbol"?e+"":e,n);var pt=(t,e,n)=>new Promise((r,s)=>{var i=c=>{try{a(n.next(c))}catch(l){s(l)}},o=c=>{try{a(n.throw(c))}catch(l){s(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,o);a((n=n.apply(t,e)).next())});import{d as se,u as z,ak as oe,X as ie,h as ae,F as an,ac as ce,r as w,$ as ot,c as L,i as cn,al as ln,am as Ot,w as C,an as un,ao as V,a6 as fn}from"../jse/index-index-Y3_OtjO-.js";const le=1/60*1e3,pn=typeof performance!="undefined"?()=>performance.now():()=>Date.now(),ue=typeof window!="undefined"?t=>window.requestAnimationFrame(t):t=>setTimeout(()=>t(pn()),le);function dn(t){let e=[],n=[],r=0,s=!1,i=!1;const o=new WeakSet,a={schedule:(c,l=!1,u=!1)=>{const f=u&&s,y=f?e:n;return l&&o.add(c),y.indexOf(c)===-1&&(y.push(c),f&&s&&(r=e.length)),c},cancel:c=>{const l=n.indexOf(c);l!==-1&&n.splice(l,1),o.delete(c)},process:c=>{if(s){i=!0;return}if(s=!0,[e,n]=[n,e],n.length=0,r=e.length,r)for(let l=0;l<r;l++){const u=e[l];u(c),o.has(u)&&(a.schedule(u),t())}s=!1,i&&(i=!1,a.process(c))}};return a}const mn=40;let vt=!0,q=!1,xt=!1;const B={delta:0,timestamp:0},W=["read","update","preRender","render","postRender"],it=W.reduce((t,e)=>(t[e]=dn(()=>q=!0),t),{}),Tt=W.reduce((t,e)=>{const n=it[e];return t[e]=(r,s=!1,i=!1)=>(q||hn(),n.schedule(r,s,i)),t},{}),yn=W.reduce((t,e)=>(t[e]=it[e].cancel,t),{});W.reduce((t,e)=>(t[e]=()=>it[e].process(B),t),{});const bn=t=>it[t].process(B),fe=t=>{q=!1,B.delta=vt?le:Math.max(Math.min(t-B.timestamp,mn),1),B.timestamp=t,xt=!0,W.forEach(bn),xt=!1,q&&(vt=!1,ue(fe))},hn=()=>{q=!0,vt=!0,xt||ue(fe)},pe=()=>B;function de(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(t);s<r.length;s++)e.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(t,r[s])&&(n[r[s]]=t[r[s]]);return n}var qt=function(){};const Mt=(t,e,n)=>Math.min(Math.max(n,t),e),Kt=.001,gn=.01,On=10,vn=.05,xn=1;function Tn({duration:t=800,bounce:e=.25,velocity:n=0,mass:r=1}){let s,i,o=1-e;o=Mt(vn,xn,o),t=Mt(gn,On,t/1e3),o<1?(s=l=>{const u=l*o,f=u*t,y=u-n,h=St(l,o),d=Math.exp(-f);return Kt-y/h*d},i=l=>{const f=l*o*t,y=f*n+n,h=Math.pow(o,2)*Math.pow(l,2)*t,d=Math.exp(-f),M=St(Math.pow(l,2),o);return(-s(l)+Kt>0?-1:1)*((y-h)*d)/M}):(s=l=>{const u=Math.exp(-l*t),f=(l-n)*t+1;return-.001+u*f},i=l=>{const u=Math.exp(-l*t),f=(n-l)*(t*t);return u*f});const a=5/t,c=Sn(s,i,a);if(t=t*1e3,isNaN(c))return{stiffness:100,damping:10,duration:t};{const l=Math.pow(c,2)*r;return{stiffness:l,damping:o*2*Math.sqrt(r*l),duration:t}}}const Mn=12;function Sn(t,e,n){let r=n;for(let s=1;s<Mn;s++)r=r-t(r)/e(r);return r}function St(t,e){return t*Math.sqrt(1-e*e)}const jn=["duration","bounce"],An=["stiffness","damping","mass"];function Wt(t,e){return e.some(n=>t[n]!==void 0)}function Vn(t){let e=Object.assign({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},t);if(!Wt(t,An)&&Wt(t,jn)){const n=Tn(t);e=Object.assign(Object.assign(Object.assign({},e),n),{velocity:0,mass:1}),e.isResolvedFromDuration=!0}return e}function wt(t){var{from:e=0,to:n=1,restSpeed:r=2,restDelta:s}=t,i=de(t,["from","to","restSpeed","restDelta"]);const o={done:!1,value:e};let{stiffness:a,damping:c,mass:l,velocity:u,duration:f,isResolvedFromDuration:y}=Vn(i),h=Zt,d=Zt;function M(){const x=u?-(u/1e3):0,v=n-e,g=c/(2*Math.sqrt(a*l)),m=Math.sqrt(a/l)/1e3;if(s===void 0&&(s=Math.min(Math.abs(n-e)/100,.4)),g<1){const b=St(m,g);h=O=>{const S=Math.exp(-g*m*O);return n-S*((x+g*m*v)/b*Math.sin(b*O)+v*Math.cos(b*O))},d=O=>{const S=Math.exp(-g*m*O);return g*m*S*(Math.sin(b*O)*(x+g*m*v)/b+v*Math.cos(b*O))-S*(Math.cos(b*O)*(x+g*m*v)-b*v*Math.sin(b*O))}}else if(g===1)h=b=>n-Math.exp(-m*b)*(v+(x+m*v)*b);else{const b=m*Math.sqrt(g*g-1);h=O=>{const S=Math.exp(-g*m*O),R=Math.min(b*O,300);return n-S*((x+g*m*v)*Math.sinh(R)+b*v*Math.cosh(R))/b}}}return M(),{next:x=>{const v=h(x);if(y)o.done=x>=f;else{const g=d(x)*1e3,m=Math.abs(g)<=r,b=Math.abs(n-v)<=s;o.done=m&&b}return o.value=o.done?n:v,o},flipTarget:()=>{u=-u,[e,n]=[n,e],M()}}}wt.needsInterpolation=(t,e)=>typeof t=="string"||typeof e=="string";const Zt=t=>0,me=(t,e,n)=>{const r=e-t;return r===0?1:(n-t)/r},Et=(t,e,n)=>-n*t+n*e+t,ye=(t,e)=>n=>Math.max(Math.min(n,e),t),H=t=>t%1?Number(t.toFixed(5)):t,K=/(-)?([\d]*\.?[\d])+/g,jt=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Cn=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Z(t){return typeof t=="string"}const Y={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},U=Object.assign(Object.assign({},Y),{transform:ye(0,1)}),J=Object.assign(Object.assign({},Y),{default:1}),Nt=t=>({test:e=>Z(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),I=Nt("deg"),$=Nt("%"),p=Nt("px"),Yt=Object.assign(Object.assign({},$),{parse:t=>$.parse(t)/100,transform:t=>$.transform(t*100)}),Ft=(t,e)=>n=>!!(Z(n)&&Cn.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),be=(t,e,n)=>r=>{if(!Z(r))return r;const[s,i,o,a]=r.match(K);return{[t]:parseFloat(s),[e]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},k={test:Ft("hsl","hue"),parse:be("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:r=1})=>"hsla("+Math.round(t)+", "+$.transform(H(e))+", "+$.transform(H(n))+", "+H(U.transform(r))+")"},Rn=ye(0,255),dt=Object.assign(Object.assign({},Y),{transform:t=>Math.round(Rn(t))}),F={test:Ft("rgb","red"),parse:be("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:r=1})=>"rgba("+dt.transform(t)+", "+dt.transform(e)+", "+dt.transform(n)+", "+H(U.transform(r))+")"};function wn(t){let e="",n="",r="",s="";return t.length>5?(e=t.substr(1,2),n=t.substr(3,2),r=t.substr(5,2),s=t.substr(7,2)):(e=t.substr(1,1),n=t.substr(2,1),r=t.substr(3,1),s=t.substr(4,1),e+=e,n+=n,r+=r,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const At={test:Ft("#"),parse:wn,transform:F.transform},j={test:t=>F.test(t)||At.test(t)||k.test(t),parse:t=>F.test(t)?F.parse(t):k.test(t)?k.parse(t):At.parse(t),transform:t=>Z(t)?t:t.hasOwnProperty("red")?F.transform(t):k.transform(t)},he="${c}",ge="${n}";function En(t){var e,n,r,s;return isNaN(t)&&Z(t)&&((n=(e=t.match(K))===null||e===void 0?void 0:e.length)!==null&&n!==void 0?n:0)+((s=(r=t.match(jt))===null||r===void 0?void 0:r.length)!==null&&s!==void 0?s:0)>0}function Oe(t){typeof t=="number"&&(t=`${t}`);const e=[];let n=0;const r=t.match(jt);r&&(n=r.length,t=t.replace(jt,he),e.push(...r.map(j.parse)));const s=t.match(K);return s&&(t=t.replace(K,ge),e.push(...s.map(Y.parse))),{values:e,numColors:n,tokenised:t}}function ve(t){return Oe(t).values}function xe(t){const{values:e,numColors:n,tokenised:r}=Oe(t),s=e.length;return i=>{let o=r;for(let a=0;a<s;a++)o=o.replace(a<n?he:ge,a<n?j.transform(i[a]):H(i[a]));return o}}const Nn=t=>typeof t=="number"?0:t;function Fn(t){const e=ve(t);return xe(t)(e.map(Nn))}const G={test:En,parse:ve,createTransformer:xe,getAnimatableNone:Fn},In=new Set(["brightness","contrast","saturate","opacity"]);function Dn(t){let[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[r]=n.match(K)||[];if(!r)return t;const s=n.replace(r,"");let i=In.has(e)?1:0;return r!==n&&(i*=100),e+"("+i+s+")"}const kn=/([a-z-]*)\(.*?\)/g,Vt=Object.assign(Object.assign({},G),{getAnimatableNone:t=>{const e=t.match(kn);return e?e.map(Dn).join(" "):t}});function mt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Gt({hue:t,saturation:e,lightness:n,alpha:r}){t/=360,e/=100,n/=100;let s=0,i=0,o=0;if(!e)s=i=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;s=mt(c,a,t+1/3),i=mt(c,a,t),o=mt(c,a,t-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}const Pn=(t,e,n)=>{const r=t*t,s=e*e;return Math.sqrt(Math.max(0,n*(s-r)+r))},Ln=[At,F,k],Xt=t=>Ln.find(e=>e.test(t)),Te=(t,e)=>{let n=Xt(t),r=Xt(e),s=n.parse(t),i=r.parse(e);n===k&&(s=Gt(s),n=F),r===k&&(i=Gt(i),r=F);const o=Object.assign({},s);return a=>{for(const c in o)c!=="alpha"&&(o[c]=Pn(s[c],i[c],a));return o.alpha=Et(s.alpha,i.alpha,a),n.transform(o)}},Bn=t=>typeof t=="number",zn=(t,e)=>n=>e(t(n)),Me=(...t)=>t.reduce(zn);function Se(t,e){return Bn(t)?n=>Et(t,e,n):j.test(t)?Te(t,e):Ae(t,e)}const je=(t,e)=>{const n=[...t],r=n.length,s=t.map((i,o)=>Se(i,e[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}},Hn=(t,e)=>{const n=Object.assign(Object.assign({},t),e),r={};for(const s in n)t[s]!==void 0&&e[s]!==void 0&&(r[s]=Se(t[s],e[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}};function _t(t){const e=G.parse(t),n=e.length;let r=0,s=0,i=0;for(let o=0;o<n;o++)r||typeof e[o]=="number"?r++:e[o].hue!==void 0?i++:s++;return{parsed:e,numNumbers:r,numRGB:s,numHSL:i}}const Ae=(t,e)=>{const n=G.createTransformer(e),r=_t(t),s=_t(e);return r.numHSL===s.numHSL&&r.numRGB===s.numRGB&&r.numNumbers>=s.numNumbers?Me(je(r.parsed,s.parsed),n):o=>`${o>0?e:t}`},Un=(t,e)=>n=>Et(t,e,n);function $n(t){if(typeof t=="number")return Un;if(typeof t=="string")return j.test(t)?Te:Ae;if(Array.isArray(t))return je;if(typeof t=="object")return Hn}function qn(t,e,n){const r=[],s=n||$n(t[0]),i=t.length-1;for(let o=0;o<i;o++){let a=s(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]:e;a=Me(c,a)}r.push(a)}return r}function Kn([t,e],[n]){return r=>n(me(t,e,r))}function Wn(t,e){const n=t.length,r=n-1;return s=>{let i=0,o=!1;if(s<=t[0]?o=!0:s>=t[r]&&(i=r-1,o=!0),!o){let c=1;for(;c<n&&!(t[c]>s||c===r);c++);i=c-1}const a=me(t[i],t[i+1],s);return e[i](a)}}function Ve(t,e,{clamp:n=!0,ease:r,mixer:s}={}){const i=t.length;qt(i===e.length),qt(!r||!Array.isArray(r)||r.length===i-1),t[0]>t[i-1]&&(t=[].concat(t),e=[].concat(e),t.reverse(),e.reverse());const o=qn(e,r,s),a=i===2?Kn(t,o):Wn(t,o);return n?c=>a(Mt(t[0],t[i-1],c)):a}const at=t=>e=>1-t(1-e),It=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Zn=t=>e=>Math.pow(e,t),Ce=t=>e=>e*e*((t+1)*e-t),Yn=t=>{const e=Ce(t);return n=>(n*=2)<1?.5*e(n):.5*(2-Math.pow(2,-10*(n-1)))},Re=1.525,Gn=4/11,Xn=8/11,_n=9/10,we=t=>t,Dt=Zn(2),Jn=at(Dt),Ee=It(Dt),Ne=t=>1-Math.sin(Math.acos(t)),Fe=at(Ne),Qn=It(Fe),kt=Ce(Re),tr=at(kt),er=It(kt),nr=Yn(Re),rr=4356/361,sr=35442/1805,or=16061/1805,nt=t=>{if(t===1||t===0)return t;const e=t*t;return t<Gn?7.5625*e:t<Xn?9.075*e-9.9*t+3.4:t<_n?rr*e-sr*t+or:10.8*t*t-20.52*t+10.72},ir=at(nt),ar=t=>t<.5?.5*(1-nt(1-t*2)):.5*nt(t*2-1)+.5;function cr(t,e){return t.map(()=>e||Ee).splice(0,t.length-1)}function lr(t){const e=t.length;return t.map((n,r)=>r!==0?r/(e-1):0)}function ur(t,e){return t.map(n=>n*e)}function tt({from:t=0,to:e=1,ease:n,offset:r,duration:s=300}){const i={done:!1,value:t},o=Array.isArray(e)?e:[t,e],a=ur(r&&r.length===o.length?r:lr(o),s);function c(){return Ve(a,o,{ease:Array.isArray(n)?n:cr(o,n)})}let l=c();return{next:u=>(i.value=l(u),i.done=u>=s,i),flipTarget:()=>{o.reverse(),l=c()}}}function fr({velocity:t=0,from:e=0,power:n=.8,timeConstant:r=350,restDelta:s=.5,modifyTarget:i}){const o={done:!1,value:e};let a=n*t;const c=e+a,l=i===void 0?c:i(c);return l!==c&&(a=l-e),{next:u=>{const f=-a*Math.exp(-u/r);return o.done=!(f>s||f<-s),o.value=o.done?l:l+f,o},flipTarget:()=>{}}}const Jt={keyframes:tt,spring:wt,decay:fr};function pr(t){if(Array.isArray(t.to))return tt;if(Jt[t.type])return Jt[t.type];const e=new Set(Object.keys(t));return e.has("ease")||e.has("duration")&&!e.has("dampingRatio")?tt:e.has("dampingRatio")||e.has("stiffness")||e.has("mass")||e.has("damping")||e.has("restSpeed")||e.has("restDelta")?wt:tt}function Ie(t,e,n=0){return t-e-n}function dr(t,e,n=0,r=!0){return r?Ie(e+-t,e,n):e-(t-e)+n}function mr(t,e,n,r){return r?t>=e+n:t<=-n}const yr=t=>{const e=({delta:n})=>t(n);return{start:()=>Tt.update(e,!0),stop:()=>yn.update(e)}};function De(t){var e,n,{from:r,autoplay:s=!0,driver:i=yr,elapsed:o=0,repeat:a=0,repeatType:c="loop",repeatDelay:l=0,onPlay:u,onStop:f,onComplete:y,onRepeat:h,onUpdate:d}=t,M=de(t,["from","autoplay","driver","elapsed","repeat","repeatType","repeatDelay","onPlay","onStop","onComplete","onRepeat","onUpdate"]);let{to:x}=M,v,g=0,m=M.duration,b,O=!1,S=!0,R;const X=pr(M);!((n=(e=X).needsInterpolation)===null||n===void 0)&&n.call(e,r,x)&&(R=Ve([0,100],[r,x],{clamp:!1}),r=0,x=100);const N=X(Object.assign(Object.assign({},M),{from:r,to:x}));function Qe(){g++,c==="reverse"?(S=g%2===0,o=dr(o,m,l,S)):(o=Ie(o,m,l),c==="mirror"&&N.flipTarget()),O=!1,h&&h()}function tn(){v.stop(),y&&y()}function en(lt){if(S||(lt=-lt),o+=lt,!O){const Ht=N.next(Math.max(0,o));b=Ht.value,R&&(b=R(b)),O=S?Ht.done:o<=0}d==null||d(b),O&&(g===0&&(m!=null||(m=o)),g<a?mr(o,m,l,S)&&Qe():tn())}function nn(){u==null||u(),v=i(en),v.start()}return s&&nn(),{stop:()=>{f==null||f(),v.stop()}}}function ke(t,e){return e?t*(1e3/e):0}function br({from:t=0,velocity:e=0,min:n,max:r,power:s=.8,timeConstant:i=750,bounceStiffness:o=500,bounceDamping:a=10,restDelta:c=1,modifyTarget:l,driver:u,onUpdate:f,onComplete:y,onStop:h}){let d;function M(m){return n!==void 0&&m<n||r!==void 0&&m>r}function x(m){return n===void 0?r:r===void 0||Math.abs(n-m)<Math.abs(r-m)?n:r}function v(m){d==null||d.stop(),d=De(Object.assign(Object.assign({},m),{driver:u,onUpdate:b=>{var O;f==null||f(b),(O=m.onUpdate)===null||O===void 0||O.call(m,b)},onComplete:y,onStop:h}))}function g(m){v(Object.assign({type:"spring",stiffness:o,damping:a,restDelta:c},m))}if(M(t))g({from:t,velocity:e,to:x(t)});else{let m=s*e+t;typeof l!="undefined"&&(m=l(m));const b=x(m),O=b===n?-1:1;let S,R;const X=N=>{S=R,R=N,e=ke(N-S,pe().delta),(O===1&&N>b||O===-1&&N<b)&&g({from:N,to:b,velocity:e})};v({type:"decay",from:t,velocity:e,timeConstant:i,power:s,restDelta:c,modifyTarget:l,onUpdate:M(m)?X:void 0})}return{stop:()=>d==null?void 0:d.stop()}}const Pe=(t,e)=>1-3*e+3*t,Le=(t,e)=>3*e-6*t,Be=t=>3*t,rt=(t,e,n)=>((Pe(e,n)*t+Le(e,n))*t+Be(e))*t,ze=(t,e,n)=>3*Pe(e,n)*t*t+2*Le(e,n)*t+Be(e),hr=1e-7,gr=10;function Or(t,e,n,r,s){let i,o,a=0;do o=e+(n-e)/2,i=rt(o,r,s)-t,i>0?n=o:e=o;while(Math.abs(i)>hr&&++a<gr);return o}const vr=8,xr=.001;function Tr(t,e,n,r){for(let s=0;s<vr;++s){const i=ze(e,n,r);if(i===0)return e;const o=rt(e,n,r)-t;e-=o/i}return e}const et=11,Q=1/(et-1);function Mr(t,e,n,r){if(t===e&&n===r)return we;const s=new Float32Array(et);for(let o=0;o<et;++o)s[o]=rt(o*Q,t,n);function i(o){let a=0,c=1;const l=et-1;for(;c!==l&&s[c]<=o;++c)a+=Q;--c;const u=(o-s[c])/(s[c+1]-s[c]),f=a+u*Q,y=ze(f,t,n);return y>=xr?Tr(o,f,t,n):y===0?f:Or(o,a,a+Q,t,n)}return o=>o===0||o===1?o:rt(i(o),e,r)}const E={},yt={};class Sr{constructor(){A(this,"subscriptions",new Set)}add(e){return this.subscriptions.add(e),()=>this.subscriptions.delete(e)}notify(e,n,r){if(this.subscriptions.size)for(const s of this.subscriptions)s(e,n,r)}clear(){this.subscriptions.clear()}}function Qt(t){return!Number.isNaN(Number.parseFloat(t))}class jr{constructor(e){A(this,"current");A(this,"prev");A(this,"timeDelta",0);A(this,"lastUpdated",0);A(this,"updateSubscribers",new Sr);A(this,"stopAnimation");A(this,"canTrackVelocity",!1);A(this,"updateAndNotify",e=>{this.prev=this.current,this.current=e;const{delta:n,timestamp:r}=pe();this.lastUpdated!==r&&(this.timeDelta=n,this.lastUpdated=r),Tt.postRender(this.scheduleVelocityCheck),this.updateSubscribers.notify(this.current)});A(this,"scheduleVelocityCheck",()=>Tt.postRender(this.velocityCheck));A(this,"velocityCheck",({timestamp:e})=>{this.canTrackVelocity||(this.canTrackVelocity=Qt(this.current)),e!==this.lastUpdated&&(this.prev=this.current)});this.prev=this.current=e,this.canTrackVelocity=Qt(this.current)}onChange(e){return this.updateSubscribers.add(e)}clearListeners(){this.updateSubscribers.clear()}set(e){this.updateAndNotify(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ke(Number.parseFloat(this.current)-Number.parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{const{stop:r}=e(n);this.stopAnimation=r}).then(()=>this.clearAnimation())}stop(){this.stopAnimation&&this.stopAnimation(),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.updateSubscribers.clear(),this.stop()}}function Ar(t){return new jr(t)}const{isArray:Vr}=Array;function Cr(){const t=w({}),e=r=>{const s=i=>{t.value[i]&&(t.value[i].stop(),t.value[i].destroy(),delete t.value[i])};r?Vr(r)?r.forEach(s):s(r):Object.keys(t.value).forEach(s)},n=(r,s,i)=>{if(t.value[r])return t.value[r];const o=Ar(s);return o.onChange(a=>i[r]=a),t.value[r]=o,o};return un(e),{motionValues:t,get:n,stop:e}}function Rr(t){return Array.isArray(t)}function D(){return{type:"spring",stiffness:500,damping:25,restDelta:.5,restSpeed:10}}function bt(t){return{type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restDelta:.01,restSpeed:10}}function wr(t){return{type:"spring",stiffness:550,damping:t===0?100:30,restDelta:.01,restSpeed:10}}function ht(){return{type:"keyframes",ease:"linear",duration:300}}function Er(t){return{type:"keyframes",duration:800,values:t}}const te={default:wr,x:D,y:D,z:D,rotate:D,rotateX:D,rotateY:D,rotateZ:D,scaleX:bt,scaleY:bt,scale:bt,backgroundColor:ht,color:ht,opacity:ht};function He(t,e){let n;return Rr(e)?n=Er:n=te[t]||te.default,T({to:e},n(e))}const ee=P(T({},Y),{transform:Math.round}),Ue={color:j,backgroundColor:j,outlineColor:j,fill:j,stroke:j,borderColor:j,borderTopColor:j,borderRightColor:j,borderBottomColor:j,borderLeftColor:j,borderWidth:p,borderTopWidth:p,borderRightWidth:p,borderBottomWidth:p,borderLeftWidth:p,borderRadius:p,radius:p,borderTopLeftRadius:p,borderTopRightRadius:p,borderBottomRightRadius:p,borderBottomLeftRadius:p,width:p,maxWidth:p,height:p,maxHeight:p,size:p,top:p,right:p,bottom:p,left:p,padding:p,paddingTop:p,paddingRight:p,paddingBottom:p,paddingLeft:p,margin:p,marginTop:p,marginRight:p,marginBottom:p,marginLeft:p,rotate:I,rotateX:I,rotateY:I,rotateZ:I,scale:J,scaleX:J,scaleY:J,scaleZ:J,skew:I,skewX:I,skewY:I,distance:p,translateX:p,translateY:p,translateZ:p,x:p,y:p,z:p,perspective:p,transformPerspective:p,opacity:U,originX:Yt,originY:Yt,originZ:p,zIndex:ee,filter:Vt,WebkitFilter:Vt,fillOpacity:U,strokeOpacity:U,numOctaves:ee},Pt=t=>Ue[t];function Ct(t,e){return e&&typeof t=="number"&&e.transform?e.transform(t):t}function Nr(t,e){let n=Pt(t);return n!==Vt&&(n=G),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Fr={linear:we,easeIn:Dt,easeInOut:Ee,easeOut:Jn,circIn:Ne,circInOut:Qn,circOut:Fe,backIn:kt,backInOut:er,backOut:tr,anticipate:nr,bounceIn:ir,bounceInOut:ar,bounceOut:nt};function ne(t){if(Array.isArray(t)){const[e,n,r,s]=t;return Mr(e,n,r,s)}else if(typeof t=="string")return Fr[t];return t}function Ir(t){return Array.isArray(t)&&typeof t[0]!="number"}function re(t,e){return t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&G.test(e)&&!e.startsWith("url("))}function Dr(t){return Array.isArray(t.to)&&t.to[0]===null&&(t.to=[...t.to],t.to[0]=t.from),t}function kr(s){var i=s,{ease:t,times:e,delay:n}=i,r=ft(i,["ease","times","delay"]);const o=T({},r);return e&&(o.offset=e),t&&(o.ease=Ir(t)?t.map(ne):ne(t)),n&&(o.elapsed=-n),o}function Pr(t,e,n){return Array.isArray(e.to)&&(t.duration||(t.duration=800)),Dr(e),Lr(t)||(t=T(T({},t),He(n,e.to))),T(T({},e),kr(t))}function Lr(o){var a=o,{delay:t,repeat:e,repeatType:n,repeatDelay:r,from:s}=a,i=ft(a,["delay","repeat","repeatType","repeatDelay","from"]);return!!Object.keys(i).length}function Br(t,e){return t[e]||t.default||t}function zr(t,e,n,r,s){const i=Br(r,t);let o=i.from===null||i.from===void 0?e.get():i.from;const a=re(t,n);o==="none"&&a&&typeof n=="string"&&(o=Nr(t,n));const c=re(t,o);function l(f){const y={from:o,to:n,velocity:r.velocity?r.velocity:e.getVelocity(),onUpdate:h=>e.set(h)};return i.type==="inertia"||i.type==="decay"?br(T(T({},y),i)):De(P(T({},Pr(i,y,t)),{onUpdate:h=>{y.onUpdate(h),i.onUpdate&&i.onUpdate(h)},onComplete:()=>{s&&s(),f&&f()}}))}function u(f){return e.set(n),s&&s(),f&&f(),{stop:()=>{}}}return!c||!a||i.type===!1?u:l}function Hr(){const{motionValues:t,stop:e,get:n}=Cr();return{motionValues:t,stop:e,push:(s,i,o,a={},c)=>{const l=o[s],u=n(s,l,o);if(a&&a.immediate){u.set(i);return}const f=zr(s,u,i,a,c);u.start(f)}}}function Ur(t,e={},{motionValues:n,push:r,stop:s}=Hr()){const i=z(e),o=w(!1);C(n,f=>{o.value=Object.values(f).filter(y=>y.isAnimating()).length>0},{immediate:!0,deep:!0});const a=f=>{if(!i||!i[f])throw new Error(`The variant ${f} does not exist.`);return i[f]},c=f=>{typeof f=="string"&&(f=a(f));const y=Object.entries(f).map(([d,M])=>{if(d!=="transition")return new Promise(x=>r(d,M,t,f.transition||He(d,f[d]),x))}).filter(Boolean);function h(){return pt(this,null,function*(){var d,M;yield Promise.all(y),(M=(d=f.transition)==null?void 0:d.onComplete)==null||M.call(d)})}return Promise.all([h()])};return{isAnimating:o,apply:c,set:f=>{const y=Ot(f)?f:a(f);Object.entries(y).forEach(([h,d])=>{h!=="transition"&&r(h,d,t,{immediate:!0})})},leave:f=>pt(null,null,function*(){let y;if(i&&(i.leave&&(y=i.leave),!i.leave&&i.initial&&(y=i.initial)),!y){f();return}yield c(y),f()}),stop:s}}const Lt=typeof window!="undefined",$r=()=>Lt&&(window.onpointerdown===null||(E==null?void 0:E.TEST)),qr=()=>Lt&&(window.ontouchstart===null||(E==null?void 0:E.TEST)),Kr=()=>Lt&&(window.onmousedown===null||(E==null?void 0:E.TEST));function Wr({target:t,state:e,variants:n,apply:r}){const s=z(n),i=w(!1),o=w(!1),a=w(!1),c=L(()=>{let u=[...Object.keys(e.value||{})];return s&&(s.hovered&&(u=[...u,...Object.keys(s.hovered)]),s.tapped&&(u=[...u,...Object.keys(s.tapped)]),s.focused&&(u=[...u,...Object.keys(s.focused)])),u}),l=L(()=>{const u={};Object.assign(u,e.value),i.value&&s.hovered&&Object.assign(u,s.hovered),o.value&&s.tapped&&Object.assign(u,s.tapped),a.value&&s.focused&&Object.assign(u,s.focused);for(const f in u)c.value.includes(f)||delete u[f];return u});s.hovered&&(V(t,"mouseenter",()=>i.value=!0),V(t,"mouseleave",()=>{i.value=!1,o.value=!1})),s.tapped&&(Kr()&&(V(t,"mousedown",()=>o.value=!0),V(t,"mouseup",()=>o.value=!1)),$r()&&(V(t,"pointerdown",()=>o.value=!0),V(t,"pointerup",()=>o.value=!1)),qr()&&(V(t,"touchstart",()=>o.value=!0),V(t,"touchend",()=>o.value=!1))),s.focused&&(V(t,"focus",()=>a.value=!0),V(t,"blur",()=>a.value=!1)),C([i,o,a],()=>{r(l.value)})}function Zr({set:t,target:e,variants:n,variant:r}){const s=z(n);C(()=>e,()=>{s&&(s.initial&&(t("initial"),r.value="initial"),s.enter&&(r.value="enter"))},{immediate:!0,flush:"pre"})}function Yr({state:t,apply:e}){C(t,n=>{n&&e(n)},{immediate:!0})}function $e({target:t,variants:e,variant:n}){const r=z(e);r&&(r.visible||r.visibleOnce)&&ln(t,([{isIntersecting:s}])=>{r.visible?s?n.value="visible":n.value="initial":r.visibleOnce&&(s&&n.value!=="visibleOnce"?n.value="visibleOnce":n.value||(n.value="initial"))})}function Gr(t,e={syncVariants:!0,lifeCycleHooks:!0,visibilityHooks:!0,eventListeners:!0}){e.lifeCycleHooks&&Zr(t),e.syncVariants&&Yr(t),e.visibilityHooks&&$e(t),e.eventListeners&&Wr(t)}function qe(t={}){const e=ot(T({},t)),n=w({});return C(e,()=>{const r={};for(const[s,i]of Object.entries(e)){const o=Pt(s),a=Ct(i,o);r[s]=a}n.value=r},{immediate:!0,deep:!0}),{state:e,style:n}}function Bt(t,e){C(()=>fn(t),n=>{n&&e(n)},{immediate:!0})}const Xr={x:"translateX",y:"translateY",z:"translateZ"};function Ke(t={},e=!0){const n=ot(T({},t)),r=w("");return C(n,s=>{let i="",o=!1;if(e&&(s.x||s.y||s.z)){const a=[s.x||0,s.y||0,s.z||0].map(c=>Ct(c,p)).join(",");i+=`translate3d(${a}) `,o=!0}for(const[a,c]of Object.entries(s)){if(e&&(a==="x"||a==="y"||a==="z"))continue;const l=Pt(a),u=Ct(c,l);i+=`${Xr[a]||a}(${u}) `}e&&!o&&(i+="translateZ(0px) "),r.value=i.trim()},{immediate:!0,deep:!0}),{state:n,transform:r}}const _r=["","X","Y","Z"],Jr=["perspective","translate","scale","rotate","skew"],We=["transformPerspective","x","y","z"];Jr.forEach(t=>{_r.forEach(e=>{const n=t+e;We.push(n)})});const Qr=new Set(We);function zt(t){return Qr.has(t)}const ts=new Set(["originX","originY","originZ"]);function Ze(t){return ts.has(t)}function es(t){const e={},n={};return Object.entries(t).forEach(([r,s])=>{zt(r)||Ze(r)?e[r]=s:n[r]=s}),{transform:e,style:n}}function ct(t){const{transform:e,style:n}=es(t),{transform:r}=Ke(e),{style:s}=qe(n);return r.value&&(s.value.transform=r.value),s.value}function ns(t,e){let n,r;const{state:s,style:i}=qe();return Bt(t,o=>{r=o;for(const a of Object.keys(Ue))o.style[a]===null||o.style[a]===""||zt(a)||Ze(a)||(s[a]=o.style[a]);n&&Object.entries(n).forEach(([a,c])=>o.style[a]=c),e&&e(s)}),C(i,o=>{if(!r){n=o;return}for(const a in o)r.style[a]=o[a]},{immediate:!0}),{style:s}}function rs(t){const e=t.trim().split(/\) |\)/);if(e.length===1)return{};const n=r=>r.endsWith("px")||r.endsWith("deg")?Number.parseFloat(r):Number.isNaN(Number(r))?Number(r):r;return e.reduce((r,s)=>{if(!s)return r;const[i,o]=s.split("("),c=o.split(",").map(u=>n(u.endsWith(")")?u.replace(")",""):u.trim())),l=c.length===1?c[0]:c;return P(T({},r),{[i]:l})},{})}function ss(t,e){Object.entries(rs(e)).forEach(([n,r])=>{const s=["x","y","z"];if(n==="translate3d"){if(r===0){s.forEach(i=>t[i]=0);return}r.forEach((i,o)=>t[s[o]]=i);return}if(r=Number.parseFloat(`${r}`),n==="translateX"){t.x=r;return}if(n==="translateY"){t.y=r;return}if(n==="translateZ"){t.z=r;return}t[n]=r})}function os(t,e){let n,r;const{state:s,transform:i}=Ke();return Bt(t,o=>{r=o,o.style.transform&&ss(s,o.style.transform),n&&(o.style.transform=n),e&&e(s)}),C(i,o=>{if(!r){n=o;return}r.style.transform=o},{immediate:!0}),{transform:s}}function is(t){return Object.entries(t)}function as(t,e){const n=ot({}),r=o=>Object.entries(o).forEach(([a,c])=>n[a]=c),{style:s}=ns(t,r),{transform:i}=os(t,r);return C(n,o=>{is(o).forEach(([a,c])=>{const l=zt(a)?i:s;l[a]&&l[a]===c||(l[a]=c)})},{immediate:!0,deep:!0}),Bt(t,()=>e),{motionProperties:n,style:s,transform:i}}function cs(t={}){const e=z(t),n=w();return{state:L(()=>{if(n.value)return e[n.value]}),variant:n}}function Ye(t,e={},n){const{motionProperties:r}=as(t),{variant:s,state:i}=cs(e),o=Ur(r,e),a=T({target:t,variant:s,variants:e,state:i,motionProperties:r},o);return Gr(a,n),a}const Ge=["delay","duration"],ls=["initial","enter","leave","visible","visible-once","visibleOnce","hovered","tapped","focused",...Ge];function us(t){return Ge.includes(t)}function fs(t,e){var r;const n=t.props?t.props:t.data&&t.data.attrs?t.data.attrs:{};if(n){n.variants&&Ot(n.variants)&&(e.value=T(T({},e.value),n.variants));for(let s of ls)if(!(!n||!n[s])){if(us(s)&&typeof n[s]=="number"){for(const i of["enter","visible","visibleOnce"]){const o=e.value[i];o!=null&&((r=o.transition)!=null||(o.transition={}),o.transition[s]=n[s])}continue}if(Ot(n[s])){const i=n[s];s==="visible-once"&&(s="visibleOnce"),e.value[s]=i}}}}function gt(t,e=!1){return{created:(s,i,o)=>{const a=i.value&&typeof i.value=="string"?i.value:o.key;a&&yt[a]&&yt[a].stop();const c=e?structuredClone(ce(t)||{}):t||{},l=w(c);typeof i.value=="object"&&(l.value=i.value),fs(o,l);const f=Ye(s,l,{eventListeners:!0,lifeCycleHooks:!0,syncVariants:!0,visibilityHooks:!1});s.motionInstance=f,a&&(yt[a]=f)},mounted:(s,i,o)=>{s.motionInstance&&$e(s.motionInstance)},getSSRProps(s,i){let{initial:o}=s.value||i&&(i==null?void 0:i.props)||{};o=z(o);const a=oe({},(t==null?void 0:t.initial)||{},o||{});return!a||Object.keys(a).length===0?void 0:{style:ct(a)}}}}const ps={initial:{opacity:0},enter:{opacity:1}},ds={initial:{opacity:0},visible:{opacity:1}},ms={initial:{opacity:0},visibleOnce:{opacity:1}},ys={initial:{scale:0,opacity:0},enter:{scale:1,opacity:1}},bs={initial:{scale:0,opacity:0},visible:{scale:1,opacity:1}},hs={initial:{scale:0,opacity:0},visibleOnce:{scale:1,opacity:1}},gs={initial:{x:-100,rotate:90,opacity:0},enter:{x:0,rotate:0,opacity:1}},Os={initial:{x:-100,rotate:90,opacity:0},visible:{x:0,rotate:0,opacity:1}},vs={initial:{x:-100,rotate:90,opacity:0},visibleOnce:{x:0,rotate:0,opacity:1}},xs={initial:{x:100,rotate:-90,opacity:0},enter:{x:0,rotate:0,opacity:1}},Ts={initial:{x:100,rotate:-90,opacity:0},visible:{x:0,rotate:0,opacity:1}},Ms={initial:{x:100,rotate:-90,opacity:0},visibleOnce:{x:0,rotate:0,opacity:1}},Ss={initial:{y:-100,rotate:-90,opacity:0},enter:{y:0,rotate:0,opacity:1}},js={initial:{y:-100,rotate:-90,opacity:0},visible:{y:0,rotate:0,opacity:1}},As={initial:{y:-100,rotate:-90,opacity:0},visibleOnce:{y:0,rotate:0,opacity:1}},Vs={initial:{y:100,rotate:90,opacity:0},enter:{y:0,rotate:0,opacity:1}},Cs={initial:{y:100,rotate:90,opacity:0},visible:{y:0,rotate:0,opacity:1}},Rs={initial:{y:100,rotate:90,opacity:0},visibleOnce:{y:0,rotate:0,opacity:1}},ws={initial:{x:-100,opacity:0},enter:{x:0,opacity:1}},Es={initial:{x:-100,opacity:0},visible:{x:0,opacity:1}},Ns={initial:{x:-100,opacity:0},visibleOnce:{x:0,opacity:1}},Fs={initial:{x:100,opacity:0},enter:{x:0,opacity:1}},Is={initial:{x:100,opacity:0},visible:{x:0,opacity:1}},Ds={initial:{x:100,opacity:0},visibleOnce:{x:0,opacity:1}},ks={initial:{y:-100,opacity:0},enter:{y:0,opacity:1}},Ps={initial:{y:-100,opacity:0},visible:{y:0,opacity:1}},Ls={initial:{y:-100,opacity:0},visibleOnce:{y:0,opacity:1}},Bs={initial:{y:100,opacity:0},enter:{y:0,opacity:1}},zs={initial:{y:100,opacity:0},visible:{y:0,opacity:1}},Hs={initial:{y:100,opacity:0},visibleOnce:{y:0,opacity:1}},st={__proto__:null,fade:ps,fadeVisible:ds,fadeVisibleOnce:ms,pop:ys,popVisible:bs,popVisibleOnce:hs,rollBottom:Vs,rollLeft:gs,rollRight:xs,rollTop:Ss,rollVisibleBottom:Cs,rollVisibleLeft:Os,rollVisibleOnceBottom:Rs,rollVisibleOnceLeft:vs,rollVisibleOnceRight:Ms,rollVisibleOnceTop:As,rollVisibleRight:Ts,rollVisibleTop:js,slideBottom:Bs,slideLeft:ws,slideRight:Fs,slideTop:ks,slideVisibleBottom:zs,slideVisibleLeft:Es,slideVisibleOnceBottom:Hs,slideVisibleOnceLeft:Ns,slideVisibleOnceRight:Ds,slideVisibleOnceTop:Ls,slideVisibleRight:Is,slideVisibleTop:Ps};function Us(t){const e="àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;",n="aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrsssssttuuuuuuuuuwxyyzzz------",r=new RegExp(e.split("").join("|"),"g");return t.toString().replace(/[A-Z]/g,s=>`-${s}`).toLowerCase().replace(/\s+/g,"-").replace(r,s=>n.charAt(e.indexOf(s))).replace(/&/g,"-and-").replace(/[^\w\-]+/g,"").replace(/-{2,}/g,"-").replace(/^-+/,"").replace(/-+$/,"")}const Xe=Symbol(""),_e={preset:{type:String,required:!1},instance:{type:Object,required:!1},variants:{type:Object,required:!1},initial:{type:Object,required:!1},enter:{type:Object,required:!1},leave:{type:Object,required:!1},visible:{type:Object,required:!1},visibleOnce:{type:Object,required:!1},hovered:{type:Object,required:!1},tapped:{type:Object,required:!1},focused:{type:Object,required:!1},delay:{type:[Number,String],required:!1},duration:{type:[Number,String],required:!1}};function $s(t){return Object.prototype.toString.call(t)==="[object Object]"}function Rt(t){if(Array.isArray(t))return t.map(Rt);if($s(t)){const e={};for(const n in t)e[n]=Rt(t[n]);return e}return t}function Je(t){const e=ot({}),n=cn(Xe,{}),r=L(()=>t.preset==null?{}:n!=null&&t.preset in n?structuredClone(ce(n)[t.preset]):t.preset in st?structuredClone(st[t.preset]):{}),s=L(()=>({initial:t.initial,enter:t.enter,leave:t.leave,visible:t.visible,visibleOnce:t.visibleOnce,hovered:t.hovered,tapped:t.tapped,focused:t.focused}));function i(c,l){var u;for(const f of["delay","duration"]){if(l[f]==null)continue;const y=Number.parseInt(l[f]);for(const h of["enter","visible","visibleOnce"]){const d=c[h];d!=null&&((u=d.transition)!=null||(d.transition={}),d.transition[f]=y)}}return c}const o=L(()=>{const c=oe({},s.value,r.value,t.variants||{});return i(T({},c),t)});function a(c,l,u){var y,h,d;(y=c.props)!=null||(c.props={}),(d=(h=c.props).style)!=null||(h.style={}),c.props.style=T(T({},c.props.style),u);const f=i(Rt(o.value),c.props);return c.props.onVnodeMounted=({el:M})=>{e[l]=Ye(M,f)},c.props.onVnodeUpdated=({el:M})=>{const x=ct(e[l].state);for(const[v,g]of Object.entries(x))M.style[v]=g},c}return{motionConfig:o,setNodeInstance:a}}const qs=se({name:"Motion",props:P(T({},_e),{is:{type:[String,Object],default:"div"}}),setup(t){const e=ie(),{motionConfig:n,setNodeInstance:r}=Je(t);return()=>{const s=ct(n.value.initial||{}),i=ae(t.is,void 0,e);return r(i,0,s),i}}}),Ks=se({name:"MotionGroup",props:P(T({},_e),{is:{type:[String,Object],required:!1}}),setup(t){const e=ie(),{motionConfig:n,setNodeInstance:r}=Je(t);return()=>{var o;const s=ct(n.value.initial||{}),i=((o=e.default)==null?void 0:o.call(e))||[];for(let a=0;a<i.length;a++){const c=i[a];c.type===an&&Array.isArray(c.children)?c.children.forEach(function l(u,f){if(u!=null){if(Array.isArray(u)){l(u,f);return}typeof u=="object"&&r(u,f,s)}}):r(c,a,s)}return t.is?ae(t.is,void 0,i):i}}}),Ys={install(t,e){if(t.directive("motion",gt()),!e||e&&!e.excludePresets)for(const n in st){const r=st[n];t.directive(`motion-${Us(n)}`,gt(r,!0))}if(e&&e.directives)for(const n in e.directives){const r=e.directives[n];r.initial,t.directive(`motion-${n}`,gt(r,!0))}t.provide(Xe,e==null?void 0:e.directives),t.component("Motion",qs),t.component("MotionGroup",Ks)}};export{qs as Motion,gt as MotionDirective,Ks as MotionGroup,Ys as MotionPlugin};
