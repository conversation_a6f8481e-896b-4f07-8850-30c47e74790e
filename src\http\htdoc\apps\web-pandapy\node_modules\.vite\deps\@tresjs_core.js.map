{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@vueuse+shared@12.8.2_typescript@5.8.3/node_modules/@vueuse/shared/index.mjs", "../../../../../node_modules/.pnpm/@vueuse+core@12.8.2_typescript@5.8.3/node_modules/@vueuse/core/index.mjs", "../../../../../node_modules/.pnpm/@tresjs+core@4.3.6_three@0.177.0_typescript@5.8.3_vue@3.5.13_typescript@5.8.3_/node_modules/@tresjs/core/dist/tres.js"], "sourcesContent": ["import { shallowRef, watchEffect, readonly, watch, customRef, getCurrentScope, onScopeDispose, effectScope, getCurrentInstance, hasInjectionContext, inject, provide, ref, isRef, unref, toValue as toValue$1, computed, reactive, toRefs as toRefs$1, toRef as toRef$1, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue';\n\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, {\n    ...options,\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  });\n  return readonly(result);\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = shallowRef(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = typeof fn === \"function\" ? fn : fn.get;\n  const set = typeof fn === \"function\" ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get(v);\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createEventHook() {\n  const fns = /* @__PURE__ */ new Set();\n  const off = (fn) => {\n    fns.delete(fn);\n  };\n  const clear = () => {\n    fns.clear();\n  };\n  const on = (fn) => {\n    fns.add(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (...args) => {\n    return Promise.all(Array.from(fns).map((fn) => fn(...args)));\n  };\n  return {\n    on,\n    off,\n    trigger,\n    clear\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return (...args) => {\n    if (!initialized) {\n      state = scope.run(() => stateFactory(...args));\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nconst localProvidedStateMap = /* @__PURE__ */ new WeakMap();\n\nconst injectLocal = (...args) => {\n  var _a;\n  const key = args[0];\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null && !hasInjectionContext())\n    throw new Error(\"injectLocal must be called in setup\");\n  if (instance && localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance))\n    return localProvidedStateMap.get(instance)[key];\n  return inject(...args);\n};\n\nconst provideLocal = (key, value) => {\n  var _a;\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null)\n    throw new Error(\"provideLocal must be called in setup\");\n  if (!localProvidedStateMap.has(instance))\n    localProvidedStateMap.set(instance, /* @__PURE__ */ Object.create(null));\n  const localProvidedState = localProvidedStateMap.get(instance);\n  localProvidedState[key] = value;\n  provide(key, value);\n};\n\nfunction createInjectionState(composable, options) {\n  const key = (options == null ? void 0 : options.injectionKey) || Symbol(composable.name || \"InjectionState\");\n  const defaultValue = options == null ? void 0 : options.defaultValue;\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provideLocal(key, state);\n    return state;\n  };\n  const useInjectedState = () => injectLocal(key, defaultValue);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction createRef(value, deep) {\n  if (deep === true) {\n    return ref(value);\n  } else {\n    return shallowRef(value);\n  }\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!scope) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = { ...obj };\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : toValue$1;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(\n    keys.map((key) => {\n      const value = obj[key];\n      return [\n        key,\n        typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n      ];\n    })\n  );\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => !predicate(toValue$1(v), k))) : Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nconst isClient = typeof window !== \"undefined\" && typeof document !== \"undefined\";\nconst isWorker = typeof WorkerGlobalScope !== \"undefined\" && globalThis instanceof WorkerGlobalScope;\nconst isDef = (val) => typeof val !== \"undefined\";\nconst notNullish = (val) => val != null;\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\nconst isIOS = /* @__PURE__ */ getIsIOS();\nfunction getIsIOS() {\n  var _a, _b;\n  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  let lastInvoker;\n  const filter = (invoke) => {\n    const duration = toValue$1(ms);\n    const maxDuration = toValue$1(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      lastInvoker = invoke;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = null;\n          resolve(lastInvoker());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(...args) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  let ms;\n  let trailing;\n  let leading;\n  let rejectOnCancel;\n  if (!isRef(args[0]) && typeof args[0] === \"object\")\n    ({ delay: ms, trailing = true, leading = true, rejectOnCancel = false } = args[0]);\n  else\n    [ms, trailing = true, leading = true, rejectOnCancel = false] = args;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = toValue$1(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter, options = {}) {\n  const {\n    initialState = \"active\"\n  } = options;\n  const isActive = toRef(initialState === \"active\");\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nfunction cacheStringFunction(fn) {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (str) => {\n    const hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n}\nconst hyphenateRE = /\\B([A-Z])/g;\nconst hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, \"-$1\").toLowerCase());\nconst camelizeRE = /-(\\w)/g;\nconst camelize = cacheStringFunction((str) => {\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : \"\");\n});\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?\\d+\\.?\\d*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = Number.parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction pxValue(px) {\n  return px.endsWith(\"rem\") ? Number.parseFloat(px) * 16 : Number.parseFloat(px);\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\nfunction objectOmit(obj, keys, omitUndefined = false) {\n  return Object.fromEntries(Object.entries(obj).filter(([key, value]) => {\n    return (!omitUndefined || value !== void 0) && !keys.includes(key);\n  }));\n}\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\nfunction getLifeCycleTarget(target) {\n  return target || getCurrentInstance();\n}\nfunction toArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\nfunction toRef(...args) {\n  if (args.length !== 1)\n    return toRef$1(...args);\n  const r = args[0];\n  return typeof r === \"function\" ? readonly(customRef(() => ({ get: r, set: noop }))) : ref(r);\n}\nconst resolveRef = toRef;\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => predicate(toValue$1(v), k))) : Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = toValue$1(defaultValue);\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = toValue$1(defaultValue);\n      trigger();\n    }, toValue$1(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(\n    debounceFilter(ms, options),\n    fn\n  );\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(value.value);\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return debounced;\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(\n    throttleFilter(ms, trailing, leading, rejectOnCancel),\n    fn\n  );\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(value.value);\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(\n    ref,\n    {\n      get,\n      set,\n      untrackedGet,\n      silentSet,\n      peek,\n      lay\n    },\n    { enumerable: true }\n  );\n}\nconst controlledRef = refWithControl;\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    const [target, key, value] = args;\n    target[key] = value;\n  }\n}\n\nfunction watchWithFilter(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  return watch(\n    source,\n    createFilterWrapper(\n      eventFilter,\n      cb\n    ),\n    watchOptions\n  );\n}\n\nfunction watchPausable(source, cb, options = {}) {\n  const {\n    eventFilter: filter,\n    initialState = \"active\",\n    ...watchOptions\n  } = options;\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter, { initialState });\n  const stop = watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter\n    }\n  );\n  return { stop, pause, resume, isActive };\n}\n\nfunction syncRef(left, right, ...[options]) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options || {};\n  const watchers = [];\n  const transformLTR = \"ltr\" in transform && transform.ltr || ((v) => v);\n  const transformRTL = \"rtl\" in transform && transform.rtl || ((v) => v);\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchers.push(watchPausable(\n      left,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        right.value = transformLTR(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchers.push(watchPausable(\n      right,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        left.value = transformRTL(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  const stop = () => {\n    watchers.forEach((w) => w.stop());\n  };\n  return stop;\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  const targetsArray = toArray(targets);\n  return watch(\n    source,\n    (newValue) => targetsArray.forEach((target) => target.value = newValue),\n    { flush, deep, immediate }\n  );\n}\n\nfunction toRefs(objectRef, options = {}) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? Array.from({ length: objectRef.value.length }) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        var _a;\n        const replaceRef = (_a = toValue$1(options.replaceRef)) != null ? _a : true;\n        if (replaceRef) {\n          if (Array.isArray(objectRef.value)) {\n            const copy = [...objectRef.value];\n            copy[key] = v;\n            objectRef.value = copy;\n          } else {\n            const newObject = { ...objectRef.value, [key]: v };\n            Object.setPrototypeOf(newObject, Object.getPrototypeOf(objectRef.value));\n            objectRef.value = newObject;\n          }\n        } else {\n          objectRef.value[key] = v;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nconst toValue = toValue$1;\nconst resolveUnref = toValue$1;\n\nfunction tryOnBeforeMount(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeMount(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeUnmount(fn, target);\n}\n\nfunction tryOnMounted(fn, sync = true, target) {\n  const instance = getLifeCycleTarget();\n  if (instance)\n    onMounted(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onUnmounted(fn, target);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        r,\n        (v) => {\n          if (condition(v) !== isNot) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => stop == null ? void 0 : stop())\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        [r, value],\n        ([v1, v2]) => {\n          if (isNot !== (v1 === v2)) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v1);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => {\n          stop == null ? void 0 : stop();\n          return toValue$1(r);\n        })\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(toValue$1(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(toValue$1(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction defaultComparator(value, othVal) {\n  return value === othVal;\n}\nfunction useArrayDifference(...args) {\n  var _a, _b;\n  const list = args[0];\n  const values = args[1];\n  let compareFn = (_a = args[2]) != null ? _a : defaultComparator;\n  const {\n    symmetric = false\n  } = (_b = args[3]) != null ? _b : {};\n  if (typeof compareFn === \"string\") {\n    const key = compareFn;\n    compareFn = (value, othVal) => value[key] === othVal[key];\n  }\n  const diff1 = computed(() => toValue$1(list).filter((x) => toValue$1(values).findIndex((y) => compareFn(x, y)) === -1));\n  if (symmetric) {\n    const diff2 = computed(() => toValue$1(values).filter((x) => toValue$1(list).findIndex((y) => compareFn(x, y)) === -1));\n    return computed(() => symmetric ? [...toValue$1(diff1), ...toValue$1(diff2)] : toValue$1(diff1));\n  } else {\n    return diff1;\n  }\n}\n\nfunction useArrayEvery(list, fn) {\n  return computed(() => toValue$1(list).every((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction useArrayFilter(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).filter(fn));\n}\n\nfunction useArrayFind(list, fn) {\n  return computed(() => toValue$1(\n    toValue$1(list).find((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => toValue$1(list).findIndex((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => toValue$1(\n    !Array.prototype.findLast ? findLast(toValue$1(list), (element, index, array) => fn(toValue$1(element), index, array)) : toValue$1(list).findLast((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction isArrayIncludesOptions(obj) {\n  return isObject(obj) && containsProp(obj, \"formIndex\", \"comparator\");\n}\nfunction useArrayIncludes(...args) {\n  var _a;\n  const list = args[0];\n  const value = args[1];\n  let comparator = args[2];\n  let formIndex = 0;\n  if (isArrayIncludesOptions(comparator)) {\n    formIndex = (_a = comparator.fromIndex) != null ? _a : 0;\n    comparator = comparator.comparator;\n  }\n  if (typeof comparator === \"string\") {\n    const key = comparator;\n    comparator = (element, value2) => element[key] === toValue$1(value2);\n  }\n  comparator = comparator != null ? comparator : (element, value2) => element === toValue$1(value2);\n  return computed(() => toValue$1(list).slice(formIndex).some((element, index, array) => comparator(\n    toValue$1(element),\n    toValue$1(value),\n    index,\n    toValue$1(array)\n  )));\n}\n\nfunction useArrayJoin(list, separator) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).join(toValue$1(separator)));\n}\n\nfunction useArrayMap(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).map(fn));\n}\n\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(toValue$1(sum), toValue$1(value), index);\n  return computed(() => {\n    const resolved = toValue$1(list);\n    return args.length ? resolved.reduce(reduceCallback, typeof args[0] === \"function\" ? toValue$1(args[0]()) : toValue$1(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\nfunction useArraySome(list, fn) {\n  return computed(() => toValue$1(list).some((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction uniq(array) {\n  return Array.from(new Set(array));\n}\nfunction uniqueElementsBy(array, fn) {\n  return array.reduce((acc, v) => {\n    if (!acc.some((x) => fn(v, x, array)))\n      acc.push(v);\n    return acc;\n  }, []);\n}\nfunction useArrayUnique(list, compareFn) {\n  return computed(() => {\n    const resolvedList = toValue$1(list).map((element) => toValue$1(element));\n    return compareFn ? uniqueElementsBy(resolvedList, compareFn) : uniq(resolvedList);\n  });\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  let _initialValue = unref(initialValue);\n  const count = shallowRef(initialValue);\n  const {\n    max = Number.POSITIVE_INFINITY,\n    min = Number.NEGATIVE_INFINITY\n  } = options;\n  const inc = (delta = 1) => count.value = Math.max(Math.min(max, count.value + delta), min);\n  const dec = (delta = 1) => count.value = Math.min(Math.max(min, count.value - delta), max);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = _initialValue) => {\n    _initialValue = val;\n    return set(val);\n  };\n  return { count, inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[T\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/i;\nconst REGEX_FORMAT = /[YMDHhms]o|\\[([^\\]]+)\\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;\nfunction defaultMeridiem(hours, minutes, isLowercase, hasPeriod) {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n}\nfunction formatOrdinal(num) {\n  const suffixes = [\"th\", \"st\", \"nd\", \"rd\"];\n  const v = num % 100;\n  return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\n}\nfunction formatDate(date, formatStr, options = {}) {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const stripTimeZone = (dateString) => {\n    var _a2;\n    return (_a2 = dateString.split(\" \")[1]) != null ? _a2 : \"\";\n  };\n  const matches = {\n    Yo: () => formatOrdinal(years),\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    Mo: () => formatOrdinal(month + 1),\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"long\" }),\n    D: () => String(days),\n    Do: () => formatOrdinal(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    Ho: () => formatOrdinal(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    ho: () => formatOrdinal(hours % 12 || 12),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mo: () => formatOrdinal(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    so: () => formatOrdinal(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true),\n    z: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"longOffset\" }))\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => {\n    var _a2, _b;\n    return (_b = $1 != null ? $1 : (_a2 = matches[match]) == null ? void 0 : _a2.call(matches)) != null ? _b : match;\n  });\n}\nfunction normalizeDate(date) {\n  if (date === null)\n    return new Date(Number.NaN);\n  if (date === void 0)\n    return /* @__PURE__ */ new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n}\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(toValue$1(date)), toValue$1(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = shallowRef(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = toValue$1(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    if (isActive.value)\n      timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || typeof interval === \"function\") {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = shallowRef(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(\n    callback ? () => {\n      update();\n      callback(counter.value);\n    } : update,\n    interval,\n    { immediate }\n  );\n  if (exposeControls) {\n    return {\n      counter,\n      reset,\n      ...controls\n    };\n  } else {\n    return counter;\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = shallowRef((_a = options.initialValue) != null ? _a : null);\n  watch(\n    source,\n    () => ms.value = timestamp(),\n    options\n  );\n  return ms;\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const isPending = shallowRef(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    if (immediateCallback)\n      cb();\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, toValue$1(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: readonly(isPending),\n    start,\n    stop\n  };\n}\n\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(\n    callback != null ? callback : noop,\n    interval,\n    options\n  );\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return {\n      ready,\n      ...controls\n    };\n  } else {\n    return ready;\n  }\n}\n\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = toValue$1(value);\n    if (typeof method === \"function\")\n      resolved = method(resolved);\n    else if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && Number.isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\nfunction useToString(value) {\n  return computed(() => `${toValue$1(value)}`);\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = shallowRef(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = toValue$1(truthyValue);\n      _value.value = _value.value === truthy ? toValue$1(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [...typeof source === \"function\" ? source() : Array.isArray(source) ? source : toValue$1(source)];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = Array.from({ length: oldList.length });\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nfunction watchAtMost(source, cb, options) {\n  const {\n    count,\n    ...watchOptions\n  } = options;\n  const current = shallowRef(0);\n  const stop = watchWithFilter(\n    source,\n    (...args) => {\n      current.value += 1;\n      if (current.value >= toValue$1(count))\n        nextTick(() => stop());\n      cb(...args);\n    },\n    watchOptions\n  );\n  return { count: current, stop };\n}\n\nfunction watchDebounced(source, cb, options = {}) {\n  const {\n    debounce = 0,\n    maxWait = void 0,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: debounceFilter(debounce, { maxWait })\n    }\n  );\n}\n\nfunction watchDeep(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      deep: true\n    }\n  );\n}\n\nfunction watchIgnorable(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  const filteredCb = createFilterWrapper(\n    eventFilter,\n    cb\n  );\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = shallowRef(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(\n      source,\n      (...args) => {\n        if (!ignore.value)\n          filteredCb(...args);\n      },\n      watchOptions\n    );\n  } else {\n    const disposables = [];\n    const ignoreCounter = shallowRef(0);\n    const syncCounter = shallowRef(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(\n      watch(\n        source,\n        () => {\n          syncCounter.value++;\n        },\n        { ...watchOptions, flush: \"sync\" }\n      )\n    );\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(\n      watch(\n        source,\n        (...args) => {\n          const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n          ignoreCounter.value = 0;\n          syncCounter.value = 0;\n          if (ignore)\n            return;\n          filteredCb(...args);\n        },\n        watchOptions\n      )\n    );\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchImmediate(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      immediate: true\n    }\n  );\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n  return stop;\n}\n\nfunction watchThrottled(source, cb, options = {}) {\n  const {\n    throttle = 0,\n    trailing = true,\n    leading = true,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: throttleFilter(throttle, trailing, leading)\n    }\n  );\n}\n\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return {\n    ...res,\n    trigger\n  };\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => toValue$1(item));\n  return toValue$1(sources);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  const stop = watch(\n    source,\n    (v, ov, onInvalidate) => {\n      if (v) {\n        if (options == null ? void 0 : options.once)\n          nextTick(() => stop());\n        cb(v, ov, onInvalidate);\n      }\n    },\n    {\n      ...options,\n      once: false\n    }\n  );\n  return stop;\n}\n\nexport { assert, refAutoReset as autoResetRef, bypassFilter, camelize, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createRef, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, computedEager as eagerComputed, extendRef, formatDate, get, getLifeCycleTarget, hasOwn, hyphenate, identity, watchIgnorable as ignorableWatch, increaseWithUnit, injectLocal, invoke, isClient, isDef, isDefined, isIOS, isObject, isWorker, makeDestructurable, noop, normalizeDate, notNullish, now, objectEntries, objectOmit, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, provideLocal, pxValue, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toArray, toReactive, toRef, toRefs, toValue, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayDifference, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayIncludes, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchDeep, watchIgnorable, watchImmediate, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "import { noop, makeDestructurable, camelize, isClient, toArray, watchImmediate, isObject, tryOnScopeDispose, isIOS, notNullish, tryOnMounted, objectOmit, promiseTimeout, until, injectLocal, provideLocal, pxValue, increaseWithUnit, objectEntries, createRef, createSingletonPromise, useTimeoutFn, pausableWatch, toRef, createEventHook, useIntervalFn, computedWithControl, timestamp, pausableFilter, watchIgnorable, debounceFilter, bypassFilter, createFilterWrapper, toRefs, watchOnce, containsProp, hasOwn, throttleFilter, useDebounceFn, useThrottleFn, tryOnUnmounted, clamp, syncRef, objectPick, watchWithFilter, identity, isDef, whenever, isWorker } from '@vueuse/shared';\nexport * from '@vueuse/shared';\nimport { isRef, shallowRef, ref, watchEffect, computed, inject, defineComponent, h, TransitionGroup, shallowReactive, Fragment, toValue, unref, getCurrentInstance, onMounted, watch, customRef, onUpdated, readonly, reactive, hasInjectionContext, toRaw, nextTick, markRaw, getCurrentScope, isReadonly, onBeforeUpdate } from 'vue';\n\nfunction computedAsync(evaluationCallback, initialState, optionsOrRef) {\n  let options;\n  if (isRef(optionsOrRef)) {\n    options = {\n      evaluating: optionsOrRef\n    };\n  } else {\n    options = optionsOrRef || {};\n  }\n  const {\n    lazy = false,\n    evaluating = void 0,\n    shallow = true,\n    onError = noop\n  } = options;\n  const started = shallowRef(!lazy);\n  const current = shallow ? shallowRef(initialState) : ref(initialState);\n  let counter = 0;\n  watchEffect(async (onInvalidate) => {\n    if (!started.value)\n      return;\n    counter++;\n    const counterAtBeginning = counter;\n    let hasFinished = false;\n    if (evaluating) {\n      Promise.resolve().then(() => {\n        evaluating.value = true;\n      });\n    }\n    try {\n      const result = await evaluationCallback((cancelCallback) => {\n        onInvalidate(() => {\n          if (evaluating)\n            evaluating.value = false;\n          if (!hasFinished)\n            cancelCallback();\n        });\n      });\n      if (counterAtBeginning === counter)\n        current.value = result;\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (evaluating && counterAtBeginning === counter)\n        evaluating.value = false;\n      hasFinished = true;\n    }\n  });\n  if (lazy) {\n    return computed(() => {\n      started.value = true;\n      return current.value;\n    });\n  } else {\n    return current;\n  }\n}\n\nfunction computedInject(key, options, defaultSource, treatDefaultAsFactory) {\n  let source = inject(key);\n  if (defaultSource)\n    source = inject(key, defaultSource);\n  if (treatDefaultAsFactory)\n    source = inject(key, defaultSource, treatDefaultAsFactory);\n  if (typeof options === \"function\") {\n    return computed((ctx) => options(source, ctx));\n  } else {\n    return computed({\n      get: (ctx) => options.get(source, ctx),\n      set: options.set\n    });\n  }\n}\n\nfunction createReusableTemplate(options = {}) {\n  const {\n    inheritAttrs = true\n  } = options;\n  const render = shallowRef();\n  const define = /*@__PURE__*/ defineComponent({\n    setup(_, { slots }) {\n      return () => {\n        render.value = slots.default;\n      };\n    }\n  });\n  const reuse = /*@__PURE__*/ defineComponent({\n    inheritAttrs,\n    props: options.props,\n    setup(props, { attrs, slots }) {\n      return () => {\n        var _a;\n        if (!render.value && process.env.NODE_ENV !== \"production\")\n          throw new Error(\"[VueUse] Failed to find the definition of reusable template\");\n        const vnode = (_a = render.value) == null ? void 0 : _a.call(render, {\n          ...options.props == null ? keysToCamelKebabCase(attrs) : props,\n          $slots: slots\n        });\n        return inheritAttrs && (vnode == null ? void 0 : vnode.length) === 1 ? vnode[0] : vnode;\n      };\n    }\n  });\n  return makeDestructurable(\n    { define, reuse },\n    [define, reuse]\n  );\n}\nfunction keysToCamelKebabCase(obj) {\n  const newObj = {};\n  for (const key in obj)\n    newObj[camelize(key)] = obj[key];\n  return newObj;\n}\n\nfunction createTemplatePromise(options = {}) {\n  let index = 0;\n  const instances = ref([]);\n  function create(...args) {\n    const props = shallowReactive({\n      key: index++,\n      args,\n      promise: void 0,\n      resolve: () => {\n      },\n      reject: () => {\n      },\n      isResolving: false,\n      options\n    });\n    instances.value.push(props);\n    props.promise = new Promise((_resolve, _reject) => {\n      props.resolve = (v) => {\n        props.isResolving = true;\n        return _resolve(v);\n      };\n      props.reject = _reject;\n    }).finally(() => {\n      props.promise = void 0;\n      const index2 = instances.value.indexOf(props);\n      if (index2 !== -1)\n        instances.value.splice(index2, 1);\n    });\n    return props.promise;\n  }\n  function start(...args) {\n    if (options.singleton && instances.value.length > 0)\n      return instances.value[0].promise;\n    return create(...args);\n  }\n  const component = /*@__PURE__*/ defineComponent((_, { slots }) => {\n    const renderList = () => instances.value.map((props) => {\n      var _a;\n      return h(Fragment, { key: props.key }, (_a = slots.default) == null ? void 0 : _a.call(slots, props));\n    });\n    if (options.transition)\n      return () => h(TransitionGroup, options.transition, renderList);\n    return renderList;\n  });\n  component.start = start;\n  return component;\n}\n\nfunction createUnrefFn(fn) {\n  return function(...args) {\n    return fn.apply(this, args.map((i) => toValue(i)));\n  };\n}\n\nconst defaultWindow = isClient ? window : void 0;\nconst defaultDocument = isClient ? window.document : void 0;\nconst defaultNavigator = isClient ? window.navigator : void 0;\nconst defaultLocation = isClient ? window.location : void 0;\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = toValue(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nfunction useEventListener(...args) {\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options) => {\n    el.addEventListener(event, listener, options);\n    return () => el.removeEventListener(event, listener, options);\n  };\n  const firstParamTargets = computed(() => {\n    const test = toArray(toValue(args[0])).filter((e) => e != null);\n    return test.every((e) => typeof e !== \"string\") ? test : void 0;\n  });\n  const stopWatch = watchImmediate(\n    () => {\n      var _a, _b;\n      return [\n        (_b = (_a = firstParamTargets.value) == null ? void 0 : _a.map((e) => unrefElement(e))) != null ? _b : [defaultWindow].filter((e) => e != null),\n        toArray(toValue(firstParamTargets.value ? args[1] : args[0])),\n        toArray(unref(firstParamTargets.value ? args[2] : args[1])),\n        // @ts-expect-error - TypeScript gets the correct types, but somehow still complains\n        toValue(firstParamTargets.value ? args[3] : args[2])\n      ];\n    },\n    ([raw_targets, raw_events, raw_listeners, raw_options]) => {\n      cleanup();\n      if (!(raw_targets == null ? void 0 : raw_targets.length) || !(raw_events == null ? void 0 : raw_events.length) || !(raw_listeners == null ? void 0 : raw_listeners.length))\n        return;\n      const optionsClone = isObject(raw_options) ? { ...raw_options } : raw_options;\n      cleanups.push(\n        ...raw_targets.flatMap(\n          (el) => raw_events.flatMap(\n            (event) => raw_listeners.map((listener) => register(el, event, listener, optionsClone))\n          )\n        )\n      );\n    },\n    { flush: \"post\" }\n  );\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(cleanup);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false, controls = false } = options;\n  if (!window) {\n    return controls ? { stop: noop, cancel: noop, trigger: noop } : noop;\n  }\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    const listenerOptions = { passive: true };\n    Array.from(window.document.body.children).forEach((el) => useEventListener(el, \"click\", noop, listenerOptions));\n    useEventListener(window.document.documentElement, \"click\", noop, listenerOptions);\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return toValue(ignore).some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  function hasMultipleRoots(target2) {\n    const vm = toValue(target2);\n    return vm && vm.$.subTree.shapeFlag === 16;\n  }\n  function checkMultipleRoots(target2, event) {\n    const vm = toValue(target2);\n    const children = vm.$.subTree && vm.$.subTree.children;\n    if (children == null || !Array.isArray(children))\n      return false;\n    return children.some((child) => child.el === event.target || event.composedPath().includes(child.el));\n  }\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (event.target == null)\n      return;\n    if (!(el instanceof Element) && hasMultipleRoots(target) && checkMultipleRoots(target, event))\n      return;\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (\"detail\" in event && event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  let isProcessingClick = false;\n  const cleanup = [\n    useEventListener(window, \"click\", (event) => {\n      if (!isProcessingClick) {\n        isProcessingClick = true;\n        setTimeout(() => {\n          isProcessingClick = false;\n        }, 0);\n        listener(event);\n      }\n    }, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      shouldListen = !shouldIgnore(e) && !!(el && !e.composedPath().includes(el));\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      setTimeout(() => {\n        var _a;\n        const el = unrefElement(target);\n        if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement))) {\n          handler(event);\n        }\n      }, 0);\n    }, { passive: true })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  if (controls) {\n    return {\n      stop,\n      cancel: () => {\n        shouldListen = false;\n      },\n      trigger: (event) => {\n        shouldListen = true;\n        listener(event);\n        shouldListen = false;\n      }\n    };\n  }\n  return stop;\n}\n\nfunction useMounted() {\n  const isMounted = shallowRef(false);\n  const instance = getCurrentInstance();\n  if (instance) {\n    onMounted(() => {\n      isMounted.value = true;\n    }, instance);\n  }\n  return isMounted;\n}\n\nfunction useSupported(callback) {\n  const isMounted = useMounted();\n  return computed(() => {\n    isMounted.value;\n    return Boolean(callback());\n  });\n}\n\nfunction useMutationObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...mutationOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const value = toValue(target);\n    const items = toArray(value).map(unrefElement).filter(notNullish);\n    return new Set(items);\n  });\n  const stopWatch = watch(\n    () => targets.value,\n    (targets2) => {\n      cleanup();\n      if (isSupported.value && targets2.size) {\n        observer = new MutationObserver(callback);\n        targets2.forEach((el) => observer.observe(el, mutationOptions));\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const takeRecords = () => {\n    return observer == null ? void 0 : observer.takeRecords();\n  };\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop,\n    takeRecords\n  };\n}\n\nfunction onElementRemoval(target, callback, options = {}) {\n  const {\n    window = defaultWindow,\n    document = window == null ? void 0 : window.document,\n    flush = \"sync\"\n  } = options;\n  if (!window || !document)\n    return noop;\n  let stopFn;\n  const cleanupAndUpdate = (fn) => {\n    stopFn == null ? void 0 : stopFn();\n    stopFn = fn;\n  };\n  const stopWatch = watchEffect(() => {\n    const el = unrefElement(target);\n    if (el) {\n      const { stop } = useMutationObserver(\n        document,\n        (mutationsList) => {\n          const targetRemoved = mutationsList.map((mutation) => [...mutation.removedNodes]).flat().some((node) => node === el || node.contains(el));\n          if (targetRemoved) {\n            callback(mutationsList);\n          }\n        },\n        {\n          window,\n          childList: true,\n          subtree: true\n        }\n      );\n      cleanupAndUpdate(stop);\n    }\n  }, { flush });\n  const stopHandle = () => {\n    stopWatch();\n    cleanupAndUpdate();\n  };\n  tryOnScopeDispose(stopHandle);\n  return stopHandle;\n}\n\nfunction createKeyPredicate(keyFilter) {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n}\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const {\n    target = defaultWindow,\n    eventName = \"keydown\",\n    passive = false,\n    dedupe = false\n  } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (e.repeat && toValue(dedupe))\n      return;\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\nfunction onKeyDown(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keydown\" });\n}\nfunction onKeyPressed(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keypress\" });\n}\nfunction onKeyUp(key, handler, options = {}) {\n  return onKeyStroke(key, handler, { ...options, eventName: \"keyup\" });\n}\n\nconst DEFAULT_DELAY = 500;\nconst DEFAULT_THRESHOLD = 10;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  let posStart;\n  let startTimestamp;\n  let hasLongPressed = false;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n    posStart = void 0;\n    startTimestamp = void 0;\n    hasLongPressed = false;\n  }\n  function onRelease(ev) {\n    var _a2, _b2, _c;\n    const [_startTimestamp, _posStart, _hasLongPressed] = [startTimestamp, posStart, hasLongPressed];\n    clear();\n    if (!(options == null ? void 0 : options.onMouseUp) || !_posStart || !_startTimestamp)\n      return;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - _posStart.x;\n    const dy = ev.y - _posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    options.onMouseUp(ev.timeStamp - _startTimestamp, distance, _hasLongPressed);\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    posStart = {\n      x: ev.x,\n      y: ev.y\n    };\n    startTimestamp = ev.timeStamp;\n    timeout = setTimeout(\n      () => {\n        hasLongPressed = true;\n        handler(ev);\n      },\n      (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY\n    );\n  }\n  function onMove(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if (!posStart || (options == null ? void 0 : options.distanceThreshold) === false)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - posStart.x;\n    const dy = ev.y - posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    if (distance >= ((_d = options == null ? void 0 : options.distanceThreshold) != null ? _d : DEFAULT_THRESHOLD))\n      clear();\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  const cleanup = [\n    useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions),\n    useEventListener(elementRef, \"pointermove\", onMove, listenerOptions),\n    useEventListener(elementRef, [\"pointerup\", \"pointerleave\"], onRelease, listenerOptions)\n  ];\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nfunction isFocusedElementEditable() {\n  const { activeElement, body } = document;\n  if (!activeElement)\n    return false;\n  if (activeElement === body)\n    return false;\n  switch (activeElement.tagName) {\n    case \"INPUT\":\n    case \"TEXTAREA\":\n      return true;\n  }\n  return activeElement.hasAttribute(\"contenteditable\");\n}\nfunction isTypedCharValid({\n  keyCode,\n  metaKey,\n  ctrlKey,\n  altKey\n}) {\n  if (metaKey || ctrlKey || altKey)\n    return false;\n  if (keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105)\n    return true;\n  if (keyCode >= 65 && keyCode <= 90)\n    return true;\n  return false;\n}\nfunction onStartTyping(callback, options = {}) {\n  const { document: document2 = defaultDocument } = options;\n  const keydown = (event) => {\n    if (!isFocusedElementEditable() && isTypedCharValid(event)) {\n      callback(event);\n    }\n  };\n  if (document2)\n    useEventListener(document2, \"keydown\", keydown, { passive: true });\n}\n\nfunction templateRef(key, initialValue = null) {\n  const instance = getCurrentInstance();\n  let _trigger = () => {\n  };\n  const element = customRef((track, trigger) => {\n    _trigger = trigger;\n    return {\n      get() {\n        var _a, _b;\n        track();\n        return (_b = (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$refs[key]) != null ? _b : initialValue;\n      },\n      set() {\n      }\n    };\n  });\n  tryOnMounted(_trigger);\n  onUpdated(_trigger);\n  return element;\n}\n\nfunction useActiveElement(options = {}) {\n  var _a;\n  const {\n    window = defaultWindow,\n    deep = true,\n    triggerOnRemoval = false\n  } = options;\n  const document = (_a = options.document) != null ? _a : window == null ? void 0 : window.document;\n  const getDeepActiveElement = () => {\n    var _a2;\n    let element = document == null ? void 0 : document.activeElement;\n    if (deep) {\n      while (element == null ? void 0 : element.shadowRoot)\n        element = (_a2 = element == null ? void 0 : element.shadowRoot) == null ? void 0 : _a2.activeElement;\n    }\n    return element;\n  };\n  const activeElement = shallowRef();\n  const trigger = () => {\n    activeElement.value = getDeepActiveElement();\n  };\n  if (window) {\n    const listenerOptions = {\n      capture: true,\n      passive: true\n    };\n    useEventListener(\n      window,\n      \"blur\",\n      (event) => {\n        if (event.relatedTarget !== null)\n          return;\n        trigger();\n      },\n      listenerOptions\n    );\n    useEventListener(\n      window,\n      \"focus\",\n      trigger,\n      listenerOptions\n    );\n  }\n  if (triggerOnRemoval) {\n    onElementRemoval(activeElement, trigger, { document });\n  }\n  trigger();\n  return activeElement;\n}\n\nfunction useRafFn(fn, options = {}) {\n  const {\n    immediate = true,\n    fpsLimit = void 0,\n    window = defaultWindow,\n    once = false\n  } = options;\n  const isActive = shallowRef(false);\n  const intervalLimit = computed(() => {\n    return fpsLimit ? 1e3 / toValue(fpsLimit) : null;\n  });\n  let previousFrameTimestamp = 0;\n  let rafId = null;\n  function loop(timestamp) {\n    if (!isActive.value || !window)\n      return;\n    if (!previousFrameTimestamp)\n      previousFrameTimestamp = timestamp;\n    const delta = timestamp - previousFrameTimestamp;\n    if (intervalLimit.value && delta < intervalLimit.value) {\n      rafId = window.requestAnimationFrame(loop);\n      return;\n    }\n    previousFrameTimestamp = timestamp;\n    fn({ delta, timestamp });\n    if (once) {\n      isActive.value = false;\n      rafId = null;\n      return;\n    }\n    rafId = window.requestAnimationFrame(loop);\n  }\n  function resume() {\n    if (!isActive.value && window) {\n      isActive.value = true;\n      previousFrameTimestamp = 0;\n      rafId = window.requestAnimationFrame(loop);\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    if (rafId != null && window) {\n      window.cancelAnimationFrame(rafId);\n      rafId = null;\n    }\n  }\n  if (immediate)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive: readonly(isActive),\n    pause,\n    resume\n  };\n}\n\nfunction useAnimate(target, keyframes, options) {\n  let config;\n  let animateOptions;\n  if (isObject(options)) {\n    config = options;\n    animateOptions = objectOmit(options, [\"window\", \"immediate\", \"commitStyles\", \"persist\", \"onReady\", \"onError\"]);\n  } else {\n    config = { duration: options };\n    animateOptions = options;\n  }\n  const {\n    window = defaultWindow,\n    immediate = true,\n    commitStyles,\n    persist,\n    playbackRate: _playbackRate = 1,\n    onReady,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = config;\n  const isSupported = useSupported(() => window && HTMLElement && \"animate\" in HTMLElement.prototype);\n  const animate = shallowRef(void 0);\n  const store = shallowReactive({\n    startTime: null,\n    currentTime: null,\n    timeline: null,\n    playbackRate: _playbackRate,\n    pending: false,\n    playState: immediate ? \"idle\" : \"paused\",\n    replaceState: \"active\"\n  });\n  const pending = computed(() => store.pending);\n  const playState = computed(() => store.playState);\n  const replaceState = computed(() => store.replaceState);\n  const startTime = computed({\n    get() {\n      return store.startTime;\n    },\n    set(value) {\n      store.startTime = value;\n      if (animate.value)\n        animate.value.startTime = value;\n    }\n  });\n  const currentTime = computed({\n    get() {\n      return store.currentTime;\n    },\n    set(value) {\n      store.currentTime = value;\n      if (animate.value) {\n        animate.value.currentTime = value;\n        syncResume();\n      }\n    }\n  });\n  const timeline = computed({\n    get() {\n      return store.timeline;\n    },\n    set(value) {\n      store.timeline = value;\n      if (animate.value)\n        animate.value.timeline = value;\n    }\n  });\n  const playbackRate = computed({\n    get() {\n      return store.playbackRate;\n    },\n    set(value) {\n      store.playbackRate = value;\n      if (animate.value)\n        animate.value.playbackRate = value;\n    }\n  });\n  const play = () => {\n    if (animate.value) {\n      try {\n        animate.value.play();\n        syncResume();\n      } catch (e) {\n        syncPause();\n        onError(e);\n      }\n    } else {\n      update();\n    }\n  };\n  const pause = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.pause();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const reverse = () => {\n    var _a;\n    if (!animate.value)\n      update();\n    try {\n      (_a = animate.value) == null ? void 0 : _a.reverse();\n      syncResume();\n    } catch (e) {\n      syncPause();\n      onError(e);\n    }\n  };\n  const finish = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.finish();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  const cancel = () => {\n    var _a;\n    try {\n      (_a = animate.value) == null ? void 0 : _a.cancel();\n      syncPause();\n    } catch (e) {\n      onError(e);\n    }\n  };\n  watch(() => unrefElement(target), (el) => {\n    if (el) {\n      update();\n    } else {\n      animate.value = void 0;\n    }\n  });\n  watch(() => keyframes, (value) => {\n    if (animate.value) {\n      update();\n      const targetEl = unrefElement(target);\n      if (targetEl) {\n        animate.value.effect = new KeyframeEffect(\n          targetEl,\n          toValue(value),\n          animateOptions\n        );\n      }\n    }\n  }, { deep: true });\n  tryOnMounted(() => update(true), false);\n  tryOnScopeDispose(cancel);\n  function update(init) {\n    const el = unrefElement(target);\n    if (!isSupported.value || !el)\n      return;\n    if (!animate.value)\n      animate.value = el.animate(toValue(keyframes), animateOptions);\n    if (persist)\n      animate.value.persist();\n    if (_playbackRate !== 1)\n      animate.value.playbackRate = _playbackRate;\n    if (init && !immediate)\n      animate.value.pause();\n    else\n      syncResume();\n    onReady == null ? void 0 : onReady(animate.value);\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(animate, [\"cancel\", \"finish\", \"remove\"], syncPause, listenerOptions);\n  useEventListener(animate, \"finish\", () => {\n    var _a;\n    if (commitStyles)\n      (_a = animate.value) == null ? void 0 : _a.commitStyles();\n  }, listenerOptions);\n  const { resume: resumeRef, pause: pauseRef } = useRafFn(() => {\n    if (!animate.value)\n      return;\n    store.pending = animate.value.pending;\n    store.playState = animate.value.playState;\n    store.replaceState = animate.value.replaceState;\n    store.startTime = animate.value.startTime;\n    store.currentTime = animate.value.currentTime;\n    store.timeline = animate.value.timeline;\n    store.playbackRate = animate.value.playbackRate;\n  }, { immediate: false });\n  function syncResume() {\n    if (isSupported.value)\n      resumeRef();\n  }\n  function syncPause() {\n    if (isSupported.value && window)\n      window.requestAnimationFrame(pauseRef);\n  }\n  return {\n    isSupported,\n    animate,\n    // actions\n    play,\n    pause,\n    reverse,\n    finish,\n    cancel,\n    // state\n    pending,\n    playState,\n    replaceState,\n    startTime,\n    currentTime,\n    timeline,\n    playbackRate\n  };\n}\n\nfunction useAsyncQueue(tasks, options) {\n  const {\n    interrupt = true,\n    onError = noop,\n    onFinished = noop,\n    signal\n  } = options || {};\n  const promiseState = {\n    aborted: \"aborted\",\n    fulfilled: \"fulfilled\",\n    pending: \"pending\",\n    rejected: \"rejected\"\n  };\n  const initialResult = Array.from(Array.from({ length: tasks.length }), () => ({ state: promiseState.pending, data: null }));\n  const result = reactive(initialResult);\n  const activeIndex = shallowRef(-1);\n  if (!tasks || tasks.length === 0) {\n    onFinished();\n    return {\n      activeIndex,\n      result\n    };\n  }\n  function updateResult(state, res) {\n    activeIndex.value++;\n    result[activeIndex.value].data = res;\n    result[activeIndex.value].state = state;\n  }\n  tasks.reduce((prev, curr) => {\n    return prev.then((prevRes) => {\n      var _a;\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, new Error(\"aborted\"));\n        return;\n      }\n      if (((_a = result[activeIndex.value]) == null ? void 0 : _a.state) === promiseState.rejected && interrupt) {\n        onFinished();\n        return;\n      }\n      const done = curr(prevRes).then((currentRes) => {\n        updateResult(promiseState.fulfilled, currentRes);\n        if (activeIndex.value === tasks.length - 1)\n          onFinished();\n        return currentRes;\n      });\n      if (!signal)\n        return done;\n      return Promise.race([done, whenAborted(signal)]);\n    }).catch((e) => {\n      if (signal == null ? void 0 : signal.aborted) {\n        updateResult(promiseState.aborted, e);\n        return e;\n      }\n      updateResult(promiseState.rejected, e);\n      onError();\n      return e;\n    });\n  }, Promise.resolve());\n  return {\n    activeIndex,\n    result\n  };\n}\nfunction whenAborted(signal) {\n  return new Promise((resolve, reject) => {\n    const error = new Error(\"aborted\");\n    if (signal.aborted)\n      reject(error);\n    else\n      signal.addEventListener(\"abort\", () => reject(error), { once: true });\n  });\n}\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = shallowRef(false);\n  const isLoading = shallowRef(false);\n  const error = shallowRef(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw e;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate) {\n    execute(delay);\n  }\n  const shell = {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n  function waitUntilIsLoaded() {\n    return new Promise((resolve, reject) => {\n      until(isLoading).toBe(false).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilIsLoaded().then(onFulfilled, onRejected);\n    }\n  };\n}\n\nconst defaults = {\n  array: (v) => JSON.stringify(v),\n  object: (v) => JSON.stringify(v),\n  set: (v) => JSON.stringify(Array.from(v)),\n  map: (v) => JSON.stringify(Object.fromEntries(v)),\n  null: () => \"\"\n};\nfunction getDefaultSerialization(target) {\n  if (!target)\n    return defaults.null;\n  if (target instanceof Map)\n    return defaults.map;\n  else if (target instanceof Set)\n    return defaults.set;\n  else if (Array.isArray(target))\n    return defaults.array;\n  else\n    return defaults.object;\n}\n\nfunction useBase64(target, options) {\n  const base64 = shallowRef(\"\");\n  const promise = shallowRef();\n  function execute() {\n    if (!isClient)\n      return;\n    promise.value = new Promise((resolve, reject) => {\n      try {\n        const _target = toValue(target);\n        if (_target == null) {\n          resolve(\"\");\n        } else if (typeof _target === \"string\") {\n          resolve(blobToBase64(new Blob([_target], { type: \"text/plain\" })));\n        } else if (_target instanceof Blob) {\n          resolve(blobToBase64(_target));\n        } else if (_target instanceof ArrayBuffer) {\n          resolve(window.btoa(String.fromCharCode(...new Uint8Array(_target))));\n        } else if (_target instanceof HTMLCanvasElement) {\n          resolve(_target.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n        } else if (_target instanceof HTMLImageElement) {\n          const img = _target.cloneNode(false);\n          img.crossOrigin = \"Anonymous\";\n          imgLoaded(img).then(() => {\n            const canvas = document.createElement(\"canvas\");\n            const ctx = canvas.getContext(\"2d\");\n            canvas.width = img.width;\n            canvas.height = img.height;\n            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n            resolve(canvas.toDataURL(options == null ? void 0 : options.type, options == null ? void 0 : options.quality));\n          }).catch(reject);\n        } else if (typeof _target === \"object\") {\n          const _serializeFn = (options == null ? void 0 : options.serializer) || getDefaultSerialization(_target);\n          const serialized = _serializeFn(_target);\n          return resolve(blobToBase64(new Blob([serialized], { type: \"application/json\" })));\n        } else {\n          reject(new Error(\"target is unsupported types\"));\n        }\n      } catch (error) {\n        reject(error);\n      }\n    });\n    promise.value.then((res) => {\n      base64.value = (options == null ? void 0 : options.dataUrl) === false ? res.replace(/^data:.*?;base64,/, \"\") : res;\n    });\n    return promise.value;\n  }\n  if (isRef(target) || typeof target === \"function\")\n    watch(target, execute, { immediate: true });\n  else\n    execute();\n  return {\n    base64,\n    promise,\n    execute\n  };\n}\nfunction imgLoaded(img) {\n  return new Promise((resolve, reject) => {\n    if (!img.complete) {\n      img.onload = () => {\n        resolve();\n      };\n      img.onerror = reject;\n    } else {\n      resolve();\n    }\n  });\n}\nfunction blobToBase64(blob) {\n  return new Promise((resolve, reject) => {\n    const fr = new FileReader();\n    fr.onload = (e) => {\n      resolve(e.target.result);\n    };\n    fr.onerror = reject;\n    fr.readAsDataURL(blob);\n  });\n}\n\nfunction useBattery(options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const events = [\"chargingchange\", \"chargingtimechange\", \"dischargingtimechange\", \"levelchange\"];\n  const isSupported = useSupported(() => navigator && \"getBattery\" in navigator && typeof navigator.getBattery === \"function\");\n  const charging = shallowRef(false);\n  const chargingTime = shallowRef(0);\n  const dischargingTime = shallowRef(0);\n  const level = shallowRef(1);\n  let battery;\n  function updateBatteryInfo() {\n    charging.value = this.charging;\n    chargingTime.value = this.chargingTime || 0;\n    dischargingTime.value = this.dischargingTime || 0;\n    level.value = this.level;\n  }\n  if (isSupported.value) {\n    navigator.getBattery().then((_battery) => {\n      battery = _battery;\n      updateBatteryInfo.call(battery);\n      useEventListener(battery, events, updateBatteryInfo, { passive: true });\n    });\n  }\n  return {\n    isSupported,\n    charging,\n    chargingTime,\n    dischargingTime,\n    level\n  };\n}\n\nfunction useBluetooth(options) {\n  let {\n    acceptAllDevices = false\n  } = options || {};\n  const {\n    filters = void 0,\n    optionalServices = void 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => navigator && \"bluetooth\" in navigator);\n  const device = shallowRef();\n  const error = shallowRef(null);\n  watch(device, () => {\n    connectToBluetoothGATTServer();\n  });\n  async function requestDevice() {\n    if (!isSupported.value)\n      return;\n    error.value = null;\n    if (filters && filters.length > 0)\n      acceptAllDevices = false;\n    try {\n      device.value = await (navigator == null ? void 0 : navigator.bluetooth.requestDevice({\n        acceptAllDevices,\n        filters,\n        optionalServices\n      }));\n    } catch (err) {\n      error.value = err;\n    }\n  }\n  const server = shallowRef();\n  const isConnected = shallowRef(false);\n  function reset() {\n    isConnected.value = false;\n    device.value = void 0;\n    server.value = void 0;\n  }\n  async function connectToBluetoothGATTServer() {\n    error.value = null;\n    if (device.value && device.value.gatt) {\n      useEventListener(device, \"gattserverdisconnected\", reset, { passive: true });\n      try {\n        server.value = await device.value.gatt.connect();\n        isConnected.value = server.value.connected;\n      } catch (err) {\n        error.value = err;\n      }\n    }\n  }\n  tryOnMounted(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.connect();\n  });\n  tryOnScopeDispose(() => {\n    var _a;\n    if (device.value)\n      (_a = device.value.gatt) == null ? void 0 : _a.disconnect();\n  });\n  return {\n    isSupported,\n    isConnected: readonly(isConnected),\n    // Device:\n    device,\n    requestDevice,\n    // Server:\n    server,\n    // Errors:\n    error\n  };\n}\n\nconst ssrWidthSymbol = Symbol(\"vueuse-ssr-width\");\nfunction useSSRWidth() {\n  const ssrWidth = hasInjectionContext() ? injectLocal(ssrWidthSymbol, null) : null;\n  return typeof ssrWidth === \"number\" ? ssrWidth : void 0;\n}\nfunction provideSSRWidth(width, app) {\n  if (app !== void 0) {\n    app.provide(ssrWidthSymbol, width);\n  } else {\n    provideLocal(ssrWidthSymbol, width);\n  }\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow, ssrWidth = useSSRWidth() } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  const ssrSupport = shallowRef(typeof ssrWidth === \"number\");\n  const mediaQuery = shallowRef();\n  const matches = shallowRef(false);\n  const handler = (event) => {\n    matches.value = event.matches;\n  };\n  watchEffect(() => {\n    if (ssrSupport.value) {\n      ssrSupport.value = !isSupported.value;\n      const queryStrings = toValue(query).split(\",\");\n      matches.value = queryStrings.some((queryString) => {\n        const not = queryString.includes(\"not all\");\n        const minWidth = queryString.match(/\\(\\s*min-width:\\s*(-?\\d+(?:\\.\\d*)?[a-z]+\\s*)\\)/);\n        const maxWidth = queryString.match(/\\(\\s*max-width:\\s*(-?\\d+(?:\\.\\d*)?[a-z]+\\s*)\\)/);\n        let res = Boolean(minWidth || maxWidth);\n        if (minWidth && res) {\n          res = ssrWidth >= pxValue(minWidth[1]);\n        }\n        if (maxWidth && res) {\n          res = ssrWidth <= pxValue(maxWidth[1]);\n        }\n        return not ? !res : res;\n      });\n      return;\n    }\n    if (!isSupported.value)\n      return;\n    mediaQuery.value = window.matchMedia(toValue(query));\n    matches.value = mediaQuery.value.matches;\n  });\n  useEventListener(mediaQuery, \"change\", handler, { passive: true });\n  return computed(() => matches.value);\n}\n\nconst breakpointsTailwind = {\n  \"sm\": 640,\n  \"md\": 768,\n  \"lg\": 1024,\n  \"xl\": 1280,\n  \"2xl\": 1536\n};\nconst breakpointsBootstrapV5 = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n};\nconst breakpointsVuetifyV2 = {\n  xs: 0,\n  sm: 600,\n  md: 960,\n  lg: 1264,\n  xl: 1904\n};\nconst breakpointsVuetifyV3 = {\n  xs: 0,\n  sm: 600,\n  md: 960,\n  lg: 1280,\n  xl: 1920,\n  xxl: 2560\n};\nconst breakpointsVuetify = breakpointsVuetifyV2;\nconst breakpointsAntDesign = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600\n};\nconst breakpointsQuasar = {\n  xs: 0,\n  sm: 600,\n  md: 1024,\n  lg: 1440,\n  xl: 1920\n};\nconst breakpointsSematic = {\n  mobileS: 320,\n  mobileM: 375,\n  mobileL: 425,\n  tablet: 768,\n  laptop: 1024,\n  laptopL: 1440,\n  desktop4K: 2560\n};\nconst breakpointsMasterCss = {\n  \"3xs\": 360,\n  \"2xs\": 480,\n  \"xs\": 600,\n  \"sm\": 768,\n  \"md\": 1024,\n  \"lg\": 1280,\n  \"xl\": 1440,\n  \"2xl\": 1600,\n  \"3xl\": 1920,\n  \"4xl\": 2560\n};\nconst breakpointsPrimeFlex = {\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nconst breakpointsElement = {\n  xs: 0,\n  sm: 768,\n  md: 992,\n  lg: 1200,\n  xl: 1920\n};\n\nfunction useBreakpoints(breakpoints, options = {}) {\n  function getValue(k, delta) {\n    let v = toValue(breakpoints[toValue(k)]);\n    if (delta != null)\n      v = increaseWithUnit(v, delta);\n    if (typeof v === \"number\")\n      v = `${v}px`;\n    return v;\n  }\n  const { window = defaultWindow, strategy = \"min-width\", ssrWidth = useSSRWidth() } = options;\n  const ssrSupport = typeof ssrWidth === \"number\";\n  const mounted = ssrSupport ? shallowRef(false) : { value: true };\n  if (ssrSupport) {\n    tryOnMounted(() => mounted.value = !!window);\n  }\n  function match(query, size) {\n    if (!mounted.value && ssrSupport) {\n      return query === \"min\" ? ssrWidth >= pxValue(size) : ssrWidth <= pxValue(size);\n    }\n    if (!window)\n      return false;\n    return window.matchMedia(`(${query}-width: ${size})`).matches;\n  }\n  const greaterOrEqual = (k) => {\n    return useMediaQuery(() => `(min-width: ${getValue(k)})`, options);\n  };\n  const smallerOrEqual = (k) => {\n    return useMediaQuery(() => `(max-width: ${getValue(k)})`, options);\n  };\n  const shortcutMethods = Object.keys(breakpoints).reduce((shortcuts, k) => {\n    Object.defineProperty(shortcuts, k, {\n      get: () => strategy === \"min-width\" ? greaterOrEqual(k) : smallerOrEqual(k),\n      enumerable: true,\n      configurable: true\n    });\n    return shortcuts;\n  }, {});\n  function current() {\n    const points = Object.keys(breakpoints).map((k) => [k, shortcutMethods[k], pxValue(getValue(k))]).sort((a, b) => a[2] - b[2]);\n    return computed(() => points.filter(([, v]) => v.value).map(([k]) => k));\n  }\n  return Object.assign(shortcutMethods, {\n    greaterOrEqual,\n    smallerOrEqual,\n    greater(k) {\n      return useMediaQuery(() => `(min-width: ${getValue(k, 0.1)})`, options);\n    },\n    smaller(k) {\n      return useMediaQuery(() => `(max-width: ${getValue(k, -0.1)})`, options);\n    },\n    between(a, b) {\n      return useMediaQuery(() => `(min-width: ${getValue(a)}) and (max-width: ${getValue(b, -0.1)})`, options);\n    },\n    isGreater(k) {\n      return match(\"min\", getValue(k, 0.1));\n    },\n    isGreaterOrEqual(k) {\n      return match(\"min\", getValue(k));\n    },\n    isSmaller(k) {\n      return match(\"max\", getValue(k, -0.1));\n    },\n    isSmallerOrEqual(k) {\n      return match(\"max\", getValue(k));\n    },\n    isInBetween(a, b) {\n      return match(\"min\", getValue(a)) && match(\"max\", getValue(b, -0.1));\n    },\n    current,\n    active() {\n      const bps = current();\n      return computed(() => bps.value.length === 0 ? \"\" : bps.value.at(strategy === \"min-width\" ? -1 : 0));\n    }\n  });\n}\n\nfunction useBroadcastChannel(options) {\n  const {\n    name,\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"BroadcastChannel\" in window);\n  const isClosed = shallowRef(false);\n  const channel = ref();\n  const data = ref();\n  const error = shallowRef(null);\n  const post = (data2) => {\n    if (channel.value)\n      channel.value.postMessage(data2);\n  };\n  const close = () => {\n    if (channel.value)\n      channel.value.close();\n    isClosed.value = true;\n  };\n  if (isSupported.value) {\n    tryOnMounted(() => {\n      error.value = null;\n      channel.value = new BroadcastChannel(name);\n      const listenerOptions = {\n        passive: true\n      };\n      useEventListener(channel, \"message\", (e) => {\n        data.value = e.data;\n      }, listenerOptions);\n      useEventListener(channel, \"messageerror\", (e) => {\n        error.value = e;\n      }, listenerOptions);\n      useEventListener(channel, \"close\", () => {\n        isClosed.value = true;\n      }, listenerOptions);\n    });\n  }\n  tryOnScopeDispose(() => {\n    close();\n  });\n  return {\n    isSupported,\n    channel,\n    data,\n    post,\n    close,\n    error,\n    isClosed\n  };\n}\n\nconst WRITABLE_PROPERTIES = [\n  \"hash\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"search\"\n];\nfunction useBrowserLocation(options = {}) {\n  const { window = defaultWindow } = options;\n  const refs = Object.fromEntries(\n    WRITABLE_PROPERTIES.map((key) => [key, ref()])\n  );\n  for (const [key, ref] of objectEntries(refs)) {\n    watch(ref, (value) => {\n      if (!(window == null ? void 0 : window.location) || window.location[key] === value)\n        return;\n      window.location[key] = value;\n    });\n  }\n  const buildState = (trigger) => {\n    var _a;\n    const { state: state2, length } = (window == null ? void 0 : window.history) || {};\n    const { origin } = (window == null ? void 0 : window.location) || {};\n    for (const key of WRITABLE_PROPERTIES)\n      refs[key].value = (_a = window == null ? void 0 : window.location) == null ? void 0 : _a[key];\n    return reactive({\n      trigger,\n      state: state2,\n      length,\n      origin,\n      ...refs\n    });\n  };\n  const state = ref(buildState(\"load\"));\n  if (window) {\n    const listenerOptions = { passive: true };\n    useEventListener(window, \"popstate\", () => state.value = buildState(\"popstate\"), listenerOptions);\n    useEventListener(window, \"hashchange\", () => state.value = buildState(\"hashchange\"), listenerOptions);\n  }\n  return state;\n}\n\nfunction useCached(refValue, comparator = (a, b) => a === b, options) {\n  const { deepRefs = true, ...watchOptions } = options || {};\n  const cachedValue = createRef(refValue.value, deepRefs);\n  watch(() => refValue.value, (value) => {\n    if (!comparator(value, cachedValue.value))\n      cachedValue.value = value;\n  }, watchOptions);\n  return cachedValue;\n}\n\nfunction usePermission(permissionDesc, options = {}) {\n  const {\n    controls = false,\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"permissions\" in navigator);\n  const permissionStatus = shallowRef();\n  const desc = typeof permissionDesc === \"string\" ? { name: permissionDesc } : permissionDesc;\n  const state = shallowRef();\n  const update = () => {\n    var _a, _b;\n    state.value = (_b = (_a = permissionStatus.value) == null ? void 0 : _a.state) != null ? _b : \"prompt\";\n  };\n  useEventListener(permissionStatus, \"change\", update, { passive: true });\n  const query = createSingletonPromise(async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionStatus.value) {\n      try {\n        permissionStatus.value = await navigator.permissions.query(desc);\n      } catch (e) {\n        permissionStatus.value = void 0;\n      } finally {\n        update();\n      }\n    }\n    if (controls)\n      return toRaw(permissionStatus.value);\n  });\n  query();\n  if (controls) {\n    return {\n      state,\n      isSupported,\n      query\n    };\n  } else {\n    return state;\n  }\n}\n\nfunction useClipboard(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500,\n    legacy = false\n  } = options;\n  const isClipboardApiSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const permissionRead = usePermission(\"clipboard-read\");\n  const permissionWrite = usePermission(\"clipboard-write\");\n  const isSupported = computed(() => isClipboardApiSupported.value || legacy);\n  const text = shallowRef(\"\");\n  const copied = shallowRef(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring, { immediate: false });\n  async function updateText() {\n    let useLegacy = !(isClipboardApiSupported.value && isAllowed(permissionRead.value));\n    if (!useLegacy) {\n      try {\n        text.value = await navigator.clipboard.readText();\n      } catch (e) {\n        useLegacy = true;\n      }\n    }\n    if (useLegacy) {\n      text.value = legacyRead();\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateText, { passive: true });\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      let useLegacy = !(isClipboardApiSupported.value && isAllowed(permissionWrite.value));\n      if (!useLegacy) {\n        try {\n          await navigator.clipboard.writeText(value);\n        } catch (e) {\n          useLegacy = true;\n        }\n      }\n      if (useLegacy)\n        legacyCopy(value);\n      text.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  function legacyCopy(value) {\n    const ta = document.createElement(\"textarea\");\n    ta.value = value != null ? value : \"\";\n    ta.style.position = \"absolute\";\n    ta.style.opacity = \"0\";\n    document.body.appendChild(ta);\n    ta.select();\n    document.execCommand(\"copy\");\n    ta.remove();\n  }\n  function legacyRead() {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = document == null ? void 0 : document.getSelection) == null ? void 0 : _a.call(document)) == null ? void 0 : _b.toString()) != null ? _c : \"\";\n  }\n  function isAllowed(status) {\n    return status === \"granted\" || status === \"prompt\";\n  }\n  return {\n    isSupported,\n    text,\n    copied,\n    copy\n  };\n}\n\nfunction useClipboardItems(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    read = false,\n    source,\n    copiedDuring = 1500\n  } = options;\n  const isSupported = useSupported(() => navigator && \"clipboard\" in navigator);\n  const content = ref([]);\n  const copied = shallowRef(false);\n  const timeout = useTimeoutFn(() => copied.value = false, copiedDuring, { immediate: false });\n  function updateContent() {\n    if (isSupported.value) {\n      navigator.clipboard.read().then((items) => {\n        content.value = items;\n      });\n    }\n  }\n  if (isSupported.value && read)\n    useEventListener([\"copy\", \"cut\"], updateContent, { passive: true });\n  async function copy(value = toValue(source)) {\n    if (isSupported.value && value != null) {\n      await navigator.clipboard.write(value);\n      content.value = value;\n      copied.value = true;\n      timeout.start();\n    }\n  }\n  return {\n    isSupported,\n    content,\n    copied,\n    copy\n  };\n}\n\nfunction cloneFnJSON(source) {\n  return JSON.parse(JSON.stringify(source));\n}\nfunction useCloned(source, options = {}) {\n  const cloned = ref({});\n  const isModified = shallowRef(false);\n  let _lastSync = false;\n  const {\n    manual,\n    clone = cloneFnJSON,\n    // watch options\n    deep = true,\n    immediate = true\n  } = options;\n  watch(cloned, () => {\n    if (_lastSync) {\n      _lastSync = false;\n      return;\n    }\n    isModified.value = true;\n  }, {\n    deep: true,\n    flush: \"sync\"\n  });\n  function sync() {\n    _lastSync = true;\n    isModified.value = false;\n    cloned.value = clone(toValue(source));\n  }\n  if (!manual && (isRef(source) || typeof source === \"function\")) {\n    watch(source, sync, {\n      ...options,\n      deep,\n      immediate\n    });\n  } else {\n    sync();\n  }\n  return { cloned, isModified, sync };\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\nconst handlers = /* @__PURE__ */ getHandlers();\nfunction getHandlers() {\n  if (!(globalKey in _global))\n    _global[globalKey] = _global[globalKey] || {};\n  return _global[globalKey];\n}\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\nfunction setSSRHandler(key, fn) {\n  handlers[key] = fn;\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    },\n    initOnMounted\n  } = options;\n  const data = (shallow ? shallowRef : ref)(typeof defaults === \"function\" ? defaults() : defaults);\n  const keyComputed = computed(() => toValue(key));\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = toValue(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(\n    data,\n    () => write(data.value),\n    { flush, deep, eventFilter }\n  );\n  watch(keyComputed, () => update(), { flush });\n  if (window && listenToStorageChanges) {\n    tryOnMounted(() => {\n      if (storage instanceof Storage)\n        useEventListener(window, \"storage\", update, { passive: true });\n      else\n        useEventListener(window, customStorageEventName, updateFromCustomEvent);\n      if (initOnMounted)\n        update();\n    });\n  }\n  if (!initOnMounted)\n    update();\n  function dispatchWriteEvent(oldValue, newValue) {\n    if (window) {\n      const payload = {\n        key: keyComputed.value,\n        oldValue,\n        newValue,\n        storageArea: storage\n      };\n      window.dispatchEvent(storage instanceof Storage ? new StorageEvent(\"storage\", payload) : new CustomEvent(customStorageEventName, {\n        detail: payload\n      }));\n    }\n  }\n  function write(v) {\n    try {\n      const oldValue = storage.getItem(keyComputed.value);\n      if (v == null) {\n        dispatchWriteEvent(oldValue, null);\n        storage.removeItem(keyComputed.value);\n      } else {\n        const serialized = serializer.write(v);\n        if (oldValue !== serialized) {\n          storage.setItem(keyComputed.value, serialized);\n          dispatchWriteEvent(oldValue, serialized);\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(keyComputed.value);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit != null)\n        storage.setItem(keyComputed.value, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (typeof mergeDefaults === \"function\")\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return { ...rawInit, ...value };\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== keyComputed.value)\n      return;\n    pauseWatch();\n    try {\n      if ((event == null ? void 0 : event.newValue) !== serializer.write(data.value))\n        data.value = read(event);\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  return data;\n}\n\nconst CSS_DISABLE_TRANS = \"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\";\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto,\n    disableTransition = true\n  } = options;\n  const modes = {\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\",\n    ...options.modes || {}\n  };\n  const preferredDark = usePreferredDark({ window });\n  const system = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? toRef(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed(() => store.value === \"auto\" ? system.value : store.value);\n  const updateHTMLAttrs = getSSRHandler(\n    \"updateHTMLAttrs\",\n    (selector2, attribute2, value) => {\n      const el = typeof selector2 === \"string\" ? window == null ? void 0 : window.document.querySelector(selector2) : unrefElement(selector2);\n      if (!el)\n        return;\n      const classesToAdd = /* @__PURE__ */ new Set();\n      const classesToRemove = /* @__PURE__ */ new Set();\n      let attributeToChange = null;\n      if (attribute2 === \"class\") {\n        const current = value.split(/\\s/g);\n        Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n          if (current.includes(v))\n            classesToAdd.add(v);\n          else\n            classesToRemove.add(v);\n        });\n      } else {\n        attributeToChange = { key: attribute2, value };\n      }\n      if (classesToAdd.size === 0 && classesToRemove.size === 0 && attributeToChange === null)\n        return;\n      let style;\n      if (disableTransition) {\n        style = window.document.createElement(\"style\");\n        style.appendChild(document.createTextNode(CSS_DISABLE_TRANS));\n        window.document.head.appendChild(style);\n      }\n      for (const c of classesToAdd) {\n        el.classList.add(c);\n      }\n      for (const c of classesToRemove) {\n        el.classList.remove(c);\n      }\n      if (attributeToChange) {\n        el.setAttribute(attributeToChange.key, attributeToChange.value);\n      }\n      if (disableTransition) {\n        window.getComputedStyle(style).opacity;\n        document.head.removeChild(style);\n      }\n    }\n  );\n  function defaultOnChanged(mode) {\n    var _a;\n    updateHTMLAttrs(selector, attribute, (_a = modes[mode]) != null ? _a : mode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  tryOnMounted(() => onChanged(state.value));\n  const auto = computed({\n    get() {\n      return emitAuto ? store.value : state.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  return Object.assign(auto, { store, system, state });\n}\n\nfunction useConfirmDialog(revealed = shallowRef(false)) {\n  const confirmHook = createEventHook();\n  const cancelHook = createEventHook();\n  const revealHook = createEventHook();\n  let _resolve = noop;\n  const reveal = (data) => {\n    revealHook.trigger(data);\n    revealed.value = true;\n    return new Promise((resolve) => {\n      _resolve = resolve;\n    });\n  };\n  const confirm = (data) => {\n    revealed.value = false;\n    confirmHook.trigger(data);\n    _resolve({ data, isCanceled: false });\n  };\n  const cancel = (data) => {\n    revealed.value = false;\n    cancelHook.trigger(data);\n    _resolve({ data, isCanceled: true });\n  };\n  return {\n    isRevealed: computed(() => revealed.value),\n    reveal,\n    confirm,\n    cancel,\n    onReveal: revealHook.on,\n    onConfirm: confirmHook.on,\n    onCancel: cancelHook.on\n  };\n}\n\nfunction useCountdown(initialCountdown, options) {\n  var _a, _b;\n  const remaining = shallowRef(toValue(initialCountdown));\n  const intervalController = useIntervalFn(() => {\n    var _a2, _b2;\n    const value = remaining.value - 1;\n    remaining.value = value < 0 ? 0 : value;\n    (_a2 = options == null ? void 0 : options.onTick) == null ? void 0 : _a2.call(options);\n    if (remaining.value <= 0) {\n      intervalController.pause();\n      (_b2 = options == null ? void 0 : options.onComplete) == null ? void 0 : _b2.call(options);\n    }\n  }, (_a = options == null ? void 0 : options.interval) != null ? _a : 1e3, { immediate: (_b = options == null ? void 0 : options.immediate) != null ? _b : false });\n  const reset = (countdown) => {\n    var _a2;\n    remaining.value = (_a2 = toValue(countdown)) != null ? _a2 : toValue(initialCountdown);\n  };\n  const stop = () => {\n    intervalController.pause();\n    reset();\n  };\n  const resume = () => {\n    if (!intervalController.isActive.value) {\n      if (remaining.value > 0) {\n        intervalController.resume();\n      }\n    }\n  };\n  const start = (countdown) => {\n    reset(countdown);\n    intervalController.resume();\n  };\n  return {\n    remaining,\n    reset,\n    stop,\n    start,\n    pause: intervalController.pause,\n    resume,\n    isActive: intervalController.isActive\n  };\n}\n\nfunction useCssVar(prop, target, options = {}) {\n  const { window = defaultWindow, initialValue, observe = false } = options;\n  const variable = shallowRef(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  function updateCssVar() {\n    var _a;\n    const key = toValue(prop);\n    const el = toValue(elRef);\n    if (el && window && key) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(key)) == null ? void 0 : _a.trim();\n      variable.value = value || variable.value || initialValue;\n    }\n  }\n  if (observe) {\n    useMutationObserver(elRef, updateCssVar, {\n      attributeFilter: [\"style\", \"class\"],\n      window\n    });\n  }\n  watch(\n    [elRef, () => toValue(prop)],\n    (_, old) => {\n      if (old[0] && old[1])\n        old[0].style.removeProperty(old[1]);\n      updateCssVar();\n    },\n    { immediate: true }\n  );\n  watch(\n    [variable, elRef],\n    ([val, el]) => {\n      const raw_prop = toValue(prop);\n      if ((el == null ? void 0 : el.style) && raw_prop) {\n        if (val == null)\n          el.style.removeProperty(raw_prop);\n        else\n          el.style.setProperty(raw_prop, val);\n      }\n    },\n    { immediate: true }\n  );\n  return variable;\n}\n\nfunction useCurrentElement(rootComponent) {\n  const vm = getCurrentInstance();\n  const currentElement = computedWithControl(\n    () => null,\n    () => rootComponent ? unrefElement(rootComponent) : vm.proxy.$el\n  );\n  onUpdated(currentElement.trigger);\n  onMounted(currentElement.trigger);\n  return currentElement;\n}\n\nfunction useCycleList(list, options) {\n  const state = shallowRef(getInitialValue());\n  const listRef = toRef(list);\n  const index = computed({\n    get() {\n      var _a;\n      const targetList = listRef.value;\n      let index2 = (options == null ? void 0 : options.getIndexOf) ? options.getIndexOf(state.value, targetList) : targetList.indexOf(state.value);\n      if (index2 < 0)\n        index2 = (_a = options == null ? void 0 : options.fallbackIndex) != null ? _a : 0;\n      return index2;\n    },\n    set(v) {\n      set(v);\n    }\n  });\n  function set(i) {\n    const targetList = listRef.value;\n    const length = targetList.length;\n    const index2 = (i % length + length) % length;\n    const value = targetList[index2];\n    state.value = value;\n    return value;\n  }\n  function shift(delta = 1) {\n    return set(index.value + delta);\n  }\n  function next(n = 1) {\n    return shift(n);\n  }\n  function prev(n = 1) {\n    return shift(-n);\n  }\n  function getInitialValue() {\n    var _a, _b;\n    return (_b = toValue((_a = options == null ? void 0 : options.initialValue) != null ? _a : toValue(list)[0])) != null ? _b : void 0;\n  }\n  watch(listRef, () => set(index.value));\n  return {\n    state,\n    index,\n    next,\n    prev,\n    go: set\n  };\n}\n\nfunction useDark(options = {}) {\n  const {\n    valueDark = \"dark\",\n    valueLight = \"\"\n  } = options;\n  const mode = useColorMode({\n    ...options,\n    onChanged: (mode2, defaultHandler) => {\n      var _a;\n      if (options.onChanged)\n        (_a = options.onChanged) == null ? void 0 : _a.call(options, mode2 === \"dark\", defaultHandler, mode2);\n      else\n        defaultHandler(mode2);\n    },\n    modes: {\n      dark: valueDark,\n      light: valueLight\n    }\n  });\n  const system = computed(() => mode.system.value);\n  const isDark = computed({\n    get() {\n      return mode.value === \"dark\";\n    },\n    set(v) {\n      const modeVal = v ? \"dark\" : \"light\";\n      if (system.value === modeVal)\n        mode.value = \"auto\";\n      else\n        mode.value = modeVal;\n    }\n  });\n  return isDark;\n}\n\nfunction fnBypass(v) {\n  return v;\n}\nfunction fnSetSource(source, value) {\n  return source.value = value;\n}\nfunction defaultDump(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction defaultParse(clone) {\n  return clone ? typeof clone === \"function\" ? clone : cloneFnJSON : fnBypass;\n}\nfunction useManualRefHistory(source, options = {}) {\n  const {\n    clone = false,\n    dump = defaultDump(clone),\n    parse = defaultParse(clone),\n    setSource = fnSetSource\n  } = options;\n  function _createHistoryRecord() {\n    return markRaw({\n      snapshot: dump(source.value),\n      timestamp: timestamp()\n    });\n  }\n  const last = ref(_createHistoryRecord());\n  const undoStack = ref([]);\n  const redoStack = ref([]);\n  const _setSource = (record) => {\n    setSource(source, parse(record.snapshot));\n    last.value = record;\n  };\n  const commit = () => {\n    undoStack.value.unshift(last.value);\n    last.value = _createHistoryRecord();\n    if (options.capacity && undoStack.value.length > options.capacity)\n      undoStack.value.splice(options.capacity, Number.POSITIVE_INFINITY);\n    if (redoStack.value.length)\n      redoStack.value.splice(0, redoStack.value.length);\n  };\n  const clear = () => {\n    undoStack.value.splice(0, undoStack.value.length);\n    redoStack.value.splice(0, redoStack.value.length);\n  };\n  const undo = () => {\n    const state = undoStack.value.shift();\n    if (state) {\n      redoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const redo = () => {\n    const state = redoStack.value.shift();\n    if (state) {\n      undoStack.value.unshift(last.value);\n      _setSource(state);\n    }\n  };\n  const reset = () => {\n    _setSource(last.value);\n  };\n  const history = computed(() => [last.value, ...undoStack.value]);\n  const canUndo = computed(() => undoStack.value.length > 0);\n  const canRedo = computed(() => redoStack.value.length > 0);\n  return {\n    source,\n    undoStack,\n    redoStack,\n    last,\n    history,\n    canUndo,\n    canRedo,\n    clear,\n    commit,\n    reset,\n    undo,\n    redo\n  };\n}\n\nfunction useRefHistory(source, options = {}) {\n  const {\n    deep = false,\n    flush = \"pre\",\n    eventFilter\n  } = options;\n  const {\n    eventFilter: composedFilter,\n    pause,\n    resume: resumeTracking,\n    isActive: isTracking\n  } = pausableFilter(eventFilter);\n  const {\n    ignoreUpdates,\n    ignorePrevAsyncUpdates,\n    stop\n  } = watchIgnorable(\n    source,\n    commit,\n    { deep, flush, eventFilter: composedFilter }\n  );\n  function setSource(source2, value) {\n    ignorePrevAsyncUpdates();\n    ignoreUpdates(() => {\n      source2.value = value;\n    });\n  }\n  const manualHistory = useManualRefHistory(source, { ...options, clone: options.clone || deep, setSource });\n  const { clear, commit: manualCommit } = manualHistory;\n  function commit() {\n    ignorePrevAsyncUpdates();\n    manualCommit();\n  }\n  function resume(commitNow) {\n    resumeTracking();\n    if (commitNow)\n      commit();\n  }\n  function batch(fn) {\n    let canceled = false;\n    const cancel = () => canceled = true;\n    ignoreUpdates(() => {\n      fn(cancel);\n    });\n    if (!canceled)\n      commit();\n  }\n  function dispose() {\n    stop();\n    clear();\n  }\n  return {\n    ...manualHistory,\n    isTracking,\n    pause,\n    resume,\n    commit,\n    batch,\n    dispose\n  };\n}\n\nfunction useDebouncedRefHistory(source, options = {}) {\n  const filter = options.debounce ? debounceFilter(options.debounce) : void 0;\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nfunction useDeviceMotion(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions = false,\n    eventFilter = bypassFilter\n  } = options;\n  const isSupported = useSupported(() => typeof DeviceMotionEvent !== \"undefined\");\n  const requirePermissions = useSupported(() => isSupported.value && \"requestPermission\" in DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === \"function\");\n  const permissionGranted = shallowRef(false);\n  const acceleration = ref({ x: null, y: null, z: null });\n  const rotationRate = ref({ alpha: null, beta: null, gamma: null });\n  const interval = shallowRef(0);\n  const accelerationIncludingGravity = ref({\n    x: null,\n    y: null,\n    z: null\n  });\n  function init() {\n    if (window) {\n      const onDeviceMotion = createFilterWrapper(\n        eventFilter,\n        (event) => {\n          var _a, _b, _c, _d, _e, _f, _g, _h, _i;\n          acceleration.value = {\n            x: ((_a = event.acceleration) == null ? void 0 : _a.x) || null,\n            y: ((_b = event.acceleration) == null ? void 0 : _b.y) || null,\n            z: ((_c = event.acceleration) == null ? void 0 : _c.z) || null\n          };\n          accelerationIncludingGravity.value = {\n            x: ((_d = event.accelerationIncludingGravity) == null ? void 0 : _d.x) || null,\n            y: ((_e = event.accelerationIncludingGravity) == null ? void 0 : _e.y) || null,\n            z: ((_f = event.accelerationIncludingGravity) == null ? void 0 : _f.z) || null\n          };\n          rotationRate.value = {\n            alpha: ((_g = event.rotationRate) == null ? void 0 : _g.alpha) || null,\n            beta: ((_h = event.rotationRate) == null ? void 0 : _h.beta) || null,\n            gamma: ((_i = event.rotationRate) == null ? void 0 : _i.gamma) || null\n          };\n          interval.value = event.interval;\n        }\n      );\n      useEventListener(window, \"devicemotion\", onDeviceMotion, { passive: true });\n    }\n  }\n  const ensurePermissions = async () => {\n    if (!requirePermissions.value)\n      permissionGranted.value = true;\n    if (permissionGranted.value)\n      return;\n    if (requirePermissions.value) {\n      const requestPermission = DeviceMotionEvent.requestPermission;\n      try {\n        const response = await requestPermission();\n        if (response === \"granted\") {\n          permissionGranted.value = true;\n          init();\n        }\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  if (isSupported.value) {\n    if (requestPermissions && requirePermissions.value) {\n      ensurePermissions().then(() => init());\n    } else {\n      init();\n    }\n  }\n  return {\n    acceleration,\n    accelerationIncludingGravity,\n    rotationRate,\n    interval,\n    isSupported,\n    requirePermissions,\n    ensurePermissions,\n    permissionGranted\n  };\n}\n\nfunction useDeviceOrientation(options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"DeviceOrientationEvent\" in window);\n  const isAbsolute = shallowRef(false);\n  const alpha = shallowRef(null);\n  const beta = shallowRef(null);\n  const gamma = shallowRef(null);\n  if (window && isSupported.value) {\n    useEventListener(window, \"deviceorientation\", (event) => {\n      isAbsolute.value = event.absolute;\n      alpha.value = event.alpha;\n      beta.value = event.beta;\n      gamma.value = event.gamma;\n    }, { passive: true });\n  }\n  return {\n    isSupported,\n    isAbsolute,\n    alpha,\n    beta,\n    gamma\n  };\n}\n\nfunction useDevicePixelRatio(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const pixelRatio = shallowRef(1);\n  const query = useMediaQuery(() => `(resolution: ${pixelRatio.value}dppx)`, options);\n  let stop = noop;\n  if (window) {\n    stop = watchImmediate(query, () => pixelRatio.value = window.devicePixelRatio);\n  }\n  return {\n    pixelRatio: readonly(pixelRatio),\n    stop\n  };\n}\n\nfunction useDevicesList(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    requestPermissions = false,\n    constraints = { audio: true, video: true },\n    onUpdated\n  } = options;\n  const devices = ref([]);\n  const videoInputs = computed(() => devices.value.filter((i) => i.kind === \"videoinput\"));\n  const audioInputs = computed(() => devices.value.filter((i) => i.kind === \"audioinput\"));\n  const audioOutputs = computed(() => devices.value.filter((i) => i.kind === \"audiooutput\"));\n  const isSupported = useSupported(() => navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n  const permissionGranted = shallowRef(false);\n  let stream;\n  async function update() {\n    if (!isSupported.value)\n      return;\n    devices.value = await navigator.mediaDevices.enumerateDevices();\n    onUpdated == null ? void 0 : onUpdated(devices.value);\n    if (stream) {\n      stream.getTracks().forEach((t) => t.stop());\n      stream = null;\n    }\n  }\n  async function ensurePermissions() {\n    const deviceName = constraints.video ? \"camera\" : \"microphone\";\n    if (!isSupported.value)\n      return false;\n    if (permissionGranted.value)\n      return true;\n    const { state, query } = usePermission(deviceName, { controls: true });\n    await query();\n    if (state.value !== \"granted\") {\n      let granted = true;\n      try {\n        stream = await navigator.mediaDevices.getUserMedia(constraints);\n      } catch (e) {\n        stream = null;\n        granted = false;\n      }\n      update();\n      permissionGranted.value = granted;\n    } else {\n      permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  }\n  if (isSupported.value) {\n    if (requestPermissions)\n      ensurePermissions();\n    useEventListener(navigator.mediaDevices, \"devicechange\", update, { passive: true });\n    update();\n  }\n  return {\n    devices,\n    ensurePermissions,\n    permissionGranted,\n    videoInputs,\n    audioInputs,\n    audioOutputs,\n    isSupported\n  };\n}\n\nfunction useDisplayMedia(options = {}) {\n  var _a;\n  const enabled = shallowRef((_a = options.enabled) != null ? _a : false);\n  const video = options.video;\n  const audio = options.audio;\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getDisplayMedia;\n  });\n  const constraint = { audio, video };\n  const stream = shallowRef();\n  async function _start() {\n    var _a2;\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getDisplayMedia(constraint);\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => useEventListener(t, \"ended\", stop, { passive: true }));\n    return stream.value;\n  }\n  async function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else\n        _stop();\n    },\n    { immediate: true }\n  );\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    enabled\n  };\n}\n\nfunction useDocumentVisibility(options = {}) {\n  const { document = defaultDocument } = options;\n  if (!document)\n    return shallowRef(\"visible\");\n  const visibility = shallowRef(document.visibilityState);\n  useEventListener(document, \"visibilitychange\", () => {\n    visibility.value = document.visibilityState;\n  }, { passive: true });\n  return visibility;\n}\n\nfunction useDraggable(target, options = {}) {\n  var _a;\n  const {\n    pointerTypes,\n    preventDefault,\n    stopPropagation,\n    exact,\n    onMove,\n    onEnd,\n    onStart,\n    initialValue,\n    axis = \"both\",\n    draggingElement = defaultWindow,\n    containerElement,\n    handle: draggingHandle = target,\n    buttons = [0]\n  } = options;\n  const position = ref(\n    (_a = toValue(initialValue)) != null ? _a : { x: 0, y: 0 }\n  );\n  const pressedDelta = ref();\n  const filterEvent = (e) => {\n    if (pointerTypes)\n      return pointerTypes.includes(e.pointerType);\n    return true;\n  };\n  const handleEvent = (e) => {\n    if (toValue(preventDefault))\n      e.preventDefault();\n    if (toValue(stopPropagation))\n      e.stopPropagation();\n  };\n  const start = (e) => {\n    var _a2;\n    if (!toValue(buttons).includes(e.button))\n      return;\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (toValue(exact) && e.target !== toValue(target))\n      return;\n    const container = toValue(containerElement);\n    const containerRect = (_a2 = container == null ? void 0 : container.getBoundingClientRect) == null ? void 0 : _a2.call(container);\n    const targetRect = toValue(target).getBoundingClientRect();\n    const pos = {\n      x: e.clientX - (container ? targetRect.left - containerRect.left + container.scrollLeft : targetRect.left),\n      y: e.clientY - (container ? targetRect.top - containerRect.top + container.scrollTop : targetRect.top)\n    };\n    if ((onStart == null ? void 0 : onStart(pos, e)) === false)\n      return;\n    pressedDelta.value = pos;\n    handleEvent(e);\n  };\n  const move = (e) => {\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    const container = toValue(containerElement);\n    const targetRect = toValue(target).getBoundingClientRect();\n    let { x, y } = position.value;\n    if (axis === \"x\" || axis === \"both\") {\n      x = e.clientX - pressedDelta.value.x;\n      if (container)\n        x = Math.min(Math.max(0, x), container.scrollWidth - targetRect.width);\n    }\n    if (axis === \"y\" || axis === \"both\") {\n      y = e.clientY - pressedDelta.value.y;\n      if (container)\n        y = Math.min(Math.max(0, y), container.scrollHeight - targetRect.height);\n    }\n    position.value = {\n      x,\n      y\n    };\n    onMove == null ? void 0 : onMove(position.value, e);\n    handleEvent(e);\n  };\n  const end = (e) => {\n    if (toValue(options.disabled) || !filterEvent(e))\n      return;\n    if (!pressedDelta.value)\n      return;\n    pressedDelta.value = void 0;\n    onEnd == null ? void 0 : onEnd(position.value, e);\n    handleEvent(e);\n  };\n  if (isClient) {\n    const config = () => {\n      var _a2;\n      return {\n        capture: (_a2 = options.capture) != null ? _a2 : true,\n        passive: !toValue(preventDefault)\n      };\n    };\n    useEventListener(draggingHandle, \"pointerdown\", start, config);\n    useEventListener(draggingElement, \"pointermove\", move, config);\n    useEventListener(draggingElement, \"pointerup\", end, config);\n  }\n  return {\n    ...toRefs(position),\n    position,\n    isDragging: computed(() => !!pressedDelta.value),\n    style: computed(\n      () => `left:${position.value.x}px;top:${position.value.y}px;`\n    )\n  };\n}\n\nfunction useDropZone(target, options = {}) {\n  var _a, _b;\n  const isOverDropZone = shallowRef(false);\n  const files = shallowRef(null);\n  let counter = 0;\n  let isValid = true;\n  if (isClient) {\n    const _options = typeof options === \"function\" ? { onDrop: options } : options;\n    const multiple = (_a = _options.multiple) != null ? _a : true;\n    const preventDefaultForUnhandled = (_b = _options.preventDefaultForUnhandled) != null ? _b : false;\n    const getFiles = (event) => {\n      var _a2, _b2;\n      const list = Array.from((_b2 = (_a2 = event.dataTransfer) == null ? void 0 : _a2.files) != null ? _b2 : []);\n      return list.length === 0 ? null : multiple ? list : [list[0]];\n    };\n    const checkDataTypes = (types) => {\n      const dataTypes = unref(_options.dataTypes);\n      if (typeof dataTypes === \"function\")\n        return dataTypes(types);\n      if (!(dataTypes == null ? void 0 : dataTypes.length))\n        return true;\n      if (types.length === 0)\n        return false;\n      return types.every(\n        (type) => dataTypes.some((allowedType) => type.includes(allowedType))\n      );\n    };\n    const checkValidity = (items) => {\n      const types = Array.from(items != null ? items : []).map((item) => item.type);\n      const dataTypesValid = checkDataTypes(types);\n      const multipleFilesValid = multiple || items.length <= 1;\n      return dataTypesValid && multipleFilesValid;\n    };\n    const isSafari = () => /^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent) && !(\"chrome\" in window);\n    const handleDragEvent = (event, eventType) => {\n      var _a2, _b2, _c, _d, _e, _f;\n      const dataTransferItemList = (_a2 = event.dataTransfer) == null ? void 0 : _a2.items;\n      isValid = (_b2 = dataTransferItemList && checkValidity(dataTransferItemList)) != null ? _b2 : false;\n      if (preventDefaultForUnhandled) {\n        event.preventDefault();\n      }\n      if (!isSafari() && !isValid) {\n        if (event.dataTransfer) {\n          event.dataTransfer.dropEffect = \"none\";\n        }\n        return;\n      }\n      event.preventDefault();\n      if (event.dataTransfer) {\n        event.dataTransfer.dropEffect = \"copy\";\n      }\n      const currentFiles = getFiles(event);\n      switch (eventType) {\n        case \"enter\":\n          counter += 1;\n          isOverDropZone.value = true;\n          (_c = _options.onEnter) == null ? void 0 : _c.call(_options, null, event);\n          break;\n        case \"over\":\n          (_d = _options.onOver) == null ? void 0 : _d.call(_options, null, event);\n          break;\n        case \"leave\":\n          counter -= 1;\n          if (counter === 0)\n            isOverDropZone.value = false;\n          (_e = _options.onLeave) == null ? void 0 : _e.call(_options, null, event);\n          break;\n        case \"drop\":\n          counter = 0;\n          isOverDropZone.value = false;\n          if (isValid) {\n            files.value = currentFiles;\n            (_f = _options.onDrop) == null ? void 0 : _f.call(_options, currentFiles, event);\n          }\n          break;\n      }\n    };\n    useEventListener(target, \"dragenter\", (event) => handleDragEvent(event, \"enter\"));\n    useEventListener(target, \"dragover\", (event) => handleDragEvent(event, \"over\"));\n    useEventListener(target, \"dragleave\", (event) => handleDragEvent(event, \"leave\"));\n    useEventListener(target, \"drop\", (event) => handleDragEvent(event, \"drop\"));\n  }\n  return {\n    files,\n    isOverDropZone\n  };\n}\n\nfunction useResizeObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...observerOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const _targets = toValue(target);\n    return Array.isArray(_targets) ? _targets.map((el) => unrefElement(el)) : [unrefElement(_targets)];\n  });\n  const stopWatch = watch(\n    targets,\n    (els) => {\n      cleanup();\n      if (isSupported.value && window) {\n        observer = new ResizeObserver(callback);\n        for (const _el of els) {\n          if (_el)\n            observer.observe(_el, observerOptions);\n        }\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementBounding(target, options = {}) {\n  const {\n    reset = true,\n    windowResize = true,\n    windowScroll = true,\n    immediate = true,\n    updateTiming = \"sync\"\n  } = options;\n  const height = shallowRef(0);\n  const bottom = shallowRef(0);\n  const left = shallowRef(0);\n  const right = shallowRef(0);\n  const top = shallowRef(0);\n  const width = shallowRef(0);\n  const x = shallowRef(0);\n  const y = shallowRef(0);\n  function recalculate() {\n    const el = unrefElement(target);\n    if (!el) {\n      if (reset) {\n        height.value = 0;\n        bottom.value = 0;\n        left.value = 0;\n        right.value = 0;\n        top.value = 0;\n        width.value = 0;\n        x.value = 0;\n        y.value = 0;\n      }\n      return;\n    }\n    const rect = el.getBoundingClientRect();\n    height.value = rect.height;\n    bottom.value = rect.bottom;\n    left.value = rect.left;\n    right.value = rect.right;\n    top.value = rect.top;\n    width.value = rect.width;\n    x.value = rect.x;\n    y.value = rect.y;\n  }\n  function update() {\n    if (updateTiming === \"sync\")\n      recalculate();\n    else if (updateTiming === \"next-frame\")\n      requestAnimationFrame(() => recalculate());\n  }\n  useResizeObserver(target, update);\n  watch(() => unrefElement(target), (ele) => !ele && update());\n  useMutationObserver(target, update, {\n    attributeFilter: [\"style\", \"class\"]\n  });\n  if (windowScroll)\n    useEventListener(\"scroll\", update, { capture: true, passive: true });\n  if (windowResize)\n    useEventListener(\"resize\", update, { passive: true });\n  tryOnMounted(() => {\n    if (immediate)\n      update();\n  });\n  return {\n    height,\n    bottom,\n    left,\n    right,\n    top,\n    width,\n    x,\n    y,\n    update\n  };\n}\n\nfunction useElementByPoint(options) {\n  const {\n    x,\n    y,\n    document = defaultDocument,\n    multiple,\n    interval = \"requestAnimationFrame\",\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => {\n    if (toValue(multiple))\n      return document && \"elementsFromPoint\" in document;\n    return document && \"elementFromPoint\" in document;\n  });\n  const element = shallowRef(null);\n  const cb = () => {\n    var _a, _b;\n    element.value = toValue(multiple) ? (_a = document == null ? void 0 : document.elementsFromPoint(toValue(x), toValue(y))) != null ? _a : [] : (_b = document == null ? void 0 : document.elementFromPoint(toValue(x), toValue(y))) != null ? _b : null;\n  };\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  return {\n    isSupported,\n    element,\n    ...controls\n  };\n}\n\nfunction useElementHover(el, options = {}) {\n  const {\n    delayEnter = 0,\n    delayLeave = 0,\n    triggerOnRemoval = false,\n    window = defaultWindow\n  } = options;\n  const isHovered = shallowRef(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  if (triggerOnRemoval) {\n    onElementRemoval(\n      computed(() => unrefElement(el)),\n      () => toggle(false)\n    );\n  }\n  return isHovered;\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = shallowRef(initialSize.width);\n  const height = shallowRef(initialSize.height);\n  const { stop: stop1 } = useResizeObserver(\n    target,\n    ([entry]) => {\n      const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n      if (window && isSVG.value) {\n        const $elem = unrefElement(target);\n        if ($elem) {\n          const rect = $elem.getBoundingClientRect();\n          width.value = rect.width;\n          height.value = rect.height;\n        }\n      } else {\n        if (boxSize) {\n          const formatBoxSize = toArray(boxSize);\n          width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n          height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n        } else {\n          width.value = entry.contentRect.width;\n          height.value = entry.contentRect.height;\n        }\n      }\n    },\n    options\n  );\n  tryOnMounted(() => {\n    const ele = unrefElement(target);\n    if (ele) {\n      width.value = \"offsetWidth\" in ele ? ele.offsetWidth : initialSize.width;\n      height.value = \"offsetHeight\" in ele ? ele.offsetHeight : initialSize.height;\n    }\n  });\n  const stop2 = watch(\n    () => unrefElement(target),\n    (ele) => {\n      width.value = ele ? initialSize.width : 0;\n      height.value = ele ? initialSize.height : 0;\n    }\n  );\n  function stop() {\n    stop1();\n    stop2();\n  }\n  return {\n    width,\n    height,\n    stop\n  };\n}\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0,\n    window = defaultWindow,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  const targets = computed(() => {\n    const _target = toValue(target);\n    return toArray(_target).map(unrefElement).filter(notNullish);\n  });\n  let cleanup = noop;\n  const isActive = shallowRef(immediate);\n  const stopWatch = isSupported.value ? watch(\n    () => [targets.value, unrefElement(root), isActive.value],\n    ([targets2, root2]) => {\n      cleanup();\n      if (!isActive.value)\n        return;\n      if (!targets2.length)\n        return;\n      const observer = new IntersectionObserver(\n        callback,\n        {\n          root: unrefElement(root2),\n          rootMargin,\n          threshold\n        }\n      );\n      targets2.forEach((el) => el && observer.observe(el));\n      cleanup = () => {\n        observer.disconnect();\n        cleanup = noop;\n      };\n    },\n    { immediate, flush: \"post\" }\n  ) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n    isActive.value = false;\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    isActive,\n    pause() {\n      cleanup();\n      isActive.value = false;\n    },\n    resume() {\n      isActive.value = true;\n    },\n    stop\n  };\n}\n\nfunction useElementVisibility(element, options = {}) {\n  const {\n    window = defaultWindow,\n    scrollTarget,\n    threshold = 0,\n    rootMargin,\n    once = false\n  } = options;\n  const elementIsVisible = shallowRef(false);\n  const { stop } = useIntersectionObserver(\n    element,\n    (intersectionObserverEntries) => {\n      let isIntersecting = elementIsVisible.value;\n      let latestTime = 0;\n      for (const entry of intersectionObserverEntries) {\n        if (entry.time >= latestTime) {\n          latestTime = entry.time;\n          isIntersecting = entry.isIntersecting;\n        }\n      }\n      elementIsVisible.value = isIntersecting;\n      if (once) {\n        watchOnce(elementIsVisible, () => {\n          stop();\n        });\n      }\n    },\n    {\n      root: scrollTarget,\n      window,\n      threshold,\n      rootMargin: toValue(rootMargin)\n    }\n  );\n  return elementIsVisible;\n}\n\nconst events = /* @__PURE__ */ new Map();\n\nfunction useEventBus(key) {\n  const scope = getCurrentScope();\n  function on(listener) {\n    var _a;\n    const listeners = events.get(key) || /* @__PURE__ */ new Set();\n    listeners.add(listener);\n    events.set(key, listeners);\n    const _off = () => off(listener);\n    (_a = scope == null ? void 0 : scope.cleanups) == null ? void 0 : _a.push(_off);\n    return _off;\n  }\n  function once(listener) {\n    function _listener(...args) {\n      off(_listener);\n      listener(...args);\n    }\n    return on(_listener);\n  }\n  function off(listener) {\n    const listeners = events.get(key);\n    if (!listeners)\n      return;\n    listeners.delete(listener);\n    if (!listeners.size)\n      reset();\n  }\n  function reset() {\n    events.delete(key);\n  }\n  function emit(event, payload) {\n    var _a;\n    (_a = events.get(key)) == null ? void 0 : _a.forEach((v) => v(event, payload));\n  }\n  return { on, once, off, emit, reset };\n}\n\nfunction resolveNestedOptions$1(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useEventSource(url, events = [], options = {}) {\n  const event = shallowRef(null);\n  const data = shallowRef(null);\n  const status = shallowRef(\"CONNECTING\");\n  const eventSource = ref(null);\n  const error = shallowRef(null);\n  const urlRef = toRef(url);\n  const lastEventId = shallowRef(null);\n  let explicitlyClosed = false;\n  let retried = 0;\n  const {\n    withCredentials = false,\n    immediate = true,\n    autoConnect = true,\n    autoReconnect\n  } = options;\n  const close = () => {\n    if (isClient && eventSource.value) {\n      eventSource.value.close();\n      eventSource.value = null;\n      status.value = \"CLOSED\";\n      explicitlyClosed = true;\n    }\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const es = new EventSource(urlRef.value, { withCredentials });\n    status.value = \"CONNECTING\";\n    eventSource.value = es;\n    es.onopen = () => {\n      status.value = \"OPEN\";\n      error.value = null;\n    };\n    es.onerror = (e) => {\n      status.value = \"CLOSED\";\n      error.value = e;\n      if (es.readyState === 2 && !explicitlyClosed && autoReconnect) {\n        es.close();\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions$1(autoReconnect);\n        retried += 1;\n        if (typeof retries === \"number\" && (retries < 0 || retried < retries))\n          setTimeout(_init, delay);\n        else if (typeof retries === \"function\" && retries())\n          setTimeout(_init, delay);\n        else\n          onFailed == null ? void 0 : onFailed();\n      }\n    };\n    es.onmessage = (e) => {\n      event.value = null;\n      data.value = e.data;\n      lastEventId.value = e.lastEventId;\n    };\n    for (const event_name of events) {\n      useEventListener(es, event_name, (e) => {\n        event.value = event_name;\n        data.value = e.data || null;\n      }, { passive: true });\n    }\n  };\n  const open = () => {\n    if (!isClient)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    open();\n  if (autoConnect)\n    watch(urlRef, open);\n  tryOnScopeDispose(close);\n  return {\n    eventSource,\n    event,\n    data,\n    status,\n    error,\n    open,\n    close,\n    lastEventId\n  };\n}\n\nfunction useEyeDropper(options = {}) {\n  const { initialValue = \"\" } = options;\n  const isSupported = useSupported(() => typeof window !== \"undefined\" && \"EyeDropper\" in window);\n  const sRGBHex = shallowRef(initialValue);\n  async function open(openOptions) {\n    if (!isSupported.value)\n      return;\n    const eyeDropper = new window.EyeDropper();\n    const result = await eyeDropper.open(openOptions);\n    sRGBHex.value = result.sRGBHex;\n    return result;\n  }\n  return { isSupported, sRGBHex, open };\n}\n\nfunction useFavicon(newIcon = null, options = {}) {\n  const {\n    baseUrl = \"\",\n    rel = \"icon\",\n    document = defaultDocument\n  } = options;\n  const favicon = toRef(newIcon);\n  const applyIcon = (icon) => {\n    const elements = document == null ? void 0 : document.head.querySelectorAll(`link[rel*=\"${rel}\"]`);\n    if (!elements || elements.length === 0) {\n      const link = document == null ? void 0 : document.createElement(\"link\");\n      if (link) {\n        link.rel = rel;\n        link.href = `${baseUrl}${icon}`;\n        link.type = `image/${icon.split(\".\").pop()}`;\n        document == null ? void 0 : document.head.append(link);\n      }\n      return;\n    }\n    elements == null ? void 0 : elements.forEach((el) => el.href = `${baseUrl}${icon}`);\n  };\n  watch(\n    favicon,\n    (i, o) => {\n      if (typeof i === \"string\" && i !== o)\n        applyIcon(i);\n    },\n    { immediate: true }\n  );\n  return favicon;\n}\n\nconst payloadMapping = {\n  json: \"application/json\",\n  text: \"text/plain\"\n};\nfunction isFetchOptions(obj) {\n  return obj && containsProp(obj, \"immediate\", \"refetch\", \"initialData\", \"timeout\", \"beforeFetch\", \"afterFetch\", \"onFetchError\", \"fetch\", \"updateDataOnError\");\n}\nconst reAbsolute = /^(?:[a-z][a-z\\d+\\-.]*:)?\\/\\//i;\nfunction isAbsoluteURL(url) {\n  return reAbsolute.test(url);\n}\nfunction headersToObject(headers) {\n  if (typeof Headers !== \"undefined\" && headers instanceof Headers)\n    return Object.fromEntries(headers.entries());\n  return headers;\n}\nfunction combineCallbacks(combination, ...callbacks) {\n  if (combination === \"overwrite\") {\n    return async (ctx) => {\n      let callback;\n      for (let i = callbacks.length - 1; i >= 0; i--) {\n        if (callbacks[i] != null) {\n          callback = callbacks[i];\n          break;\n        }\n      }\n      if (callback)\n        return { ...ctx, ...await callback(ctx) };\n      return ctx;\n    };\n  } else {\n    return async (ctx) => {\n      for (const callback of callbacks) {\n        if (callback)\n          ctx = { ...ctx, ...await callback(ctx) };\n      }\n      return ctx;\n    };\n  }\n}\nfunction createFetch(config = {}) {\n  const _combination = config.combination || \"chain\";\n  const _options = config.options || {};\n  const _fetchOptions = config.fetchOptions || {};\n  function useFactoryFetch(url, ...args) {\n    const computedUrl = computed(() => {\n      const baseUrl = toValue(config.baseUrl);\n      const targetUrl = toValue(url);\n      return baseUrl && !isAbsoluteURL(targetUrl) ? joinPaths(baseUrl, targetUrl) : targetUrl;\n    });\n    let options = _options;\n    let fetchOptions = _fetchOptions;\n    if (args.length > 0) {\n      if (isFetchOptions(args[0])) {\n        options = {\n          ...options,\n          ...args[0],\n          beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[0].beforeFetch),\n          afterFetch: combineCallbacks(_combination, _options.afterFetch, args[0].afterFetch),\n          onFetchError: combineCallbacks(_combination, _options.onFetchError, args[0].onFetchError)\n        };\n      } else {\n        fetchOptions = {\n          ...fetchOptions,\n          ...args[0],\n          headers: {\n            ...headersToObject(fetchOptions.headers) || {},\n            ...headersToObject(args[0].headers) || {}\n          }\n        };\n      }\n    }\n    if (args.length > 1 && isFetchOptions(args[1])) {\n      options = {\n        ...options,\n        ...args[1],\n        beforeFetch: combineCallbacks(_combination, _options.beforeFetch, args[1].beforeFetch),\n        afterFetch: combineCallbacks(_combination, _options.afterFetch, args[1].afterFetch),\n        onFetchError: combineCallbacks(_combination, _options.onFetchError, args[1].onFetchError)\n      };\n    }\n    return useFetch(computedUrl, fetchOptions, options);\n  }\n  return useFactoryFetch;\n}\nfunction useFetch(url, ...args) {\n  var _a;\n  const supportsAbort = typeof AbortController === \"function\";\n  let fetchOptions = {};\n  let options = {\n    immediate: true,\n    refetch: false,\n    timeout: 0,\n    updateDataOnError: false\n  };\n  const config = {\n    method: \"GET\",\n    type: \"text\",\n    payload: void 0\n  };\n  if (args.length > 0) {\n    if (isFetchOptions(args[0]))\n      options = { ...options, ...args[0] };\n    else\n      fetchOptions = args[0];\n  }\n  if (args.length > 1) {\n    if (isFetchOptions(args[1]))\n      options = { ...options, ...args[1] };\n  }\n  const {\n    fetch = (_a = defaultWindow) == null ? void 0 : _a.fetch,\n    initialData,\n    timeout\n  } = options;\n  const responseEvent = createEventHook();\n  const errorEvent = createEventHook();\n  const finallyEvent = createEventHook();\n  const isFinished = shallowRef(false);\n  const isFetching = shallowRef(false);\n  const aborted = shallowRef(false);\n  const statusCode = shallowRef(null);\n  const response = shallowRef(null);\n  const error = shallowRef(null);\n  const data = shallowRef(initialData || null);\n  const canAbort = computed(() => supportsAbort && isFetching.value);\n  let controller;\n  let timer;\n  const abort = () => {\n    if (supportsAbort) {\n      controller == null ? void 0 : controller.abort();\n      controller = new AbortController();\n      controller.signal.onabort = () => aborted.value = true;\n      fetchOptions = {\n        ...fetchOptions,\n        signal: controller.signal\n      };\n    }\n  };\n  const loading = (isLoading) => {\n    isFetching.value = isLoading;\n    isFinished.value = !isLoading;\n  };\n  if (timeout)\n    timer = useTimeoutFn(abort, timeout, { immediate: false });\n  let executeCounter = 0;\n  const execute = async (throwOnFailed = false) => {\n    var _a2, _b;\n    abort();\n    loading(true);\n    error.value = null;\n    statusCode.value = null;\n    aborted.value = false;\n    executeCounter += 1;\n    const currentExecuteCounter = executeCounter;\n    const defaultFetchOptions = {\n      method: config.method,\n      headers: {}\n    };\n    const payload = toValue(config.payload);\n    if (payload) {\n      const headers = headersToObject(defaultFetchOptions.headers);\n      const proto = Object.getPrototypeOf(payload);\n      if (!config.payloadType && payload && (proto === Object.prototype || Array.isArray(proto)) && !(payload instanceof FormData))\n        config.payloadType = \"json\";\n      if (config.payloadType)\n        headers[\"Content-Type\"] = (_a2 = payloadMapping[config.payloadType]) != null ? _a2 : config.payloadType;\n      defaultFetchOptions.body = config.payloadType === \"json\" ? JSON.stringify(payload) : payload;\n    }\n    let isCanceled = false;\n    const context = {\n      url: toValue(url),\n      options: {\n        ...defaultFetchOptions,\n        ...fetchOptions\n      },\n      cancel: () => {\n        isCanceled = true;\n      }\n    };\n    if (options.beforeFetch)\n      Object.assign(context, await options.beforeFetch(context));\n    if (isCanceled || !fetch) {\n      loading(false);\n      return Promise.resolve(null);\n    }\n    let responseData = null;\n    if (timer)\n      timer.start();\n    return fetch(\n      context.url,\n      {\n        ...defaultFetchOptions,\n        ...context.options,\n        headers: {\n          ...headersToObject(defaultFetchOptions.headers),\n          ...headersToObject((_b = context.options) == null ? void 0 : _b.headers)\n        }\n      }\n    ).then(async (fetchResponse) => {\n      response.value = fetchResponse;\n      statusCode.value = fetchResponse.status;\n      responseData = await fetchResponse.clone()[config.type]();\n      if (!fetchResponse.ok) {\n        data.value = initialData || null;\n        throw new Error(fetchResponse.statusText);\n      }\n      if (options.afterFetch) {\n        ({ data: responseData } = await options.afterFetch({\n          data: responseData,\n          response: fetchResponse,\n          context,\n          execute\n        }));\n      }\n      data.value = responseData;\n      responseEvent.trigger(fetchResponse);\n      return fetchResponse;\n    }).catch(async (fetchError) => {\n      let errorData = fetchError.message || fetchError.name;\n      if (options.onFetchError) {\n        ({ error: errorData, data: responseData } = await options.onFetchError({\n          data: responseData,\n          error: fetchError,\n          response: response.value,\n          context,\n          execute\n        }));\n      }\n      error.value = errorData;\n      if (options.updateDataOnError)\n        data.value = responseData;\n      errorEvent.trigger(fetchError);\n      if (throwOnFailed)\n        throw fetchError;\n      return null;\n    }).finally(() => {\n      if (currentExecuteCounter === executeCounter)\n        loading(false);\n      if (timer)\n        timer.stop();\n      finallyEvent.trigger(null);\n    });\n  };\n  const refetch = toRef(options.refetch);\n  watch(\n    [\n      refetch,\n      toRef(url)\n    ],\n    ([refetch2]) => refetch2 && execute(),\n    { deep: true }\n  );\n  const shell = {\n    isFinished: readonly(isFinished),\n    isFetching: readonly(isFetching),\n    statusCode,\n    response,\n    error,\n    data,\n    canAbort,\n    aborted,\n    abort,\n    execute,\n    onFetchResponse: responseEvent.on,\n    onFetchError: errorEvent.on,\n    onFetchFinally: finallyEvent.on,\n    // method\n    get: setMethod(\"GET\"),\n    put: setMethod(\"PUT\"),\n    post: setMethod(\"POST\"),\n    delete: setMethod(\"DELETE\"),\n    patch: setMethod(\"PATCH\"),\n    head: setMethod(\"HEAD\"),\n    options: setMethod(\"OPTIONS\"),\n    // type\n    json: setType(\"json\"),\n    text: setType(\"text\"),\n    blob: setType(\"blob\"),\n    arrayBuffer: setType(\"arrayBuffer\"),\n    formData: setType(\"formData\")\n  };\n  function setMethod(method) {\n    return (payload, payloadType) => {\n      if (!isFetching.value) {\n        config.method = method;\n        config.payload = payload;\n        config.payloadType = payloadType;\n        if (isRef(config.payload)) {\n          watch(\n            [\n              refetch,\n              toRef(config.payload)\n            ],\n            ([refetch2]) => refetch2 && execute(),\n            { deep: true }\n          );\n        }\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  function waitUntilFinished() {\n    return new Promise((resolve, reject) => {\n      until(isFinished).toBe(true).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  function setType(type) {\n    return () => {\n      if (!isFetching.value) {\n        config.type = type;\n        return {\n          ...shell,\n          then(onFulfilled, onRejected) {\n            return waitUntilFinished().then(onFulfilled, onRejected);\n          }\n        };\n      }\n      return void 0;\n    };\n  }\n  if (options.immediate)\n    Promise.resolve().then(() => execute());\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilFinished().then(onFulfilled, onRejected);\n    }\n  };\n}\nfunction joinPaths(start, end) {\n  if (!start.endsWith(\"/\") && !end.startsWith(\"/\")) {\n    return `${start}/${end}`;\n  }\n  if (start.endsWith(\"/\") && end.startsWith(\"/\")) {\n    return `${start.slice(0, -1)}${end}`;\n  }\n  return `${start}${end}`;\n}\n\nconst DEFAULT_OPTIONS = {\n  multiple: true,\n  accept: \"*\",\n  reset: false,\n  directory: false\n};\nfunction prepareInitialFiles(files) {\n  if (!files)\n    return null;\n  if (files instanceof FileList)\n    return files;\n  const dt = new DataTransfer();\n  for (const file of files) {\n    dt.items.add(file);\n  }\n  return dt.files;\n}\nfunction useFileDialog(options = {}) {\n  const {\n    document = defaultDocument\n  } = options;\n  const files = ref(prepareInitialFiles(options.initialFiles));\n  const { on: onChange, trigger: changeTrigger } = createEventHook();\n  const { on: onCancel, trigger: cancelTrigger } = createEventHook();\n  let input;\n  if (document) {\n    input = document.createElement(\"input\");\n    input.type = \"file\";\n    input.onchange = (event) => {\n      const result = event.target;\n      files.value = result.files;\n      changeTrigger(files.value);\n    };\n    input.oncancel = () => {\n      cancelTrigger();\n    };\n  }\n  const reset = () => {\n    files.value = null;\n    if (input && input.value) {\n      input.value = \"\";\n      changeTrigger(null);\n    }\n  };\n  const open = (localOptions) => {\n    if (!input)\n      return;\n    const _options = {\n      ...DEFAULT_OPTIONS,\n      ...options,\n      ...localOptions\n    };\n    input.multiple = _options.multiple;\n    input.accept = _options.accept;\n    input.webkitdirectory = _options.directory;\n    if (hasOwn(_options, \"capture\"))\n      input.capture = _options.capture;\n    if (_options.reset)\n      reset();\n    input.click();\n  };\n  return {\n    files: readonly(files),\n    open,\n    reset,\n    onCancel,\n    onChange\n  };\n}\n\nfunction useFileSystemAccess(options = {}) {\n  const {\n    window: _window = defaultWindow,\n    dataType = \"Text\"\n  } = options;\n  const window = _window;\n  const isSupported = useSupported(() => window && \"showSaveFilePicker\" in window && \"showOpenFilePicker\" in window);\n  const fileHandle = shallowRef();\n  const data = shallowRef();\n  const file = shallowRef();\n  const fileName = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.name) != null ? _b : \"\";\n  });\n  const fileMIME = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.type) != null ? _b : \"\";\n  });\n  const fileSize = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.size) != null ? _b : 0;\n  });\n  const fileLastModified = computed(() => {\n    var _a, _b;\n    return (_b = (_a = file.value) == null ? void 0 : _a.lastModified) != null ? _b : 0;\n  });\n  async function open(_options = {}) {\n    if (!isSupported.value)\n      return;\n    const [handle] = await window.showOpenFilePicker({ ...toValue(options), ..._options });\n    fileHandle.value = handle;\n    await updateData();\n  }\n  async function create(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    data.value = void 0;\n    await updateData();\n  }\n  async function save(_options = {}) {\n    if (!isSupported.value)\n      return;\n    if (!fileHandle.value)\n      return saveAs(_options);\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function saveAs(_options = {}) {\n    if (!isSupported.value)\n      return;\n    fileHandle.value = await window.showSaveFilePicker({ ...options, ..._options });\n    if (data.value) {\n      const writableStream = await fileHandle.value.createWritable();\n      await writableStream.write(data.value);\n      await writableStream.close();\n    }\n    await updateFile();\n  }\n  async function updateFile() {\n    var _a;\n    file.value = await ((_a = fileHandle.value) == null ? void 0 : _a.getFile());\n  }\n  async function updateData() {\n    var _a, _b;\n    await updateFile();\n    const type = toValue(dataType);\n    if (type === \"Text\")\n      data.value = await ((_a = file.value) == null ? void 0 : _a.text());\n    else if (type === \"ArrayBuffer\")\n      data.value = await ((_b = file.value) == null ? void 0 : _b.arrayBuffer());\n    else if (type === \"Blob\")\n      data.value = file.value;\n  }\n  watch(() => toValue(dataType), updateData);\n  return {\n    isSupported,\n    data,\n    file,\n    fileName,\n    fileMIME,\n    fileSize,\n    fileLastModified,\n    open,\n    create,\n    save,\n    saveAs,\n    updateData\n  };\n}\n\nfunction useFocus(target, options = {}) {\n  const { initialValue = false, focusVisible = false, preventScroll = false } = options;\n  const innerFocused = shallowRef(false);\n  const targetElement = computed(() => unrefElement(target));\n  const listenerOptions = { passive: true };\n  useEventListener(targetElement, \"focus\", (event) => {\n    var _a, _b;\n    if (!focusVisible || ((_b = (_a = event.target).matches) == null ? void 0 : _b.call(_a, \":focus-visible\")))\n      innerFocused.value = true;\n  }, listenerOptions);\n  useEventListener(targetElement, \"blur\", () => innerFocused.value = false, listenerOptions);\n  const focused = computed({\n    get: () => innerFocused.value,\n    set(value) {\n      var _a, _b;\n      if (!value && innerFocused.value)\n        (_a = targetElement.value) == null ? void 0 : _a.blur();\n      else if (value && !innerFocused.value)\n        (_b = targetElement.value) == null ? void 0 : _b.focus({ preventScroll });\n    }\n  });\n  watch(\n    targetElement,\n    () => {\n      focused.value = initialValue;\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  return { focused };\n}\n\nconst EVENT_FOCUS_IN = \"focusin\";\nconst EVENT_FOCUS_OUT = \"focusout\";\nconst PSEUDO_CLASS_FOCUS_WITHIN = \":focus-within\";\nfunction useFocusWithin(target, options = {}) {\n  const { window = defaultWindow } = options;\n  const targetElement = computed(() => unrefElement(target));\n  const _focused = shallowRef(false);\n  const focused = computed(() => _focused.value);\n  const activeElement = useActiveElement(options);\n  if (!window || !activeElement.value) {\n    return { focused };\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(targetElement, EVENT_FOCUS_IN, () => _focused.value = true, listenerOptions);\n  useEventListener(targetElement, EVENT_FOCUS_OUT, () => {\n    var _a, _b, _c;\n    return _focused.value = (_c = (_b = (_a = targetElement.value) == null ? void 0 : _a.matches) == null ? void 0 : _b.call(_a, PSEUDO_CLASS_FOCUS_WITHIN)) != null ? _c : false;\n  }, listenerOptions);\n  return { focused };\n}\n\nfunction useFps(options) {\n  var _a;\n  const fps = shallowRef(0);\n  if (typeof performance === \"undefined\")\n    return fps;\n  const every = (_a = options == null ? void 0 : options.every) != null ? _a : 10;\n  let last = performance.now();\n  let ticks = 0;\n  useRafFn(() => {\n    ticks += 1;\n    if (ticks >= every) {\n      const now = performance.now();\n      const diff = now - last;\n      fps.value = Math.round(1e3 / (diff / ticks));\n      last = now;\n      ticks = 0;\n    }\n  });\n  return fps;\n}\n\nconst eventHandlers = [\n  \"fullscreenchange\",\n  \"webkitfullscreenchange\",\n  \"webkitendfullscreen\",\n  \"mozfullscreenchange\",\n  \"MSFullscreenChange\"\n];\nfunction useFullscreen(target, options = {}) {\n  const {\n    document = defaultDocument,\n    autoExit = false\n  } = options;\n  const targetRef = computed(() => {\n    var _a;\n    return (_a = unrefElement(target)) != null ? _a : document == null ? void 0 : document.documentElement;\n  });\n  const isFullscreen = shallowRef(false);\n  const requestMethod = computed(() => {\n    return [\n      \"requestFullscreen\",\n      \"webkitRequestFullscreen\",\n      \"webkitEnterFullscreen\",\n      \"webkitEnterFullScreen\",\n      \"webkitRequestFullScreen\",\n      \"mozRequestFullScreen\",\n      \"msRequestFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const exitMethod = computed(() => {\n    return [\n      \"exitFullscreen\",\n      \"webkitExitFullscreen\",\n      \"webkitExitFullScreen\",\n      \"webkitCancelFullScreen\",\n      \"mozCancelFullScreen\",\n      \"msExitFullscreen\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenEnabled = computed(() => {\n    return [\n      \"fullScreen\",\n      \"webkitIsFullScreen\",\n      \"webkitDisplayingFullscreen\",\n      \"mozFullScreen\",\n      \"msFullscreenElement\"\n    ].find((m) => document && m in document || targetRef.value && m in targetRef.value);\n  });\n  const fullscreenElementMethod = [\n    \"fullscreenElement\",\n    \"webkitFullscreenElement\",\n    \"mozFullScreenElement\",\n    \"msFullscreenElement\"\n  ].find((m) => document && m in document);\n  const isSupported = useSupported(() => targetRef.value && document && requestMethod.value !== void 0 && exitMethod.value !== void 0 && fullscreenEnabled.value !== void 0);\n  const isCurrentElementFullScreen = () => {\n    if (fullscreenElementMethod)\n      return (document == null ? void 0 : document[fullscreenElementMethod]) === targetRef.value;\n    return false;\n  };\n  const isElementFullScreen = () => {\n    if (fullscreenEnabled.value) {\n      if (document && document[fullscreenEnabled.value] != null) {\n        return document[fullscreenEnabled.value];\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[fullscreenEnabled.value]) != null) {\n          return Boolean(target2[fullscreenEnabled.value]);\n        }\n      }\n    }\n    return false;\n  };\n  async function exit() {\n    if (!isSupported.value || !isFullscreen.value)\n      return;\n    if (exitMethod.value) {\n      if ((document == null ? void 0 : document[exitMethod.value]) != null) {\n        await document[exitMethod.value]();\n      } else {\n        const target2 = targetRef.value;\n        if ((target2 == null ? void 0 : target2[exitMethod.value]) != null)\n          await target2[exitMethod.value]();\n      }\n    }\n    isFullscreen.value = false;\n  }\n  async function enter() {\n    if (!isSupported.value || isFullscreen.value)\n      return;\n    if (isElementFullScreen())\n      await exit();\n    const target2 = targetRef.value;\n    if (requestMethod.value && (target2 == null ? void 0 : target2[requestMethod.value]) != null) {\n      await target2[requestMethod.value]();\n      isFullscreen.value = true;\n    }\n  }\n  async function toggle() {\n    await (isFullscreen.value ? exit() : enter());\n  }\n  const handlerCallback = () => {\n    const isElementFullScreenValue = isElementFullScreen();\n    if (!isElementFullScreenValue || isElementFullScreenValue && isCurrentElementFullScreen())\n      isFullscreen.value = isElementFullScreenValue;\n  };\n  const listenerOptions = { capture: false, passive: true };\n  useEventListener(document, eventHandlers, handlerCallback, listenerOptions);\n  useEventListener(() => unrefElement(targetRef), eventHandlers, handlerCallback, listenerOptions);\n  if (autoExit)\n    tryOnScopeDispose(exit);\n  return {\n    isSupported,\n    isFullscreen,\n    enter,\n    exit,\n    toggle\n  };\n}\n\nfunction mapGamepadToXbox360Controller(gamepad) {\n  return computed(() => {\n    if (gamepad.value) {\n      return {\n        buttons: {\n          a: gamepad.value.buttons[0],\n          b: gamepad.value.buttons[1],\n          x: gamepad.value.buttons[2],\n          y: gamepad.value.buttons[3]\n        },\n        bumper: {\n          left: gamepad.value.buttons[4],\n          right: gamepad.value.buttons[5]\n        },\n        triggers: {\n          left: gamepad.value.buttons[6],\n          right: gamepad.value.buttons[7]\n        },\n        stick: {\n          left: {\n            horizontal: gamepad.value.axes[0],\n            vertical: gamepad.value.axes[1],\n            button: gamepad.value.buttons[10]\n          },\n          right: {\n            horizontal: gamepad.value.axes[2],\n            vertical: gamepad.value.axes[3],\n            button: gamepad.value.buttons[11]\n          }\n        },\n        dpad: {\n          up: gamepad.value.buttons[12],\n          down: gamepad.value.buttons[13],\n          left: gamepad.value.buttons[14],\n          right: gamepad.value.buttons[15]\n        },\n        back: gamepad.value.buttons[8],\n        start: gamepad.value.buttons[9]\n      };\n    }\n    return null;\n  });\n}\nfunction useGamepad(options = {}) {\n  const {\n    navigator = defaultNavigator\n  } = options;\n  const isSupported = useSupported(() => navigator && \"getGamepads\" in navigator);\n  const gamepads = ref([]);\n  const onConnectedHook = createEventHook();\n  const onDisconnectedHook = createEventHook();\n  const stateFromGamepad = (gamepad) => {\n    const hapticActuators = [];\n    const vibrationActuator = \"vibrationActuator\" in gamepad ? gamepad.vibrationActuator : null;\n    if (vibrationActuator)\n      hapticActuators.push(vibrationActuator);\n    if (gamepad.hapticActuators)\n      hapticActuators.push(...gamepad.hapticActuators);\n    return {\n      id: gamepad.id,\n      index: gamepad.index,\n      connected: gamepad.connected,\n      mapping: gamepad.mapping,\n      timestamp: gamepad.timestamp,\n      vibrationActuator: gamepad.vibrationActuator,\n      hapticActuators,\n      axes: gamepad.axes.map((axes) => axes),\n      buttons: gamepad.buttons.map((button) => ({ pressed: button.pressed, touched: button.touched, value: button.value }))\n    };\n  };\n  const updateGamepadState = () => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (const gamepad of _gamepads) {\n      if (gamepad && gamepads.value[gamepad.index])\n        gamepads.value[gamepad.index] = stateFromGamepad(gamepad);\n    }\n  };\n  const { isActive, pause, resume } = useRafFn(updateGamepadState);\n  const onGamepadConnected = (gamepad) => {\n    if (!gamepads.value.some(({ index }) => index === gamepad.index)) {\n      gamepads.value.push(stateFromGamepad(gamepad));\n      onConnectedHook.trigger(gamepad.index);\n    }\n    resume();\n  };\n  const onGamepadDisconnected = (gamepad) => {\n    gamepads.value = gamepads.value.filter((x) => x.index !== gamepad.index);\n    onDisconnectedHook.trigger(gamepad.index);\n  };\n  const listenerOptions = { passive: true };\n  useEventListener(\"gamepadconnected\", (e) => onGamepadConnected(e.gamepad), listenerOptions);\n  useEventListener(\"gamepaddisconnected\", (e) => onGamepadDisconnected(e.gamepad), listenerOptions);\n  tryOnMounted(() => {\n    const _gamepads = (navigator == null ? void 0 : navigator.getGamepads()) || [];\n    for (const gamepad of _gamepads) {\n      if (gamepad && gamepads.value[gamepad.index])\n        onGamepadConnected(gamepad);\n    }\n  });\n  pause();\n  return {\n    isSupported,\n    onConnected: onConnectedHook.on,\n    onDisconnected: onDisconnectedHook.on,\n    gamepads,\n    pause,\n    resume,\n    isActive\n  };\n}\n\nfunction useGeolocation(options = {}) {\n  const {\n    enableHighAccuracy = true,\n    maximumAge = 3e4,\n    timeout = 27e3,\n    navigator = defaultNavigator,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => navigator && \"geolocation\" in navigator);\n  const locatedAt = shallowRef(null);\n  const error = shallowRef(null);\n  const coords = ref({\n    accuracy: 0,\n    latitude: Number.POSITIVE_INFINITY,\n    longitude: Number.POSITIVE_INFINITY,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n  });\n  function updatePosition(position) {\n    locatedAt.value = position.timestamp;\n    coords.value = position.coords;\n    error.value = null;\n  }\n  let watcher;\n  function resume() {\n    if (isSupported.value) {\n      watcher = navigator.geolocation.watchPosition(\n        updatePosition,\n        (err) => error.value = err,\n        {\n          enableHighAccuracy,\n          maximumAge,\n          timeout\n        }\n      );\n    }\n  }\n  if (immediate)\n    resume();\n  function pause() {\n    if (watcher && navigator)\n      navigator.geolocation.clearWatch(watcher);\n  }\n  tryOnScopeDispose(() => {\n    pause();\n  });\n  return {\n    isSupported,\n    coords,\n    locatedAt,\n    error,\n    resume,\n    pause\n  };\n}\n\nconst defaultEvents$1 = [\"mousemove\", \"mousedown\", \"resize\", \"keydown\", \"touchstart\", \"wheel\"];\nconst oneMinute = 6e4;\nfunction useIdle(timeout = oneMinute, options = {}) {\n  const {\n    initialState = false,\n    listenForVisibilityChange = true,\n    events = defaultEvents$1,\n    window = defaultWindow,\n    eventFilter = throttleFilter(50)\n  } = options;\n  const idle = shallowRef(initialState);\n  const lastActive = shallowRef(timestamp());\n  let timer;\n  const reset = () => {\n    idle.value = false;\n    clearTimeout(timer);\n    timer = setTimeout(() => idle.value = true, timeout);\n  };\n  const onEvent = createFilterWrapper(\n    eventFilter,\n    () => {\n      lastActive.value = timestamp();\n      reset();\n    }\n  );\n  if (window) {\n    const document = window.document;\n    const listenerOptions = { passive: true };\n    for (const event of events)\n      useEventListener(window, event, onEvent, listenerOptions);\n    if (listenForVisibilityChange) {\n      useEventListener(document, \"visibilitychange\", () => {\n        if (!document.hidden)\n          onEvent();\n      }, listenerOptions);\n    }\n    reset();\n  }\n  return {\n    idle,\n    lastActive,\n    reset\n  };\n}\n\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes, class: clazz, loading, crossorigin, referrerPolicy, width, height, decoding, fetchPriority, ismap, usemap } = options;\n    img.src = src;\n    if (srcset != null)\n      img.srcset = srcset;\n    if (sizes != null)\n      img.sizes = sizes;\n    if (clazz != null)\n      img.className = clazz;\n    if (loading != null)\n      img.loading = loading;\n    if (crossorigin != null)\n      img.crossOrigin = crossorigin;\n    if (referrerPolicy != null)\n      img.referrerPolicy = referrerPolicy;\n    if (width != null)\n      img.width = width;\n    if (height != null)\n      img.height = height;\n    if (decoding != null)\n      img.decoding = decoding;\n    if (fetchPriority != null)\n      img.fetchPriority = fetchPriority;\n    if (ismap != null)\n      img.isMap = ismap;\n    if (usemap != null)\n      img.useMap = usemap;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nfunction useImage(options, asyncStateOptions = {}) {\n  const state = useAsyncState(\n    () => loadImage(toValue(options)),\n    void 0,\n    {\n      resetOnExecute: true,\n      ...asyncStateOptions\n    }\n  );\n  watch(\n    () => toValue(options),\n    () => state.execute(asyncStateOptions.delay),\n    { deep: true }\n  );\n  return state;\n}\n\nfunction resolveElement(el) {\n  if (typeof Window !== \"undefined\" && el instanceof Window)\n    return el.document.documentElement;\n  if (typeof Document !== \"undefined\" && el instanceof Document)\n    return el.documentElement;\n  return el;\n}\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\",\n    window = defaultWindow,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const internalX = shallowRef(0);\n  const internalY = shallowRef(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c, _d;\n    if (!window)\n      return;\n    const _element = toValue(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? window.document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = toValue(_y)) != null ? _a : y.value,\n      left: (_b = toValue(_x)) != null ? _b : x.value,\n      behavior: toValue(behavior)\n    });\n    const scrollContainer = ((_d = _element == null ? void 0 : _element.document) == null ? void 0 : _d.documentElement) || (_element == null ? void 0 : _element.documentElement) || _element;\n    if (x != null)\n      internalX.value = scrollContainer.scrollLeft;\n    if (y != null)\n      internalY.value = scrollContainer.scrollTop;\n  }\n  const isScrolling = shallowRef(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const setArrivedState = (target) => {\n    var _a;\n    if (!window)\n      return;\n    const el = ((_a = target == null ? void 0 : target.document) == null ? void 0 : _a.documentElement) || (target == null ? void 0 : target.documentElement) || unrefElement(target);\n    const { display, flexDirection, direction } = getComputedStyle(el);\n    const directionMultipler = direction === \"rtl\" ? -1 : 1;\n    const scrollLeft = el.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalX.value;\n    const left = Math.abs(scrollLeft * directionMultipler) <= (offset.left || 0);\n    const right = Math.abs(scrollLeft * directionMultipler) + el.clientWidth >= el.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"row-reverse\") {\n      arrivedState.left = right;\n      arrivedState.right = left;\n    } else {\n      arrivedState.left = left;\n      arrivedState.right = right;\n    }\n    internalX.value = scrollLeft;\n    let scrollTop = el.scrollTop;\n    if (target === window.document && !scrollTop)\n      scrollTop = window.document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    const top = Math.abs(scrollTop) <= (offset.top || 0);\n    const bottom = Math.abs(scrollTop) + el.clientHeight >= el.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"column-reverse\") {\n      arrivedState.top = bottom;\n      arrivedState.bottom = top;\n    } else {\n      arrivedState.top = top;\n      arrivedState.bottom = bottom;\n    }\n    internalY.value = scrollTop;\n  };\n  const onScrollHandler = (e) => {\n    var _a;\n    if (!window)\n      return;\n    const eventTarget = (_a = e.target.documentElement) != null ? _a : e.target;\n    setArrivedState(eventTarget);\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(\n    element,\n    \"scroll\",\n    throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler,\n    eventListenerOptions\n  );\n  tryOnMounted(() => {\n    try {\n      const _element = toValue(element);\n      if (!_element)\n        return;\n      setArrivedState(_element);\n    } catch (e) {\n      onError(e);\n    }\n  });\n  useEventListener(\n    element,\n    \"scrollend\",\n    onScrollEnd,\n    eventListenerOptions\n  );\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions,\n    measure() {\n      const _element = toValue(element);\n      if (window && _element)\n        setArrivedState(_element);\n    }\n  };\n}\n\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a;\n  const {\n    direction = \"bottom\",\n    interval = 100,\n    canLoadMore = () => true\n  } = options;\n  const state = reactive(useScroll(\n    element,\n    {\n      ...options,\n      offset: {\n        [direction]: (_a = options.distance) != null ? _a : 0,\n        ...options.offset\n      }\n    }\n  ));\n  const promise = ref();\n  const isLoading = computed(() => !!promise.value);\n  const observedElement = computed(() => {\n    return resolveElement(toValue(element));\n  });\n  const isElementVisible = useElementVisibility(observedElement);\n  function checkAndLoad() {\n    state.measure();\n    if (!observedElement.value || !isElementVisible.value || !canLoadMore(observedElement.value))\n      return;\n    const { scrollHeight, clientHeight, scrollWidth, clientWidth } = observedElement.value;\n    const isNarrower = direction === \"bottom\" || direction === \"top\" ? scrollHeight <= clientHeight : scrollWidth <= clientWidth;\n    if (state.arrivedState[direction] || isNarrower) {\n      if (!promise.value) {\n        promise.value = Promise.all([\n          onLoadMore(state),\n          new Promise((resolve) => setTimeout(resolve, interval))\n        ]).finally(() => {\n          promise.value = null;\n          nextTick(() => checkAndLoad());\n        });\n      }\n    }\n  }\n  const stop = watch(\n    () => [state.arrivedState[direction], isElementVisible.value],\n    checkAndLoad,\n    { immediate: true }\n  );\n  tryOnUnmounted(stop);\n  return {\n    isLoading,\n    reset() {\n      nextTick(() => checkAndLoad());\n    }\n  };\n}\n\nconst defaultEvents = [\"mousedown\", \"mouseup\", \"keydown\", \"keyup\"];\nfunction useKeyModifier(modifier, options = {}) {\n  const {\n    events = defaultEvents,\n    document = defaultDocument,\n    initial = null\n  } = options;\n  const state = shallowRef(initial);\n  if (document) {\n    events.forEach((listenerEvent) => {\n      useEventListener(document, listenerEvent, (evt) => {\n        if (typeof evt.getModifierState === \"function\")\n          state.value = evt.getModifierState(modifier);\n      }, { passive: true });\n    });\n  }\n  return state;\n}\n\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.localStorage, options);\n}\n\nconst DefaultMagicKeysAliasMap = {\n  ctrl: \"control\",\n  command: \"meta\",\n  cmd: \"meta\",\n  option: \"alt\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  left: \"arrowleft\",\n  right: \"arrowright\"\n};\n\nfunction useMagicKeys(options = {}) {\n  const {\n    reactive: useReactive = false,\n    target = defaultWindow,\n    aliasMap = DefaultMagicKeysAliasMap,\n    passive = true,\n    onEventFired = noop\n  } = options;\n  const current = reactive(/* @__PURE__ */ new Set());\n  const obj = {\n    toJSON() {\n      return {};\n    },\n    current\n  };\n  const refs = useReactive ? reactive(obj) : obj;\n  const metaDeps = /* @__PURE__ */ new Set();\n  const usedKeys = /* @__PURE__ */ new Set();\n  function setRefs(key, value) {\n    if (key in refs) {\n      if (useReactive)\n        refs[key] = value;\n      else\n        refs[key].value = value;\n    }\n  }\n  function reset() {\n    current.clear();\n    for (const key of usedKeys)\n      setRefs(key, false);\n  }\n  function updateRefs(e, value) {\n    var _a, _b;\n    const key = (_a = e.key) == null ? void 0 : _a.toLowerCase();\n    const code = (_b = e.code) == null ? void 0 : _b.toLowerCase();\n    const values = [code, key].filter(Boolean);\n    if (key) {\n      if (value)\n        current.add(key);\n      else\n        current.delete(key);\n    }\n    for (const key2 of values) {\n      usedKeys.add(key2);\n      setRefs(key2, value);\n    }\n    if (key === \"meta\" && !value) {\n      metaDeps.forEach((key2) => {\n        current.delete(key2);\n        setRefs(key2, false);\n      });\n      metaDeps.clear();\n    } else if (typeof e.getModifierState === \"function\" && e.getModifierState(\"Meta\") && value) {\n      [...current, ...values].forEach((key2) => metaDeps.add(key2));\n    }\n  }\n  useEventListener(target, \"keydown\", (e) => {\n    updateRefs(e, true);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(target, \"keyup\", (e) => {\n    updateRefs(e, false);\n    return onEventFired(e);\n  }, { passive });\n  useEventListener(\"blur\", reset, { passive });\n  useEventListener(\"focus\", reset, { passive });\n  const proxy = new Proxy(\n    refs,\n    {\n      get(target2, prop, rec) {\n        if (typeof prop !== \"string\")\n          return Reflect.get(target2, prop, rec);\n        prop = prop.toLowerCase();\n        if (prop in aliasMap)\n          prop = aliasMap[prop];\n        if (!(prop in refs)) {\n          if (/[+_-]/.test(prop)) {\n            const keys = prop.split(/[+_-]/g).map((i) => i.trim());\n            refs[prop] = computed(() => keys.map((key) => toValue(proxy[key])).every(Boolean));\n          } else {\n            refs[prop] = shallowRef(false);\n          }\n        }\n        const r = Reflect.get(target2, prop, rec);\n        return useReactive ? toValue(r) : r;\n      }\n    }\n  );\n  return proxy;\n}\n\nfunction usingElRef(source, cb) {\n  if (toValue(source))\n    cb(toValue(source));\n}\nfunction timeRangeToArray(timeRanges) {\n  let ranges = [];\n  for (let i = 0; i < timeRanges.length; ++i)\n    ranges = [...ranges, [timeRanges.start(i), timeRanges.end(i)]];\n  return ranges;\n}\nfunction tracksToArray(tracks) {\n  return Array.from(tracks).map(({ label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }, id) => ({ id, label, kind, language, mode, activeCues, cues, inBandMetadataTrackDispatchType }));\n}\nconst defaultOptions = {\n  src: \"\",\n  tracks: []\n};\nfunction useMediaControls(target, options = {}) {\n  target = toRef(target);\n  options = {\n    ...defaultOptions,\n    ...options\n  };\n  const {\n    document = defaultDocument\n  } = options;\n  const listenerOptions = { passive: true };\n  const currentTime = shallowRef(0);\n  const duration = shallowRef(0);\n  const seeking = shallowRef(false);\n  const volume = shallowRef(1);\n  const waiting = shallowRef(false);\n  const ended = shallowRef(false);\n  const playing = shallowRef(false);\n  const rate = shallowRef(1);\n  const stalled = shallowRef(false);\n  const buffered = ref([]);\n  const tracks = ref([]);\n  const selectedTrack = shallowRef(-1);\n  const isPictureInPicture = shallowRef(false);\n  const muted = shallowRef(false);\n  const supportsPictureInPicture = document && \"pictureInPictureEnabled\" in document;\n  const sourceErrorEvent = createEventHook();\n  const playbackErrorEvent = createEventHook();\n  const disableTrack = (track) => {\n    usingElRef(target, (el) => {\n      if (track) {\n        const id = typeof track === \"number\" ? track : track.id;\n        el.textTracks[id].mode = \"disabled\";\n      } else {\n        for (let i = 0; i < el.textTracks.length; ++i)\n          el.textTracks[i].mode = \"disabled\";\n      }\n      selectedTrack.value = -1;\n    });\n  };\n  const enableTrack = (track, disableTracks = true) => {\n    usingElRef(target, (el) => {\n      const id = typeof track === \"number\" ? track : track.id;\n      if (disableTracks)\n        disableTrack();\n      el.textTracks[id].mode = \"showing\";\n      selectedTrack.value = id;\n    });\n  };\n  const togglePictureInPicture = () => {\n    return new Promise((resolve, reject) => {\n      usingElRef(target, async (el) => {\n        if (supportsPictureInPicture) {\n          if (!isPictureInPicture.value) {\n            el.requestPictureInPicture().then(resolve).catch(reject);\n          } else {\n            document.exitPictureInPicture().then(resolve).catch(reject);\n          }\n        }\n      });\n    });\n  };\n  watchEffect(() => {\n    if (!document)\n      return;\n    const el = toValue(target);\n    if (!el)\n      return;\n    const src = toValue(options.src);\n    let sources = [];\n    if (!src)\n      return;\n    if (typeof src === \"string\")\n      sources = [{ src }];\n    else if (Array.isArray(src))\n      sources = src;\n    else if (isObject(src))\n      sources = [src];\n    el.querySelectorAll(\"source\").forEach((e) => {\n      e.remove();\n    });\n    sources.forEach(({ src: src2, type, media }) => {\n      const source = document.createElement(\"source\");\n      source.setAttribute(\"src\", src2);\n      source.setAttribute(\"type\", type || \"\");\n      source.setAttribute(\"media\", media || \"\");\n      useEventListener(source, \"error\", sourceErrorEvent.trigger, listenerOptions);\n      el.appendChild(source);\n    });\n    el.load();\n  });\n  watch([target, volume], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.volume = volume.value;\n  });\n  watch([target, muted], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.muted = muted.value;\n  });\n  watch([target, rate], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.playbackRate = rate.value;\n  });\n  watchEffect(() => {\n    if (!document)\n      return;\n    const textTracks = toValue(options.tracks);\n    const el = toValue(target);\n    if (!textTracks || !textTracks.length || !el)\n      return;\n    el.querySelectorAll(\"track\").forEach((e) => e.remove());\n    textTracks.forEach(({ default: isDefault, kind, label, src, srcLang }, i) => {\n      const track = document.createElement(\"track\");\n      track.default = isDefault || false;\n      track.kind = kind;\n      track.label = label;\n      track.src = src;\n      track.srclang = srcLang;\n      if (track.default)\n        selectedTrack.value = i;\n      el.appendChild(track);\n    });\n  });\n  const { ignoreUpdates: ignoreCurrentTimeUpdates } = watchIgnorable(currentTime, (time) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    el.currentTime = time;\n  });\n  const { ignoreUpdates: ignorePlayingUpdates } = watchIgnorable(playing, (isPlaying) => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    if (isPlaying) {\n      el.play().catch((e) => {\n        playbackErrorEvent.trigger(e);\n        throw e;\n      });\n    } else {\n      el.pause();\n    }\n  });\n  useEventListener(\n    target,\n    \"timeupdate\",\n    () => ignoreCurrentTimeUpdates(() => currentTime.value = toValue(target).currentTime),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"durationchange\",\n    () => duration.value = toValue(target).duration,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"progress\",\n    () => buffered.value = timeRangeToArray(toValue(target).buffered),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"seeking\",\n    () => seeking.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"seeked\",\n    () => seeking.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    [\"waiting\", \"loadstart\"],\n    () => {\n      waiting.value = true;\n      ignorePlayingUpdates(() => playing.value = false);\n    },\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"loadeddata\",\n    () => waiting.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"playing\",\n    () => {\n      waiting.value = false;\n      ended.value = false;\n      ignorePlayingUpdates(() => playing.value = true);\n    },\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"ratechange\",\n    () => rate.value = toValue(target).playbackRate,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"stalled\",\n    () => stalled.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"ended\",\n    () => ended.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"pause\",\n    () => ignorePlayingUpdates(() => playing.value = false),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"play\",\n    () => ignorePlayingUpdates(() => playing.value = true),\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"enterpictureinpicture\",\n    () => isPictureInPicture.value = true,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"leavepictureinpicture\",\n    () => isPictureInPicture.value = false,\n    listenerOptions\n  );\n  useEventListener(\n    target,\n    \"volumechange\",\n    () => {\n      const el = toValue(target);\n      if (!el)\n        return;\n      volume.value = el.volume;\n      muted.value = el.muted;\n    },\n    listenerOptions\n  );\n  const listeners = [];\n  const stop = watch([target], () => {\n    const el = toValue(target);\n    if (!el)\n      return;\n    stop();\n    listeners[0] = useEventListener(el.textTracks, \"addtrack\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n    listeners[1] = useEventListener(el.textTracks, \"removetrack\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n    listeners[2] = useEventListener(el.textTracks, \"change\", () => tracks.value = tracksToArray(el.textTracks), listenerOptions);\n  });\n  tryOnScopeDispose(() => listeners.forEach((listener) => listener()));\n  return {\n    currentTime,\n    duration,\n    waiting,\n    seeking,\n    ended,\n    stalled,\n    buffered,\n    playing,\n    rate,\n    // Volume\n    volume,\n    muted,\n    // Tracks\n    tracks,\n    selectedTrack,\n    enableTrack,\n    disableTrack,\n    // Picture in Picture\n    supportsPictureInPicture,\n    togglePictureInPicture,\n    isPictureInPicture,\n    // Events\n    onSourceError: sourceErrorEvent.on,\n    onPlaybackError: playbackErrorEvent.on\n  };\n}\n\nfunction useMemoize(resolver, options) {\n  const initCache = () => {\n    if (options == null ? void 0 : options.cache)\n      return shallowReactive(options.cache);\n    return shallowReactive(/* @__PURE__ */ new Map());\n  };\n  const cache = initCache();\n  const generateKey = (...args) => (options == null ? void 0 : options.getKey) ? options.getKey(...args) : JSON.stringify(args);\n  const _loadData = (key, ...args) => {\n    cache.set(key, resolver(...args));\n    return cache.get(key);\n  };\n  const loadData = (...args) => _loadData(generateKey(...args), ...args);\n  const deleteData = (...args) => {\n    cache.delete(generateKey(...args));\n  };\n  const clearData = () => {\n    cache.clear();\n  };\n  const memoized = (...args) => {\n    const key = generateKey(...args);\n    if (cache.has(key))\n      return cache.get(key);\n    return _loadData(key, ...args);\n  };\n  memoized.load = loadData;\n  memoized.delete = deleteData;\n  memoized.clear = clearData;\n  memoized.generateKey = generateKey;\n  memoized.cache = cache;\n  return memoized;\n}\n\nfunction useMemory(options = {}) {\n  const memory = ref();\n  const isSupported = useSupported(() => typeof performance !== \"undefined\" && \"memory\" in performance);\n  if (isSupported.value) {\n    const { interval = 1e3 } = options;\n    useIntervalFn(() => {\n      memory.value = performance.memory;\n    }, interval, { immediate: options.immediate, immediateCallback: options.immediateCallback });\n  }\n  return { isSupported, memory };\n}\n\nconst UseMouseBuiltinExtractors = {\n  page: (event) => [event.pageX, event.pageY],\n  client: (event) => [event.clientX, event.clientY],\n  screen: (event) => [event.screenX, event.screenY],\n  movement: (event) => event instanceof MouseEvent ? [event.movementX, event.movementY] : null\n};\nfunction useMouse(options = {}) {\n  const {\n    type = \"page\",\n    touch = true,\n    resetOnTouchEnds = false,\n    initialValue = { x: 0, y: 0 },\n    window = defaultWindow,\n    target = window,\n    scroll = true,\n    eventFilter\n  } = options;\n  let _prevMouseEvent = null;\n  let _prevScrollX = 0;\n  let _prevScrollY = 0;\n  const x = shallowRef(initialValue.x);\n  const y = shallowRef(initialValue.y);\n  const sourceType = shallowRef(null);\n  const extractor = typeof type === \"function\" ? type : UseMouseBuiltinExtractors[type];\n  const mouseHandler = (event) => {\n    const result = extractor(event);\n    _prevMouseEvent = event;\n    if (result) {\n      [x.value, y.value] = result;\n      sourceType.value = \"mouse\";\n    }\n    if (window) {\n      _prevScrollX = window.scrollX;\n      _prevScrollY = window.scrollY;\n    }\n  };\n  const touchHandler = (event) => {\n    if (event.touches.length > 0) {\n      const result = extractor(event.touches[0]);\n      if (result) {\n        [x.value, y.value] = result;\n        sourceType.value = \"touch\";\n      }\n    }\n  };\n  const scrollHandler = () => {\n    if (!_prevMouseEvent || !window)\n      return;\n    const pos = extractor(_prevMouseEvent);\n    if (_prevMouseEvent instanceof MouseEvent && pos) {\n      x.value = pos[0] + window.scrollX - _prevScrollX;\n      y.value = pos[1] + window.scrollY - _prevScrollY;\n    }\n  };\n  const reset = () => {\n    x.value = initialValue.x;\n    y.value = initialValue.y;\n  };\n  const mouseHandlerWrapper = eventFilter ? (event) => eventFilter(() => mouseHandler(event), {}) : (event) => mouseHandler(event);\n  const touchHandlerWrapper = eventFilter ? (event) => eventFilter(() => touchHandler(event), {}) : (event) => touchHandler(event);\n  const scrollHandlerWrapper = eventFilter ? () => eventFilter(() => scrollHandler(), {}) : () => scrollHandler();\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"mousemove\", \"dragover\"], mouseHandlerWrapper, listenerOptions);\n    if (touch && type !== \"movement\") {\n      useEventListener(target, [\"touchstart\", \"touchmove\"], touchHandlerWrapper, listenerOptions);\n      if (resetOnTouchEnds)\n        useEventListener(target, \"touchend\", reset, listenerOptions);\n    }\n    if (scroll && type === \"page\")\n      useEventListener(window, \"scroll\", scrollHandlerWrapper, listenerOptions);\n  }\n  return {\n    x,\n    y,\n    sourceType\n  };\n}\n\nfunction useMouseInElement(target, options = {}) {\n  const {\n    handleOutside = true,\n    window = defaultWindow\n  } = options;\n  const type = options.type || \"page\";\n  const { x, y, sourceType } = useMouse(options);\n  const targetRef = shallowRef(target != null ? target : window == null ? void 0 : window.document.body);\n  const elementX = shallowRef(0);\n  const elementY = shallowRef(0);\n  const elementPositionX = shallowRef(0);\n  const elementPositionY = shallowRef(0);\n  const elementHeight = shallowRef(0);\n  const elementWidth = shallowRef(0);\n  const isOutside = shallowRef(true);\n  let stop = () => {\n  };\n  if (window) {\n    stop = watch(\n      [targetRef, x, y],\n      () => {\n        const el = unrefElement(targetRef);\n        if (!el || !(el instanceof Element))\n          return;\n        const {\n          left,\n          top,\n          width,\n          height\n        } = el.getBoundingClientRect();\n        elementPositionX.value = left + (type === \"page\" ? window.pageXOffset : 0);\n        elementPositionY.value = top + (type === \"page\" ? window.pageYOffset : 0);\n        elementHeight.value = height;\n        elementWidth.value = width;\n        const elX = x.value - elementPositionX.value;\n        const elY = y.value - elementPositionY.value;\n        isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;\n        if (handleOutside || !isOutside.value) {\n          elementX.value = elX;\n          elementY.value = elY;\n        }\n      },\n      { immediate: true }\n    );\n    useEventListener(\n      document,\n      \"mouseleave\",\n      () => isOutside.value = true,\n      { passive: true }\n    );\n  }\n  return {\n    x,\n    y,\n    sourceType,\n    elementX,\n    elementY,\n    elementPositionX,\n    elementPositionY,\n    elementHeight,\n    elementWidth,\n    isOutside,\n    stop\n  };\n}\n\nfunction useMousePressed(options = {}) {\n  const {\n    touch = true,\n    drag = true,\n    capture = false,\n    initialValue = false,\n    window = defaultWindow\n  } = options;\n  const pressed = shallowRef(initialValue);\n  const sourceType = shallowRef(null);\n  if (!window) {\n    return {\n      pressed,\n      sourceType\n    };\n  }\n  const onPressed = (srcType) => (event) => {\n    var _a;\n    pressed.value = true;\n    sourceType.value = srcType;\n    (_a = options.onPressed) == null ? void 0 : _a.call(options, event);\n  };\n  const onReleased = (event) => {\n    var _a;\n    pressed.value = false;\n    sourceType.value = null;\n    (_a = options.onReleased) == null ? void 0 : _a.call(options, event);\n  };\n  const target = computed(() => unrefElement(options.target) || window);\n  const listenerOptions = { passive: true, capture };\n  useEventListener(target, \"mousedown\", onPressed(\"mouse\"), listenerOptions);\n  useEventListener(window, \"mouseleave\", onReleased, listenerOptions);\n  useEventListener(window, \"mouseup\", onReleased, listenerOptions);\n  if (drag) {\n    useEventListener(target, \"dragstart\", onPressed(\"mouse\"), listenerOptions);\n    useEventListener(window, \"drop\", onReleased, listenerOptions);\n    useEventListener(window, \"dragend\", onReleased, listenerOptions);\n  }\n  if (touch) {\n    useEventListener(target, \"touchstart\", onPressed(\"touch\"), listenerOptions);\n    useEventListener(window, \"touchend\", onReleased, listenerOptions);\n    useEventListener(window, \"touchcancel\", onReleased, listenerOptions);\n  }\n  return {\n    pressed,\n    sourceType\n  };\n}\n\nfunction useNavigatorLanguage(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"language\" in navigator);\n  const language = shallowRef(navigator == null ? void 0 : navigator.language);\n  useEventListener(window, \"languagechange\", () => {\n    if (navigator)\n      language.value = navigator.language;\n  }, { passive: true });\n  return {\n    isSupported,\n    language\n  };\n}\n\nfunction useNetwork(options = {}) {\n  const { window = defaultWindow } = options;\n  const navigator = window == null ? void 0 : window.navigator;\n  const isSupported = useSupported(() => navigator && \"connection\" in navigator);\n  const isOnline = shallowRef(true);\n  const saveData = shallowRef(false);\n  const offlineAt = shallowRef(void 0);\n  const onlineAt = shallowRef(void 0);\n  const downlink = shallowRef(void 0);\n  const downlinkMax = shallowRef(void 0);\n  const rtt = shallowRef(void 0);\n  const effectiveType = shallowRef(void 0);\n  const type = shallowRef(\"unknown\");\n  const connection = isSupported.value && navigator.connection;\n  function updateNetworkInformation() {\n    if (!navigator)\n      return;\n    isOnline.value = navigator.onLine;\n    offlineAt.value = isOnline.value ? void 0 : Date.now();\n    onlineAt.value = isOnline.value ? Date.now() : void 0;\n    if (connection) {\n      downlink.value = connection.downlink;\n      downlinkMax.value = connection.downlinkMax;\n      effectiveType.value = connection.effectiveType;\n      rtt.value = connection.rtt;\n      saveData.value = connection.saveData;\n      type.value = connection.type;\n    }\n  }\n  const listenerOptions = { passive: true };\n  if (window) {\n    useEventListener(window, \"offline\", () => {\n      isOnline.value = false;\n      offlineAt.value = Date.now();\n    }, listenerOptions);\n    useEventListener(window, \"online\", () => {\n      isOnline.value = true;\n      onlineAt.value = Date.now();\n    }, listenerOptions);\n  }\n  if (connection)\n    useEventListener(connection, \"change\", updateNetworkInformation, listenerOptions);\n  updateNetworkInformation();\n  return {\n    isSupported,\n    isOnline: readonly(isOnline),\n    saveData: readonly(saveData),\n    offlineAt: readonly(offlineAt),\n    onlineAt: readonly(onlineAt),\n    downlink: readonly(downlink),\n    downlinkMax: readonly(downlinkMax),\n    effectiveType: readonly(effectiveType),\n    rtt: readonly(rtt),\n    type: readonly(type)\n  };\n}\n\nfunction useNow(options = {}) {\n  const {\n    controls: exposeControls = false,\n    interval = \"requestAnimationFrame\"\n  } = options;\n  const now = ref(/* @__PURE__ */ new Date());\n  const update = () => now.value = /* @__PURE__ */ new Date();\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(update, { immediate: true }) : useIntervalFn(update, interval, { immediate: true });\n  if (exposeControls) {\n    return {\n      now,\n      ...controls\n    };\n  } else {\n    return now;\n  }\n}\n\nfunction useObjectUrl(object) {\n  const url = shallowRef();\n  const release = () => {\n    if (url.value)\n      URL.revokeObjectURL(url.value);\n    url.value = void 0;\n  };\n  watch(\n    () => toValue(object),\n    (newObject) => {\n      release();\n      if (newObject)\n        url.value = URL.createObjectURL(newObject);\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(release);\n  return readonly(url);\n}\n\nfunction useClamp(value, min, max) {\n  if (typeof value === \"function\" || isReadonly(value))\n    return computed(() => clamp(toValue(value), toValue(min), toValue(max)));\n  const _value = ref(value);\n  return computed({\n    get() {\n      return _value.value = clamp(_value.value, toValue(min), toValue(max));\n    },\n    set(value2) {\n      _value.value = clamp(value2, toValue(min), toValue(max));\n    }\n  });\n}\n\nfunction useOffsetPagination(options) {\n  const {\n    total = Number.POSITIVE_INFINITY,\n    pageSize = 10,\n    page = 1,\n    onPageChange = noop,\n    onPageSizeChange = noop,\n    onPageCountChange = noop\n  } = options;\n  const currentPageSize = useClamp(pageSize, 1, Number.POSITIVE_INFINITY);\n  const pageCount = computed(() => Math.max(\n    1,\n    Math.ceil(toValue(total) / toValue(currentPageSize))\n  ));\n  const currentPage = useClamp(page, 1, pageCount);\n  const isFirstPage = computed(() => currentPage.value === 1);\n  const isLastPage = computed(() => currentPage.value === pageCount.value);\n  if (isRef(page)) {\n    syncRef(page, currentPage, {\n      direction: isReadonly(page) ? \"ltr\" : \"both\"\n    });\n  }\n  if (isRef(pageSize)) {\n    syncRef(pageSize, currentPageSize, {\n      direction: isReadonly(pageSize) ? \"ltr\" : \"both\"\n    });\n  }\n  function prev() {\n    currentPage.value--;\n  }\n  function next() {\n    currentPage.value++;\n  }\n  const returnValue = {\n    currentPage,\n    currentPageSize,\n    pageCount,\n    isFirstPage,\n    isLastPage,\n    prev,\n    next\n  };\n  watch(currentPage, () => {\n    onPageChange(reactive(returnValue));\n  });\n  watch(currentPageSize, () => {\n    onPageSizeChange(reactive(returnValue));\n  });\n  watch(pageCount, () => {\n    onPageCountChange(reactive(returnValue));\n  });\n  return returnValue;\n}\n\nfunction useOnline(options = {}) {\n  const { isOnline } = useNetwork(options);\n  return isOnline;\n}\n\nfunction usePageLeave(options = {}) {\n  const { window = defaultWindow } = options;\n  const isLeft = shallowRef(false);\n  const handler = (event) => {\n    if (!window)\n      return;\n    event = event || window.event;\n    const from = event.relatedTarget || event.toElement;\n    isLeft.value = !from;\n  };\n  if (window) {\n    const listenerOptions = { passive: true };\n    useEventListener(window, \"mouseout\", handler, listenerOptions);\n    useEventListener(window.document, \"mouseleave\", handler, listenerOptions);\n    useEventListener(window.document, \"mouseenter\", handler, listenerOptions);\n  }\n  return isLeft;\n}\n\nfunction useScreenOrientation(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const isSupported = useSupported(() => window && \"screen\" in window && \"orientation\" in window.screen);\n  const screenOrientation = isSupported.value ? window.screen.orientation : {};\n  const orientation = ref(screenOrientation.type);\n  const angle = shallowRef(screenOrientation.angle || 0);\n  if (isSupported.value) {\n    useEventListener(window, \"orientationchange\", () => {\n      orientation.value = screenOrientation.type;\n      angle.value = screenOrientation.angle;\n    }, { passive: true });\n  }\n  const lockOrientation = (type) => {\n    if (isSupported.value && typeof screenOrientation.lock === \"function\")\n      return screenOrientation.lock(type);\n    return Promise.reject(new Error(\"Not supported\"));\n  };\n  const unlockOrientation = () => {\n    if (isSupported.value && typeof screenOrientation.unlock === \"function\")\n      screenOrientation.unlock();\n  };\n  return {\n    isSupported,\n    orientation,\n    angle,\n    lockOrientation,\n    unlockOrientation\n  };\n}\n\nfunction useParallax(target, options = {}) {\n  const {\n    deviceOrientationTiltAdjust = (i) => i,\n    deviceOrientationRollAdjust = (i) => i,\n    mouseTiltAdjust = (i) => i,\n    mouseRollAdjust = (i) => i,\n    window = defaultWindow\n  } = options;\n  const orientation = reactive(useDeviceOrientation({ window }));\n  const screenOrientation = reactive(useScreenOrientation({ window }));\n  const {\n    elementX: x,\n    elementY: y,\n    elementWidth: width,\n    elementHeight: height\n  } = useMouseInElement(target, { handleOutside: false, window });\n  const source = computed(() => {\n    if (orientation.isSupported && (orientation.alpha != null && orientation.alpha !== 0 || orientation.gamma != null && orientation.gamma !== 0)) {\n      return \"deviceOrientation\";\n    }\n    return \"mouse\";\n  });\n  const roll = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      let value;\n      switch (screenOrientation.orientation) {\n        case \"landscape-primary\":\n          value = orientation.gamma / 90;\n          break;\n        case \"landscape-secondary\":\n          value = -orientation.gamma / 90;\n          break;\n        case \"portrait-primary\":\n          value = -orientation.beta / 90;\n          break;\n        case \"portrait-secondary\":\n          value = orientation.beta / 90;\n          break;\n        default:\n          value = -orientation.beta / 90;\n      }\n      return deviceOrientationRollAdjust(value);\n    } else {\n      const value = -(y.value - height.value / 2) / height.value;\n      return mouseRollAdjust(value);\n    }\n  });\n  const tilt = computed(() => {\n    if (source.value === \"deviceOrientation\") {\n      let value;\n      switch (screenOrientation.orientation) {\n        case \"landscape-primary\":\n          value = orientation.beta / 90;\n          break;\n        case \"landscape-secondary\":\n          value = -orientation.beta / 90;\n          break;\n        case \"portrait-primary\":\n          value = orientation.gamma / 90;\n          break;\n        case \"portrait-secondary\":\n          value = -orientation.gamma / 90;\n          break;\n        default:\n          value = orientation.gamma / 90;\n      }\n      return deviceOrientationTiltAdjust(value);\n    } else {\n      const value = (x.value - width.value / 2) / width.value;\n      return mouseTiltAdjust(value);\n    }\n  });\n  return { roll, tilt, source };\n}\n\nfunction useParentElement(element = useCurrentElement()) {\n  const parentElement = shallowRef();\n  const update = () => {\n    const el = unrefElement(element);\n    if (el)\n      parentElement.value = el.parentElement;\n  };\n  tryOnMounted(update);\n  watch(() => toValue(element), update);\n  return parentElement;\n}\n\nfunction usePerformanceObserver(options, callback) {\n  const {\n    window = defaultWindow,\n    immediate = true,\n    ...performanceOptions\n  } = options;\n  const isSupported = useSupported(() => window && \"PerformanceObserver\" in window);\n  let observer;\n  const stop = () => {\n    observer == null ? void 0 : observer.disconnect();\n  };\n  const start = () => {\n    if (isSupported.value) {\n      stop();\n      observer = new PerformanceObserver(callback);\n      observer.observe(performanceOptions);\n    }\n  };\n  tryOnScopeDispose(stop);\n  if (immediate)\n    start();\n  return {\n    isSupported,\n    start,\n    stop\n  };\n}\n\nconst defaultState = {\n  x: 0,\n  y: 0,\n  pointerId: 0,\n  pressure: 0,\n  tiltX: 0,\n  tiltY: 0,\n  width: 0,\n  height: 0,\n  twist: 0,\n  pointerType: null\n};\nconst keys = /* @__PURE__ */ Object.keys(defaultState);\nfunction usePointer(options = {}) {\n  const {\n    target = defaultWindow\n  } = options;\n  const isInside = shallowRef(false);\n  const state = ref(options.initialValue || {});\n  Object.assign(state.value, defaultState, state.value);\n  const handler = (event) => {\n    isInside.value = true;\n    if (options.pointerTypes && !options.pointerTypes.includes(event.pointerType))\n      return;\n    state.value = objectPick(event, keys, false);\n  };\n  if (target) {\n    const listenerOptions = { passive: true };\n    useEventListener(target, [\"pointerdown\", \"pointermove\", \"pointerup\"], handler, listenerOptions);\n    useEventListener(target, \"pointerleave\", () => isInside.value = false, listenerOptions);\n  }\n  return {\n    ...toRefs(state),\n    isInside\n  };\n}\n\nfunction usePointerLock(target, options = {}) {\n  const { document = defaultDocument } = options;\n  const isSupported = useSupported(() => document && \"pointerLockElement\" in document);\n  const element = shallowRef();\n  const triggerElement = shallowRef();\n  let targetElement;\n  if (isSupported.value) {\n    const listenerOptions = { passive: true };\n    useEventListener(document, \"pointerlockchange\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        element.value = document.pointerLockElement;\n        if (!element.value)\n          targetElement = triggerElement.value = null;\n      }\n    }, listenerOptions);\n    useEventListener(document, \"pointerlockerror\", () => {\n      var _a;\n      const currentElement = (_a = document.pointerLockElement) != null ? _a : element.value;\n      if (targetElement && currentElement === targetElement) {\n        const action = document.pointerLockElement ? \"release\" : \"acquire\";\n        throw new Error(`Failed to ${action} pointer lock.`);\n      }\n    }, listenerOptions);\n  }\n  async function lock(e) {\n    var _a;\n    if (!isSupported.value)\n      throw new Error(\"Pointer Lock API is not supported by your browser.\");\n    triggerElement.value = e instanceof Event ? e.currentTarget : null;\n    targetElement = e instanceof Event ? (_a = unrefElement(target)) != null ? _a : triggerElement.value : unrefElement(e);\n    if (!targetElement)\n      throw new Error(\"Target element undefined.\");\n    targetElement.requestPointerLock();\n    return await until(element).toBe(targetElement);\n  }\n  async function unlock() {\n    if (!element.value)\n      return false;\n    document.exitPointerLock();\n    await until(element).toBeNull();\n    return true;\n  }\n  return {\n    isSupported,\n    element,\n    triggerElement,\n    lock,\n    unlock\n  };\n}\n\nfunction usePointerSwipe(target, options = {}) {\n  const targetRef = toRef(target);\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    disableTextSelect = false\n  } = options;\n  const posStart = reactive({ x: 0, y: 0 });\n  const updatePosStart = (x, y) => {\n    posStart.x = x;\n    posStart.y = y;\n  };\n  const posEnd = reactive({ x: 0, y: 0 });\n  const updatePosEnd = (x, y) => {\n    posEnd.x = x;\n    posEnd.y = y;\n  };\n  const distanceX = computed(() => posStart.x - posEnd.x);\n  const distanceY = computed(() => posStart.y - posEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(distanceX.value), abs(distanceY.value)) >= threshold);\n  const isSwiping = shallowRef(false);\n  const isPointerDown = shallowRef(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(distanceX.value) > abs(distanceY.value)) {\n      return distanceX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return distanceY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const eventIsAllowed = (e) => {\n    var _a, _b, _c;\n    const isReleasingButton = e.buttons === 0;\n    const isPrimaryButton = e.buttons === 1;\n    return (_c = (_b = (_a = options.pointerTypes) == null ? void 0 : _a.includes(e.pointerType)) != null ? _b : isReleasingButton || isPrimaryButton) != null ? _c : true;\n  };\n  const listenerOptions = { passive: true };\n  const stops = [\n    useEventListener(target, \"pointerdown\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      isPointerDown.value = true;\n      const eventTarget = e.target;\n      eventTarget == null ? void 0 : eventTarget.setPointerCapture(e.pointerId);\n      const { clientX: x, clientY: y } = e;\n      updatePosStart(x, y);\n      updatePosEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"pointermove\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (!isPointerDown.value)\n        return;\n      const { clientX: x, clientY: y } = e;\n      updatePosEnd(x, y);\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, \"pointerup\", (e) => {\n      if (!eventIsAllowed(e))\n        return;\n      if (isSwiping.value)\n        onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n      isPointerDown.value = false;\n      isSwiping.value = false;\n    }, listenerOptions)\n  ];\n  tryOnMounted(() => {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    (_b = (_a = targetRef.value) == null ? void 0 : _a.style) == null ? void 0 : _b.setProperty(\"touch-action\", \"none\");\n    if (disableTextSelect) {\n      (_d = (_c = targetRef.value) == null ? void 0 : _c.style) == null ? void 0 : _d.setProperty(\"-webkit-user-select\", \"none\");\n      (_f = (_e = targetRef.value) == null ? void 0 : _e.style) == null ? void 0 : _f.setProperty(\"-ms-user-select\", \"none\");\n      (_h = (_g = targetRef.value) == null ? void 0 : _g.style) == null ? void 0 : _h.setProperty(\"user-select\", \"none\");\n    }\n  });\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping: readonly(isSwiping),\n    direction: readonly(direction),\n    posStart: readonly(posStart),\n    posEnd: readonly(posEnd),\n    distanceX,\n    distanceY,\n    stop\n  };\n}\n\nfunction usePreferredColorScheme(options) {\n  const isLight = useMediaQuery(\"(prefers-color-scheme: light)\", options);\n  const isDark = useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n  return computed(() => {\n    if (isDark.value)\n      return \"dark\";\n    if (isLight.value)\n      return \"light\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredContrast(options) {\n  const isMore = useMediaQuery(\"(prefers-contrast: more)\", options);\n  const isLess = useMediaQuery(\"(prefers-contrast: less)\", options);\n  const isCustom = useMediaQuery(\"(prefers-contrast: custom)\", options);\n  return computed(() => {\n    if (isMore.value)\n      return \"more\";\n    if (isLess.value)\n      return \"less\";\n    if (isCustom.value)\n      return \"custom\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredLanguages(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return ref([\"en\"]);\n  const navigator = window.navigator;\n  const value = ref(navigator.languages);\n  useEventListener(window, \"languagechange\", () => {\n    value.value = navigator.languages;\n  }, { passive: true });\n  return value;\n}\n\nfunction usePreferredReducedMotion(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePreferredReducedTransparency(options) {\n  const isReduced = useMediaQuery(\"(prefers-reduced-transparency: reduce)\", options);\n  return computed(() => {\n    if (isReduced.value)\n      return \"reduce\";\n    return \"no-preference\";\n  });\n}\n\nfunction usePrevious(value, initialValue) {\n  const previous = shallowRef(initialValue);\n  watch(\n    toRef(value),\n    (_, oldValue) => {\n      previous.value = oldValue;\n    },\n    { flush: \"sync\" }\n  );\n  return readonly(previous);\n}\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = shallowRef(\"\");\n  const right = shallowRef(\"\");\n  const bottom = shallowRef(\"\");\n  const left = shallowRef(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update), { passive: true });\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nfunction useScriptTag(src, onLoaded = noop, options = {}) {\n  const {\n    immediate = true,\n    manual = false,\n    type = \"text/javascript\",\n    async = true,\n    crossOrigin,\n    referrerPolicy,\n    noModule,\n    defer,\n    document = defaultDocument,\n    attrs = {}\n  } = options;\n  const scriptTag = shallowRef(null);\n  let _promise = null;\n  const loadScript = (waitForScriptLoad) => new Promise((resolve, reject) => {\n    const resolveWithElement = (el2) => {\n      scriptTag.value = el2;\n      resolve(el2);\n      return el2;\n    };\n    if (!document) {\n      resolve(false);\n      return;\n    }\n    let shouldAppend = false;\n    let el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (!el) {\n      el = document.createElement(\"script\");\n      el.type = type;\n      el.async = async;\n      el.src = toValue(src);\n      if (defer)\n        el.defer = defer;\n      if (crossOrigin)\n        el.crossOrigin = crossOrigin;\n      if (noModule)\n        el.noModule = noModule;\n      if (referrerPolicy)\n        el.referrerPolicy = referrerPolicy;\n      Object.entries(attrs).forEach(([name, value]) => el == null ? void 0 : el.setAttribute(name, value));\n      shouldAppend = true;\n    } else if (el.hasAttribute(\"data-loaded\")) {\n      resolveWithElement(el);\n    }\n    const listenerOptions = {\n      passive: true\n    };\n    useEventListener(el, \"error\", (event) => reject(event), listenerOptions);\n    useEventListener(el, \"abort\", (event) => reject(event), listenerOptions);\n    useEventListener(el, \"load\", () => {\n      el.setAttribute(\"data-loaded\", \"true\");\n      onLoaded(el);\n      resolveWithElement(el);\n    }, listenerOptions);\n    if (shouldAppend)\n      el = document.head.appendChild(el);\n    if (!waitForScriptLoad)\n      resolveWithElement(el);\n  });\n  const load = (waitForScriptLoad = true) => {\n    if (!_promise)\n      _promise = loadScript(waitForScriptLoad);\n    return _promise;\n  };\n  const unload = () => {\n    if (!document)\n      return;\n    _promise = null;\n    if (scriptTag.value)\n      scriptTag.value = null;\n    const el = document.querySelector(`script[src=\"${toValue(src)}\"]`);\n    if (el)\n      document.head.removeChild(el);\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnUnmounted(unload);\n  return { scriptTag, load, unload };\n}\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientWidth < ele.scrollWidth || style.overflowY === \"auto\" && ele.clientHeight < ele.scrollHeight) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nconst elInitialOverflow = /* @__PURE__ */ new WeakMap();\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = shallowRef(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow = \"\";\n  watch(toRef(element), (el) => {\n    const target = resolveElement(toValue(el));\n    if (target) {\n      const ele = target;\n      if (!elInitialOverflow.get(ele))\n        elInitialOverflow.set(ele, ele.style.overflow);\n      if (ele.style.overflow !== \"hidden\")\n        initialOverflow = ele.style.overflow;\n      if (ele.style.overflow === \"hidden\")\n        return isLocked.value = true;\n      if (isLocked.value)\n        return ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(\n        el,\n        \"touchmove\",\n        (e) => {\n          preventDefault(e);\n        },\n        { passive: false }\n      );\n    }\n    el.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || !isLocked.value)\n      return;\n    if (isIOS)\n      stopTouchMoveListener == null ? void 0 : stopTouchMoveListener();\n    el.style.overflow = initialOverflow;\n    elInitialOverflow.delete(el);\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else unlock();\n    }\n  });\n}\n\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { window = defaultWindow } = options;\n  return useStorage(key, initialValue, window == null ? void 0 : window.sessionStorage, options);\n}\n\nfunction useShare(shareOptions = {}, options = {}) {\n  const { navigator = defaultNavigator } = options;\n  const _navigator = navigator;\n  const isSupported = useSupported(() => _navigator && \"canShare\" in _navigator);\n  const share = async (overrideOptions = {}) => {\n    if (isSupported.value) {\n      const data = {\n        ...toValue(shareOptions),\n        ...toValue(overrideOptions)\n      };\n      let granted = true;\n      if (data.files && _navigator.canShare)\n        granted = _navigator.canShare({ files: data.files });\n      if (granted)\n        return _navigator.share(data);\n    }\n  };\n  return {\n    isSupported,\n    share\n  };\n}\n\nconst defaultSortFn = (source, compareFn) => source.sort(compareFn);\nconst defaultCompare = (a, b) => a - b;\nfunction useSorted(...args) {\n  var _a, _b, _c, _d;\n  const [source] = args;\n  let compareFn = defaultCompare;\n  let options = {};\n  if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      options = args[1];\n      compareFn = (_a = options.compareFn) != null ? _a : defaultCompare;\n    } else {\n      compareFn = (_b = args[1]) != null ? _b : defaultCompare;\n    }\n  } else if (args.length > 2) {\n    compareFn = (_c = args[1]) != null ? _c : defaultCompare;\n    options = (_d = args[2]) != null ? _d : {};\n  }\n  const {\n    dirty = false,\n    sortFn = defaultSortFn\n  } = options;\n  if (!dirty)\n    return computed(() => sortFn([...toValue(source)], compareFn));\n  watchEffect(() => {\n    const result = sortFn(toValue(source), compareFn);\n    if (isRef(source))\n      source.value = result;\n    else\n      source.splice(0, source.length, ...result);\n  });\n  return source;\n}\n\nfunction useSpeechRecognition(options = {}) {\n  const {\n    interimResults = true,\n    continuous = true,\n    maxAlternatives = 1,\n    window = defaultWindow\n  } = options;\n  const lang = toRef(options.lang || \"en-US\");\n  const isListening = shallowRef(false);\n  const isFinal = shallowRef(false);\n  const result = shallowRef(\"\");\n  const error = shallowRef(void 0);\n  let recognition;\n  const start = () => {\n    isListening.value = true;\n  };\n  const stop = () => {\n    isListening.value = false;\n  };\n  const toggle = (value = !isListening.value) => {\n    if (value) {\n      start();\n    } else {\n      stop();\n    }\n  };\n  const SpeechRecognition = window && (window.SpeechRecognition || window.webkitSpeechRecognition);\n  const isSupported = useSupported(() => SpeechRecognition);\n  if (isSupported.value) {\n    recognition = new SpeechRecognition();\n    recognition.continuous = continuous;\n    recognition.interimResults = interimResults;\n    recognition.lang = toValue(lang);\n    recognition.maxAlternatives = maxAlternatives;\n    recognition.onstart = () => {\n      isListening.value = true;\n      isFinal.value = false;\n    };\n    watch(lang, (lang2) => {\n      if (recognition && !isListening.value)\n        recognition.lang = lang2;\n    });\n    recognition.onresult = (event) => {\n      const currentResult = event.results[event.resultIndex];\n      const { transcript } = currentResult[0];\n      isFinal.value = currentResult.isFinal;\n      result.value = transcript;\n      error.value = void 0;\n    };\n    recognition.onerror = (event) => {\n      error.value = event;\n    };\n    recognition.onend = () => {\n      isListening.value = false;\n      recognition.lang = toValue(lang);\n    };\n    watch(isListening, (newValue, oldValue) => {\n      if (newValue === oldValue)\n        return;\n      if (newValue)\n        recognition.start();\n      else\n        recognition.stop();\n    });\n  }\n  tryOnScopeDispose(() => {\n    stop();\n  });\n  return {\n    isSupported,\n    isListening,\n    isFinal,\n    recognition,\n    result,\n    error,\n    toggle,\n    start,\n    stop\n  };\n}\n\nfunction useSpeechSynthesis(text, options = {}) {\n  const {\n    pitch = 1,\n    rate = 1,\n    volume = 1,\n    window = defaultWindow\n  } = options;\n  const synth = window && window.speechSynthesis;\n  const isSupported = useSupported(() => synth);\n  const isPlaying = shallowRef(false);\n  const status = shallowRef(\"init\");\n  const spokenText = toRef(text || \"\");\n  const lang = toRef(options.lang || \"en-US\");\n  const error = shallowRef(void 0);\n  const toggle = (value = !isPlaying.value) => {\n    isPlaying.value = value;\n  };\n  const bindEventsForUtterance = (utterance2) => {\n    utterance2.lang = toValue(lang);\n    utterance2.voice = toValue(options.voice) || null;\n    utterance2.pitch = toValue(pitch);\n    utterance2.rate = toValue(rate);\n    utterance2.volume = volume;\n    utterance2.onstart = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onpause = () => {\n      isPlaying.value = false;\n      status.value = \"pause\";\n    };\n    utterance2.onresume = () => {\n      isPlaying.value = true;\n      status.value = \"play\";\n    };\n    utterance2.onend = () => {\n      isPlaying.value = false;\n      status.value = \"end\";\n    };\n    utterance2.onerror = (event) => {\n      error.value = event;\n    };\n  };\n  const utterance = computed(() => {\n    isPlaying.value = false;\n    status.value = \"init\";\n    const newUtterance = new SpeechSynthesisUtterance(spokenText.value);\n    bindEventsForUtterance(newUtterance);\n    return newUtterance;\n  });\n  const speak = () => {\n    synth.cancel();\n    if (utterance)\n      synth.speak(utterance.value);\n  };\n  const stop = () => {\n    synth.cancel();\n    isPlaying.value = false;\n  };\n  if (isSupported.value) {\n    bindEventsForUtterance(utterance.value);\n    watch(lang, (lang2) => {\n      if (utterance.value && !isPlaying.value)\n        utterance.value.lang = lang2;\n    });\n    if (options.voice) {\n      watch(options.voice, () => {\n        synth.cancel();\n      });\n    }\n    watch(isPlaying, () => {\n      if (isPlaying.value)\n        synth.resume();\n      else\n        synth.pause();\n    });\n  }\n  tryOnScopeDispose(() => {\n    isPlaying.value = false;\n  });\n  return {\n    isSupported,\n    isPlaying,\n    status,\n    utterance,\n    error,\n    stop,\n    toggle,\n    speak\n  };\n}\n\nfunction useStepper(steps, initialStep) {\n  const stepsRef = ref(steps);\n  const stepNames = computed(() => Array.isArray(stepsRef.value) ? stepsRef.value : Object.keys(stepsRef.value));\n  const index = ref(stepNames.value.indexOf(initialStep != null ? initialStep : stepNames.value[0]));\n  const current = computed(() => at(index.value));\n  const isFirst = computed(() => index.value === 0);\n  const isLast = computed(() => index.value === stepNames.value.length - 1);\n  const next = computed(() => stepNames.value[index.value + 1]);\n  const previous = computed(() => stepNames.value[index.value - 1]);\n  function at(index2) {\n    if (Array.isArray(stepsRef.value))\n      return stepsRef.value[index2];\n    return stepsRef.value[stepNames.value[index2]];\n  }\n  function get(step) {\n    if (!stepNames.value.includes(step))\n      return;\n    return at(stepNames.value.indexOf(step));\n  }\n  function goTo(step) {\n    if (stepNames.value.includes(step))\n      index.value = stepNames.value.indexOf(step);\n  }\n  function goToNext() {\n    if (isLast.value)\n      return;\n    index.value++;\n  }\n  function goToPrevious() {\n    if (isFirst.value)\n      return;\n    index.value--;\n  }\n  function goBackTo(step) {\n    if (isAfter(step))\n      goTo(step);\n  }\n  function isNext(step) {\n    return stepNames.value.indexOf(step) === index.value + 1;\n  }\n  function isPrevious(step) {\n    return stepNames.value.indexOf(step) === index.value - 1;\n  }\n  function isCurrent(step) {\n    return stepNames.value.indexOf(step) === index.value;\n  }\n  function isBefore(step) {\n    return index.value < stepNames.value.indexOf(step);\n  }\n  function isAfter(step) {\n    return index.value > stepNames.value.indexOf(step);\n  }\n  return {\n    steps: stepsRef,\n    stepNames,\n    index,\n    current,\n    next,\n    previous,\n    isFirst,\n    isLast,\n    at,\n    get,\n    goTo,\n    goToNext,\n    goToPrevious,\n    goBackTo,\n    isNext,\n    isPrevious,\n    isCurrent,\n    isBefore,\n    isAfter\n  };\n}\n\nfunction useStorageAsync(key, initialValue, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const rawInit = toValue(initialValue);\n  const type = guessSerializerType(rawInit);\n  const data = (shallow ? shallowRef : ref)(toValue(initialValue));\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorageAsync\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  async function read(event) {\n    if (!storage || event && event.key !== key)\n      return;\n    try {\n      const rawValue = event ? event.newValue : await storage.getItem(key);\n      if (rawValue == null) {\n        data.value = rawInit;\n        if (writeDefaults && rawInit !== null)\n          await storage.setItem(key, await serializer.write(rawInit));\n      } else if (mergeDefaults) {\n        const value = await serializer.read(rawValue);\n        if (typeof mergeDefaults === \"function\")\n          data.value = mergeDefaults(value, rawInit);\n        else if (type === \"object\" && !Array.isArray(value))\n          data.value = { ...rawInit, ...value };\n        else data.value = value;\n      } else {\n        data.value = await serializer.read(rawValue);\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  read();\n  if (window && listenToStorageChanges)\n    useEventListener(window, \"storage\", (e) => Promise.resolve().then(() => read(e)), { passive: true });\n  if (storage) {\n    watchWithFilter(\n      data,\n      async () => {\n        try {\n          if (data.value == null)\n            await storage.removeItem(key);\n          else\n            await storage.setItem(key, await serializer.write(data.value));\n        } catch (e) {\n          onError(e);\n        }\n      },\n      {\n        flush,\n        deep,\n        eventFilter\n      }\n    );\n  }\n  return data;\n}\n\nlet _id = 0;\nfunction useStyleTag(css, options = {}) {\n  const isLoaded = shallowRef(false);\n  const {\n    document = defaultDocument,\n    immediate = true,\n    manual = false,\n    id = `vueuse_styletag_${++_id}`\n  } = options;\n  const cssRef = shallowRef(css);\n  let stop = () => {\n  };\n  const load = () => {\n    if (!document)\n      return;\n    const el = document.getElementById(id) || document.createElement(\"style\");\n    if (!el.isConnected) {\n      el.id = id;\n      if (options.media)\n        el.media = options.media;\n      document.head.appendChild(el);\n    }\n    if (isLoaded.value)\n      return;\n    stop = watch(\n      cssRef,\n      (value) => {\n        el.textContent = value;\n      },\n      { immediate: true }\n    );\n    isLoaded.value = true;\n  };\n  const unload = () => {\n    if (!document || !isLoaded.value)\n      return;\n    stop();\n    document.head.removeChild(document.getElementById(id));\n    isLoaded.value = false;\n  };\n  if (immediate && !manual)\n    tryOnMounted(load);\n  if (!manual)\n    tryOnScopeDispose(unload);\n  return {\n    id,\n    css: cssRef,\n    unload,\n    load,\n    isLoaded: readonly(isLoaded)\n  };\n}\n\nfunction useSwipe(target, options = {}) {\n  const {\n    threshold = 50,\n    onSwipe,\n    onSwipeEnd,\n    onSwipeStart,\n    passive = true\n  } = options;\n  const coordsStart = reactive({ x: 0, y: 0 });\n  const coordsEnd = reactive({ x: 0, y: 0 });\n  const diffX = computed(() => coordsStart.x - coordsEnd.x);\n  const diffY = computed(() => coordsStart.y - coordsEnd.y);\n  const { max, abs } = Math;\n  const isThresholdExceeded = computed(() => max(abs(diffX.value), abs(diffY.value)) >= threshold);\n  const isSwiping = shallowRef(false);\n  const direction = computed(() => {\n    if (!isThresholdExceeded.value)\n      return \"none\";\n    if (abs(diffX.value) > abs(diffY.value)) {\n      return diffX.value > 0 ? \"left\" : \"right\";\n    } else {\n      return diffY.value > 0 ? \"up\" : \"down\";\n    }\n  });\n  const getTouchEventCoords = (e) => [e.touches[0].clientX, e.touches[0].clientY];\n  const updateCoordsStart = (x, y) => {\n    coordsStart.x = x;\n    coordsStart.y = y;\n  };\n  const updateCoordsEnd = (x, y) => {\n    coordsEnd.x = x;\n    coordsEnd.y = y;\n  };\n  const listenerOptions = { passive, capture: !passive };\n  const onTouchEnd = (e) => {\n    if (isSwiping.value)\n      onSwipeEnd == null ? void 0 : onSwipeEnd(e, direction.value);\n    isSwiping.value = false;\n  };\n  const stops = [\n    useEventListener(target, \"touchstart\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsStart(x, y);\n      updateCoordsEnd(x, y);\n      onSwipeStart == null ? void 0 : onSwipeStart(e);\n    }, listenerOptions),\n    useEventListener(target, \"touchmove\", (e) => {\n      if (e.touches.length !== 1)\n        return;\n      const [x, y] = getTouchEventCoords(e);\n      updateCoordsEnd(x, y);\n      if (listenerOptions.capture && !listenerOptions.passive && Math.abs(diffX.value) > Math.abs(diffY.value))\n        e.preventDefault();\n      if (!isSwiping.value && isThresholdExceeded.value)\n        isSwiping.value = true;\n      if (isSwiping.value)\n        onSwipe == null ? void 0 : onSwipe(e);\n    }, listenerOptions),\n    useEventListener(target, [\"touchend\", \"touchcancel\"], onTouchEnd, listenerOptions)\n  ];\n  const stop = () => stops.forEach((s) => s());\n  return {\n    isSwiping,\n    direction,\n    coordsStart,\n    coordsEnd,\n    lengthX: diffX,\n    lengthY: diffY,\n    stop,\n    // TODO: Remove in the next major version\n    isPassiveEventSupported: true\n  };\n}\n\nfunction useTemplateRefsList() {\n  const refs = ref([]);\n  refs.value.set = (el) => {\n    if (el)\n      refs.value.push(el);\n  };\n  onBeforeUpdate(() => {\n    refs.value.length = 0;\n  });\n  return refs;\n}\n\nfunction useTextDirection(options = {}) {\n  const {\n    document = defaultDocument,\n    selector = \"html\",\n    observe = false,\n    initialValue = \"ltr\"\n  } = options;\n  function getValue() {\n    var _a, _b;\n    return (_b = (_a = document == null ? void 0 : document.querySelector(selector)) == null ? void 0 : _a.getAttribute(\"dir\")) != null ? _b : initialValue;\n  }\n  const dir = ref(getValue());\n  tryOnMounted(() => dir.value = getValue());\n  if (observe && document) {\n    useMutationObserver(\n      document.querySelector(selector),\n      () => dir.value = getValue(),\n      { attributes: true }\n    );\n  }\n  return computed({\n    get() {\n      return dir.value;\n    },\n    set(v) {\n      var _a, _b;\n      dir.value = v;\n      if (!document)\n        return;\n      if (dir.value)\n        (_a = document.querySelector(selector)) == null ? void 0 : _a.setAttribute(\"dir\", dir.value);\n      else\n        (_b = document.querySelector(selector)) == null ? void 0 : _b.removeAttribute(\"dir\");\n    }\n  });\n}\n\nfunction getRangesFromSelection(selection) {\n  var _a;\n  const rangeCount = (_a = selection.rangeCount) != null ? _a : 0;\n  return Array.from({ length: rangeCount }, (_, i) => selection.getRangeAt(i));\n}\nfunction useTextSelection(options = {}) {\n  const {\n    window = defaultWindow\n  } = options;\n  const selection = ref(null);\n  const text = computed(() => {\n    var _a, _b;\n    return (_b = (_a = selection.value) == null ? void 0 : _a.toString()) != null ? _b : \"\";\n  });\n  const ranges = computed(() => selection.value ? getRangesFromSelection(selection.value) : []);\n  const rects = computed(() => ranges.value.map((range) => range.getBoundingClientRect()));\n  function onSelectionChange() {\n    selection.value = null;\n    if (window)\n      selection.value = window.getSelection();\n  }\n  if (window)\n    useEventListener(window.document, \"selectionchange\", onSelectionChange, { passive: true });\n  return {\n    text,\n    rects,\n    ranges,\n    selection\n  };\n}\n\nfunction tryRequestAnimationFrame(window = defaultWindow, fn) {\n  if (window && typeof window.requestAnimationFrame === \"function\") {\n    window.requestAnimationFrame(fn);\n  } else {\n    fn();\n  }\n}\nfunction useTextareaAutosize(options = {}) {\n  var _a, _b;\n  const { window = defaultWindow } = options;\n  const textarea = toRef(options == null ? void 0 : options.element);\n  const input = toRef((_a = options == null ? void 0 : options.input) != null ? _a : \"\");\n  const styleProp = (_b = options == null ? void 0 : options.styleProp) != null ? _b : \"height\";\n  const textareaScrollHeight = shallowRef(1);\n  const textareaOldWidth = shallowRef(0);\n  function triggerResize() {\n    var _a2;\n    if (!textarea.value)\n      return;\n    let height = \"\";\n    textarea.value.style[styleProp] = \"1px\";\n    textareaScrollHeight.value = (_a2 = textarea.value) == null ? void 0 : _a2.scrollHeight;\n    const _styleTarget = toValue(options == null ? void 0 : options.styleTarget);\n    if (_styleTarget)\n      _styleTarget.style[styleProp] = `${textareaScrollHeight.value}px`;\n    else\n      height = `${textareaScrollHeight.value}px`;\n    textarea.value.style[styleProp] = height;\n  }\n  watch([input, textarea], () => nextTick(triggerResize), { immediate: true });\n  watch(textareaScrollHeight, () => {\n    var _a2;\n    return (_a2 = options == null ? void 0 : options.onResize) == null ? void 0 : _a2.call(options);\n  });\n  useResizeObserver(textarea, ([{ contentRect }]) => {\n    if (textareaOldWidth.value === contentRect.width)\n      return;\n    tryRequestAnimationFrame(window, () => {\n      textareaOldWidth.value = contentRect.width;\n      triggerResize();\n    });\n  });\n  if (options == null ? void 0 : options.watch)\n    watch(options.watch, triggerResize, { immediate: true, deep: true });\n  return {\n    textarea,\n    input,\n    triggerResize\n  };\n}\n\nfunction useThrottledRefHistory(source, options = {}) {\n  const { throttle = 200, trailing = true } = options;\n  const filter = throttleFilter(throttle, trailing);\n  const history = useRefHistory(source, { ...options, eventFilter: filter });\n  return {\n    ...history\n  };\n}\n\nconst DEFAULT_UNITS = [\n  { max: 6e4, value: 1e3, name: \"second\" },\n  { max: 276e4, value: 6e4, name: \"minute\" },\n  { max: 72e6, value: 36e5, name: \"hour\" },\n  { max: 5184e5, value: 864e5, name: \"day\" },\n  { max: 24192e5, value: 6048e5, name: \"week\" },\n  { max: 28512e6, value: 2592e6, name: \"month\" },\n  { max: Number.POSITIVE_INFINITY, value: 31536e6, name: \"year\" }\n];\nconst DEFAULT_MESSAGES = {\n  justNow: \"just now\",\n  past: (n) => n.match(/\\d/) ? `${n} ago` : n,\n  future: (n) => n.match(/\\d/) ? `in ${n}` : n,\n  month: (n, past) => n === 1 ? past ? \"last month\" : \"next month\" : `${n} month${n > 1 ? \"s\" : \"\"}`,\n  year: (n, past) => n === 1 ? past ? \"last year\" : \"next year\" : `${n} year${n > 1 ? \"s\" : \"\"}`,\n  day: (n, past) => n === 1 ? past ? \"yesterday\" : \"tomorrow\" : `${n} day${n > 1 ? \"s\" : \"\"}`,\n  week: (n, past) => n === 1 ? past ? \"last week\" : \"next week\" : `${n} week${n > 1 ? \"s\" : \"\"}`,\n  hour: (n) => `${n} hour${n > 1 ? \"s\" : \"\"}`,\n  minute: (n) => `${n} minute${n > 1 ? \"s\" : \"\"}`,\n  second: (n) => `${n} second${n > 1 ? \"s\" : \"\"}`,\n  invalid: \"\"\n};\nfunction DEFAULT_FORMATTER(date) {\n  return date.toISOString().slice(0, 10);\n}\nfunction useTimeAgo(time, options = {}) {\n  const {\n    controls: exposeControls = false,\n    updateInterval = 3e4\n  } = options;\n  const { now, ...controls } = useNow({ interval: updateInterval, controls: true });\n  const timeAgo = computed(() => formatTimeAgo(new Date(toValue(time)), options, toValue(now)));\n  if (exposeControls) {\n    return {\n      timeAgo,\n      ...controls\n    };\n  } else {\n    return timeAgo;\n  }\n}\nfunction formatTimeAgo(from, options = {}, now = Date.now()) {\n  var _a;\n  const {\n    max,\n    messages = DEFAULT_MESSAGES,\n    fullDateFormatter = DEFAULT_FORMATTER,\n    units = DEFAULT_UNITS,\n    showSecond = false,\n    rounding = \"round\"\n  } = options;\n  const roundFn = typeof rounding === \"number\" ? (n) => +n.toFixed(rounding) : Math[rounding];\n  const diff = +now - +from;\n  const absDiff = Math.abs(diff);\n  function getValue(diff2, unit) {\n    return roundFn(Math.abs(diff2) / unit.value);\n  }\n  function format(diff2, unit) {\n    const val = getValue(diff2, unit);\n    const past = diff2 > 0;\n    const str = applyFormat(unit.name, val, past);\n    return applyFormat(past ? \"past\" : \"future\", str, past);\n  }\n  function applyFormat(name, val, isPast) {\n    const formatter = messages[name];\n    if (typeof formatter === \"function\")\n      return formatter(val, isPast);\n    return formatter.replace(\"{0}\", val.toString());\n  }\n  if (absDiff < 6e4 && !showSecond)\n    return messages.justNow;\n  if (typeof max === \"number\" && absDiff > max)\n    return fullDateFormatter(new Date(from));\n  if (typeof max === \"string\") {\n    const unitMax = (_a = units.find((i) => i.name === max)) == null ? void 0 : _a.max;\n    if (unitMax && absDiff > unitMax)\n      return fullDateFormatter(new Date(from));\n  }\n  for (const [idx, unit] of units.entries()) {\n    const val = getValue(diff, unit);\n    if (val <= 0 && units[idx - 1])\n      return format(diff, units[idx - 1]);\n    if (absDiff < unit.max)\n      return format(diff, unit);\n  }\n  return messages.invalid;\n}\n\nfunction useTimeoutPoll(fn, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const { start } = useTimeoutFn(loop, interval, { immediate });\n  const isActive = shallowRef(false);\n  async function loop() {\n    if (!isActive.value)\n      return;\n    await fn();\n    start();\n  }\n  function resume() {\n    if (!isActive.value) {\n      isActive.value = true;\n      if (immediateCallback)\n        fn();\n      start();\n    }\n  }\n  function pause() {\n    isActive.value = false;\n  }\n  if (immediate && isClient)\n    resume();\n  tryOnScopeDispose(pause);\n  return {\n    isActive,\n    pause,\n    resume\n  };\n}\n\nfunction useTimestamp(options = {}) {\n  const {\n    controls: exposeControls = false,\n    offset = 0,\n    immediate = true,\n    interval = \"requestAnimationFrame\",\n    callback\n  } = options;\n  const ts = shallowRef(timestamp() + offset);\n  const update = () => ts.value = timestamp() + offset;\n  const cb = callback ? () => {\n    update();\n    callback(ts.value);\n  } : update;\n  const controls = interval === \"requestAnimationFrame\" ? useRafFn(cb, { immediate }) : useIntervalFn(cb, interval, { immediate });\n  if (exposeControls) {\n    return {\n      timestamp: ts,\n      ...controls\n    };\n  } else {\n    return ts;\n  }\n}\n\nfunction useTitle(newTitle = null, options = {}) {\n  var _a, _b, _c;\n  const {\n    document = defaultDocument,\n    restoreOnUnmount = (t) => t\n  } = options;\n  const originalTitle = (_a = document == null ? void 0 : document.title) != null ? _a : \"\";\n  const title = toRef((_b = newTitle != null ? newTitle : document == null ? void 0 : document.title) != null ? _b : null);\n  const isReadonly = !!(newTitle && typeof newTitle === \"function\");\n  function format(t) {\n    if (!(\"titleTemplate\" in options))\n      return t;\n    const template = options.titleTemplate || \"%s\";\n    return typeof template === \"function\" ? template(t) : toValue(template).replace(/%s/g, t);\n  }\n  watch(\n    title,\n    (newValue, oldValue) => {\n      if (newValue !== oldValue && document)\n        document.title = format(newValue != null ? newValue : \"\");\n    },\n    { immediate: true }\n  );\n  if (options.observe && !options.titleTemplate && document && !isReadonly) {\n    useMutationObserver(\n      (_c = document.head) == null ? void 0 : _c.querySelector(\"title\"),\n      () => {\n        if (document && document.title !== title.value)\n          title.value = format(document.title);\n      },\n      { childList: true }\n    );\n  }\n  tryOnScopeDispose(() => {\n    if (restoreOnUnmount) {\n      const restoredTitle = restoreOnUnmount(originalTitle, title.value || \"\");\n      if (restoredTitle != null && document)\n        document.title = restoredTitle;\n    }\n  });\n  return title;\n}\n\nconst _TransitionPresets = {\n  easeInSine: [0.12, 0, 0.39, 0],\n  easeOutSine: [0.61, 1, 0.88, 1],\n  easeInOutSine: [0.37, 0, 0.63, 1],\n  easeInQuad: [0.11, 0, 0.5, 0],\n  easeOutQuad: [0.5, 1, 0.89, 1],\n  easeInOutQuad: [0.45, 0, 0.55, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeInQuart: [0.5, 0, 0.75, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  easeInQuint: [0.64, 0, 0.78, 0],\n  easeOutQuint: [0.22, 1, 0.36, 1],\n  easeInOutQuint: [0.83, 0, 0.17, 1],\n  easeInExpo: [0.7, 0, 0.84, 0],\n  easeOutExpo: [0.16, 1, 0.3, 1],\n  easeInOutExpo: [0.87, 0, 0.13, 1],\n  easeInCirc: [0.55, 0, 1, 0.45],\n  easeOutCirc: [0, 0.55, 0.45, 1],\n  easeInOutCirc: [0.85, 0, 0.15, 1],\n  easeInBack: [0.36, 0, 0.66, -0.56],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6]\n};\nconst TransitionPresets = /* @__PURE__ */ Object.assign({}, { linear: identity }, _TransitionPresets);\nfunction createEasingFunction([p0, p1, p2, p3]) {\n  const a = (a1, a2) => 1 - 3 * a2 + 3 * a1;\n  const b = (a1, a2) => 3 * a2 - 6 * a1;\n  const c = (a1) => 3 * a1;\n  const calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\n  const getSlope = (t, a1, a2) => 3 * a(a1, a2) * t * t + 2 * b(a1, a2) * t + c(a1);\n  const getTforX = (x) => {\n    let aGuessT = x;\n    for (let i = 0; i < 4; ++i) {\n      const currentSlope = getSlope(aGuessT, p0, p2);\n      if (currentSlope === 0)\n        return aGuessT;\n      const currentX = calcBezier(aGuessT, p0, p2) - x;\n      aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n  };\n  return (x) => p0 === p1 && p2 === p3 ? x : calcBezier(getTforX(x), p1, p3);\n}\nfunction lerp(a, b, alpha) {\n  return a + alpha * (b - a);\n}\nfunction toVec(t) {\n  return (typeof t === \"number\" ? [t] : t) || [];\n}\nfunction executeTransition(source, from, to, options = {}) {\n  var _a, _b;\n  const fromVal = toValue(from);\n  const toVal = toValue(to);\n  const v1 = toVec(fromVal);\n  const v2 = toVec(toVal);\n  const duration = (_a = toValue(options.duration)) != null ? _a : 1e3;\n  const startedAt = Date.now();\n  const endAt = Date.now() + duration;\n  const trans = typeof options.transition === \"function\" ? options.transition : (_b = toValue(options.transition)) != null ? _b : identity;\n  const ease = typeof trans === \"function\" ? trans : createEasingFunction(trans);\n  return new Promise((resolve) => {\n    source.value = fromVal;\n    const tick = () => {\n      var _a2;\n      if ((_a2 = options.abort) == null ? void 0 : _a2.call(options)) {\n        resolve();\n        return;\n      }\n      const now = Date.now();\n      const alpha = ease((now - startedAt) / duration);\n      const arr = toVec(source.value).map((n, i) => lerp(v1[i], v2[i], alpha));\n      if (Array.isArray(source.value))\n        source.value = arr.map((n, i) => {\n          var _a3, _b2;\n          return lerp((_a3 = v1[i]) != null ? _a3 : 0, (_b2 = v2[i]) != null ? _b2 : 0, alpha);\n        });\n      else if (typeof source.value === \"number\")\n        source.value = arr[0];\n      if (now < endAt) {\n        requestAnimationFrame(tick);\n      } else {\n        source.value = toVal;\n        resolve();\n      }\n    };\n    tick();\n  });\n}\nfunction useTransition(source, options = {}) {\n  let currentId = 0;\n  const sourceVal = () => {\n    const v = toValue(source);\n    return typeof v === \"number\" ? v : v.map(toValue);\n  };\n  const outputRef = ref(sourceVal());\n  watch(sourceVal, async (to) => {\n    var _a, _b;\n    if (toValue(options.disabled))\n      return;\n    const id = ++currentId;\n    if (options.delay)\n      await promiseTimeout(toValue(options.delay));\n    if (id !== currentId)\n      return;\n    const toVal = Array.isArray(to) ? to.map(toValue) : toValue(to);\n    (_a = options.onStarted) == null ? void 0 : _a.call(options);\n    await executeTransition(outputRef, outputRef.value, toVal, {\n      ...options,\n      abort: () => {\n        var _a2;\n        return id !== currentId || ((_a2 = options.abort) == null ? void 0 : _a2.call(options));\n      }\n    });\n    (_b = options.onFinished) == null ? void 0 : _b.call(options);\n  }, { deep: true });\n  watch(() => toValue(options.disabled), (disabled) => {\n    if (disabled) {\n      currentId++;\n      outputRef.value = sourceVal();\n    }\n  });\n  tryOnScopeDispose(() => {\n    currentId++;\n  });\n  return computed(() => toValue(options.disabled) ? sourceVal() : outputRef.value);\n}\n\nfunction useUrlSearchParams(mode = \"history\", options = {}) {\n  const {\n    initialValue = {},\n    removeNullishValues = true,\n    removeFalsyValues = false,\n    write: enableWrite = true,\n    writeMode = \"replace\",\n    window = defaultWindow\n  } = options;\n  if (!window)\n    return reactive(initialValue);\n  const state = reactive({});\n  function getRawParams() {\n    if (mode === \"history\") {\n      return window.location.search || \"\";\n    } else if (mode === \"hash\") {\n      const hash = window.location.hash || \"\";\n      const index = hash.indexOf(\"?\");\n      return index > 0 ? hash.slice(index) : \"\";\n    } else {\n      return (window.location.hash || \"\").replace(/^#/, \"\");\n    }\n  }\n  function constructQuery(params) {\n    const stringified = params.toString();\n    if (mode === \"history\")\n      return `${stringified ? `?${stringified}` : \"\"}${window.location.hash || \"\"}`;\n    if (mode === \"hash-params\")\n      return `${window.location.search || \"\"}${stringified ? `#${stringified}` : \"\"}`;\n    const hash = window.location.hash || \"#\";\n    const index = hash.indexOf(\"?\");\n    if (index > 0)\n      return `${window.location.search || \"\"}${hash.slice(0, index)}${stringified ? `?${stringified}` : \"\"}`;\n    return `${window.location.search || \"\"}${hash}${stringified ? `?${stringified}` : \"\"}`;\n  }\n  function read() {\n    return new URLSearchParams(getRawParams());\n  }\n  function updateState(params) {\n    const unusedKeys = new Set(Object.keys(state));\n    for (const key of params.keys()) {\n      const paramsForKey = params.getAll(key);\n      state[key] = paramsForKey.length > 1 ? paramsForKey : params.get(key) || \"\";\n      unusedKeys.delete(key);\n    }\n    Array.from(unusedKeys).forEach((key) => delete state[key]);\n  }\n  const { pause, resume } = pausableWatch(\n    state,\n    () => {\n      const params = new URLSearchParams(\"\");\n      Object.keys(state).forEach((key) => {\n        const mapEntry = state[key];\n        if (Array.isArray(mapEntry))\n          mapEntry.forEach((value) => params.append(key, value));\n        else if (removeNullishValues && mapEntry == null)\n          params.delete(key);\n        else if (removeFalsyValues && !mapEntry)\n          params.delete(key);\n        else\n          params.set(key, mapEntry);\n      });\n      write(params, false);\n    },\n    { deep: true }\n  );\n  function write(params, shouldUpdate) {\n    pause();\n    if (shouldUpdate)\n      updateState(params);\n    if (writeMode === \"replace\") {\n      window.history.replaceState(\n        window.history.state,\n        window.document.title,\n        window.location.pathname + constructQuery(params)\n      );\n    } else {\n      window.history.pushState(\n        window.history.state,\n        window.document.title,\n        window.location.pathname + constructQuery(params)\n      );\n    }\n    resume();\n  }\n  function onChanged() {\n    if (!enableWrite)\n      return;\n    write(read(), true);\n  }\n  const listenerOptions = { passive: true };\n  useEventListener(window, \"popstate\", onChanged, listenerOptions);\n  if (mode !== \"history\")\n    useEventListener(window, \"hashchange\", onChanged, listenerOptions);\n  const initial = read();\n  if (initial.keys().next().value)\n    updateState(initial);\n  else\n    Object.assign(state, initialValue);\n  return state;\n}\n\nfunction useUserMedia(options = {}) {\n  var _a, _b;\n  const enabled = shallowRef((_a = options.enabled) != null ? _a : false);\n  const autoSwitch = shallowRef((_b = options.autoSwitch) != null ? _b : true);\n  const constraints = ref(options.constraints);\n  const { navigator = defaultNavigator } = options;\n  const isSupported = useSupported(() => {\n    var _a2;\n    return (_a2 = navigator == null ? void 0 : navigator.mediaDevices) == null ? void 0 : _a2.getUserMedia;\n  });\n  const stream = shallowRef();\n  function getDeviceOptions(type) {\n    switch (type) {\n      case \"video\": {\n        if (constraints.value)\n          return constraints.value.video || false;\n        break;\n      }\n      case \"audio\": {\n        if (constraints.value)\n          return constraints.value.audio || false;\n        break;\n      }\n    }\n  }\n  async function _start() {\n    if (!isSupported.value || stream.value)\n      return;\n    stream.value = await navigator.mediaDevices.getUserMedia({\n      video: getDeviceOptions(\"video\"),\n      audio: getDeviceOptions(\"audio\")\n    });\n    return stream.value;\n  }\n  function _stop() {\n    var _a2;\n    (_a2 = stream.value) == null ? void 0 : _a2.getTracks().forEach((t) => t.stop());\n    stream.value = void 0;\n  }\n  function stop() {\n    _stop();\n    enabled.value = false;\n  }\n  async function start() {\n    await _start();\n    if (stream.value)\n      enabled.value = true;\n    return stream.value;\n  }\n  async function restart() {\n    _stop();\n    return await start();\n  }\n  watch(\n    enabled,\n    (v) => {\n      if (v)\n        _start();\n      else _stop();\n    },\n    { immediate: true }\n  );\n  watch(\n    constraints,\n    () => {\n      if (autoSwitch.value && stream.value)\n        restart();\n    },\n    { immediate: true }\n  );\n  tryOnScopeDispose(() => {\n    stop();\n  });\n  return {\n    isSupported,\n    stream,\n    start,\n    stop,\n    restart,\n    constraints,\n    enabled,\n    autoSwitch\n  };\n}\n\nfunction useVModel(props, key, emit, options = {}) {\n  var _a, _b, _c;\n  const {\n    clone = false,\n    passive = false,\n    eventName,\n    deep = false,\n    defaultValue,\n    shouldEmit\n  } = options;\n  const vm = getCurrentInstance();\n  const _emit = emit || (vm == null ? void 0 : vm.emit) || ((_a = vm == null ? void 0 : vm.$emit) == null ? void 0 : _a.bind(vm)) || ((_c = (_b = vm == null ? void 0 : vm.proxy) == null ? void 0 : _b.$emit) == null ? void 0 : _c.bind(vm == null ? void 0 : vm.proxy));\n  let event = eventName;\n  if (!key) {\n    key = \"modelValue\";\n  }\n  event = event || `update:${key.toString()}`;\n  const cloneFn = (val) => !clone ? val : typeof clone === \"function\" ? clone(val) : cloneFnJSON(val);\n  const getValue = () => isDef(props[key]) ? cloneFn(props[key]) : defaultValue;\n  const triggerEmit = (value) => {\n    if (shouldEmit) {\n      if (shouldEmit(value))\n        _emit(event, value);\n    } else {\n      _emit(event, value);\n    }\n  };\n  if (passive) {\n    const initialValue = getValue();\n    const proxy = ref(initialValue);\n    let isUpdating = false;\n    watch(\n      () => props[key],\n      (v) => {\n        if (!isUpdating) {\n          isUpdating = true;\n          proxy.value = cloneFn(v);\n          nextTick(() => isUpdating = false);\n        }\n      }\n    );\n    watch(\n      proxy,\n      (v) => {\n        if (!isUpdating && (v !== props[key] || deep))\n          triggerEmit(v);\n      },\n      { deep }\n    );\n    return proxy;\n  } else {\n    return computed({\n      get() {\n        return getValue();\n      },\n      set(value) {\n        triggerEmit(value);\n      }\n    });\n  }\n}\n\nfunction useVModels(props, emit, options = {}) {\n  const ret = {};\n  for (const key in props) {\n    ret[key] = useVModel(\n      props,\n      key,\n      emit,\n      options\n    );\n  }\n  return ret;\n}\n\nfunction useVibrate(options) {\n  const {\n    pattern = [],\n    interval = 0,\n    navigator = defaultNavigator\n  } = options || {};\n  const isSupported = useSupported(() => typeof navigator !== \"undefined\" && \"vibrate\" in navigator);\n  const patternRef = toRef(pattern);\n  let intervalControls;\n  const vibrate = (pattern2 = patternRef.value) => {\n    if (isSupported.value)\n      navigator.vibrate(pattern2);\n  };\n  const stop = () => {\n    if (isSupported.value)\n      navigator.vibrate(0);\n    intervalControls == null ? void 0 : intervalControls.pause();\n  };\n  if (interval > 0) {\n    intervalControls = useIntervalFn(\n      vibrate,\n      interval,\n      {\n        immediate: false,\n        immediateCallback: false\n      }\n    );\n  }\n  return {\n    isSupported,\n    pattern,\n    intervalControls,\n    vibrate,\n    stop\n  };\n}\n\nfunction useVirtualList(list, options) {\n  const { containerStyle, wrapperProps, scrollTo, calculateRange, currentList, containerRef } = \"itemHeight\" in options ? useVerticalVirtualList(options, list) : useHorizontalVirtualList(options, list);\n  return {\n    list: currentList,\n    scrollTo,\n    containerProps: {\n      ref: containerRef,\n      onScroll: () => {\n        calculateRange();\n      },\n      style: containerStyle\n    },\n    wrapperProps\n  };\n}\nfunction useVirtualListResources(list) {\n  const containerRef = shallowRef(null);\n  const size = useElementSize(containerRef);\n  const currentList = ref([]);\n  const source = shallowRef(list);\n  const state = ref({ start: 0, end: 10 });\n  return { state, source, currentList, size, containerRef };\n}\nfunction createGetViewCapacity(state, source, itemSize) {\n  return (containerSize) => {\n    if (typeof itemSize === \"number\")\n      return Math.ceil(containerSize / itemSize);\n    const { start = 0 } = state.value;\n    let sum = 0;\n    let capacity = 0;\n    for (let i = start; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      capacity = i;\n      if (sum > containerSize)\n        break;\n    }\n    return capacity - start;\n  };\n}\nfunction createGetOffset(source, itemSize) {\n  return (scrollDirection) => {\n    if (typeof itemSize === \"number\")\n      return Math.floor(scrollDirection / itemSize) + 1;\n    let sum = 0;\n    let offset = 0;\n    for (let i = 0; i < source.value.length; i++) {\n      const size = itemSize(i);\n      sum += size;\n      if (sum >= scrollDirection) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n}\nfunction createCalculateRange(type, overscan, getOffset, getViewCapacity, { containerRef, state, currentList, source }) {\n  return () => {\n    const element = containerRef.value;\n    if (element) {\n      const offset = getOffset(type === \"vertical\" ? element.scrollTop : element.scrollLeft);\n      const viewCapacity = getViewCapacity(type === \"vertical\" ? element.clientHeight : element.clientWidth);\n      const from = offset - overscan;\n      const to = offset + viewCapacity + overscan;\n      state.value = {\n        start: from < 0 ? 0 : from,\n        end: to > source.value.length ? source.value.length : to\n      };\n      currentList.value = source.value.slice(state.value.start, state.value.end).map((ele, index) => ({\n        data: ele,\n        index: index + state.value.start\n      }));\n    }\n  };\n}\nfunction createGetDistance(itemSize, source) {\n  return (index) => {\n    if (typeof itemSize === \"number\") {\n      const size2 = index * itemSize;\n      return size2;\n    }\n    const size = source.value.slice(0, index).reduce((sum, _, i) => sum + itemSize(i), 0);\n    return size;\n  };\n}\nfunction useWatchForSizes(size, list, containerRef, calculateRange) {\n  watch([size.width, size.height, list, containerRef], () => {\n    calculateRange();\n  });\n}\nfunction createComputedTotalSize(itemSize, source) {\n  return computed(() => {\n    if (typeof itemSize === \"number\")\n      return source.value.length * itemSize;\n    return source.value.reduce((sum, _, index) => sum + itemSize(index), 0);\n  });\n}\nconst scrollToDictionaryForElementScrollKey = {\n  horizontal: \"scrollLeft\",\n  vertical: \"scrollTop\"\n};\nfunction createScrollTo(type, calculateRange, getDistance, containerRef) {\n  return (index) => {\n    if (containerRef.value) {\n      containerRef.value[scrollToDictionaryForElementScrollKey[type]] = getDistance(index);\n      calculateRange();\n    }\n  };\n}\nfunction useHorizontalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowX: \"auto\" };\n  const { itemWidth, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemWidth);\n  const getOffset = createGetOffset(source, itemWidth);\n  const calculateRange = createCalculateRange(\"horizontal\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceLeft = createGetDistance(itemWidth, source);\n  const offsetLeft = computed(() => getDistanceLeft(state.value.start));\n  const totalWidth = createComputedTotalSize(itemWidth, source);\n  useWatchForSizes(size, list, containerRef, calculateRange);\n  const scrollTo = createScrollTo(\"horizontal\", calculateRange, getDistanceLeft, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        height: \"100%\",\n        width: `${totalWidth.value - offsetLeft.value}px`,\n        marginLeft: `${offsetLeft.value}px`,\n        display: \"flex\"\n      }\n    };\n  });\n  return {\n    scrollTo,\n    calculateRange,\n    wrapperProps,\n    containerStyle,\n    currentList,\n    containerRef\n  };\n}\nfunction useVerticalVirtualList(options, list) {\n  const resources = useVirtualListResources(list);\n  const { state, source, currentList, size, containerRef } = resources;\n  const containerStyle = { overflowY: \"auto\" };\n  const { itemHeight, overscan = 5 } = options;\n  const getViewCapacity = createGetViewCapacity(state, source, itemHeight);\n  const getOffset = createGetOffset(source, itemHeight);\n  const calculateRange = createCalculateRange(\"vertical\", overscan, getOffset, getViewCapacity, resources);\n  const getDistanceTop = createGetDistance(itemHeight, source);\n  const offsetTop = computed(() => getDistanceTop(state.value.start));\n  const totalHeight = createComputedTotalSize(itemHeight, source);\n  useWatchForSizes(size, list, containerRef, calculateRange);\n  const scrollTo = createScrollTo(\"vertical\", calculateRange, getDistanceTop, containerRef);\n  const wrapperProps = computed(() => {\n    return {\n      style: {\n        width: \"100%\",\n        height: `${totalHeight.value - offsetTop.value}px`,\n        marginTop: `${offsetTop.value}px`\n      }\n    };\n  });\n  return {\n    calculateRange,\n    scrollTo,\n    containerStyle,\n    wrapperProps,\n    currentList,\n    containerRef\n  };\n}\n\nfunction useWakeLock(options = {}) {\n  const {\n    navigator = defaultNavigator,\n    document = defaultDocument\n  } = options;\n  const requestedType = shallowRef(false);\n  const sentinel = shallowRef(null);\n  const documentVisibility = useDocumentVisibility({ document });\n  const isSupported = useSupported(() => navigator && \"wakeLock\" in navigator);\n  const isActive = computed(() => !!sentinel.value && documentVisibility.value === \"visible\");\n  if (isSupported.value) {\n    useEventListener(sentinel, \"release\", () => {\n      var _a, _b;\n      requestedType.value = (_b = (_a = sentinel.value) == null ? void 0 : _a.type) != null ? _b : false;\n    }, { passive: true });\n    whenever(\n      () => documentVisibility.value === \"visible\" && (document == null ? void 0 : document.visibilityState) === \"visible\" && requestedType.value,\n      (type) => {\n        requestedType.value = false;\n        forceRequest(type);\n      }\n    );\n  }\n  async function forceRequest(type) {\n    var _a;\n    await ((_a = sentinel.value) == null ? void 0 : _a.release());\n    sentinel.value = isSupported.value ? await navigator.wakeLock.request(type) : null;\n  }\n  async function request(type) {\n    if (documentVisibility.value === \"visible\")\n      await forceRequest(type);\n    else\n      requestedType.value = type;\n  }\n  async function release() {\n    requestedType.value = false;\n    const s = sentinel.value;\n    sentinel.value = null;\n    await (s == null ? void 0 : s.release());\n  }\n  return {\n    sentinel,\n    isSupported,\n    isActive,\n    request,\n    forceRequest,\n    release\n  };\n}\n\nfunction useWebNotification(options = {}) {\n  const {\n    window = defaultWindow,\n    requestPermissions: _requestForPermissions = true\n  } = options;\n  const defaultWebNotificationOptions = options;\n  const isSupported = useSupported(() => {\n    if (!window || !(\"Notification\" in window))\n      return false;\n    if (Notification.permission === \"granted\")\n      return true;\n    try {\n      const notification2 = new Notification(\"\");\n      notification2.onshow = () => {\n        notification2.close();\n      };\n    } catch (e) {\n      if (e.name === \"TypeError\")\n        return false;\n    }\n    return true;\n  });\n  const permissionGranted = shallowRef(isSupported.value && \"permission\" in Notification && Notification.permission === \"granted\");\n  const notification = ref(null);\n  const ensurePermissions = async () => {\n    if (!isSupported.value)\n      return;\n    if (!permissionGranted.value && Notification.permission !== \"denied\") {\n      const result = await Notification.requestPermission();\n      if (result === \"granted\")\n        permissionGranted.value = true;\n    }\n    return permissionGranted.value;\n  };\n  const { on: onClick, trigger: clickTrigger } = createEventHook();\n  const { on: onShow, trigger: showTrigger } = createEventHook();\n  const { on: onError, trigger: errorTrigger } = createEventHook();\n  const { on: onClose, trigger: closeTrigger } = createEventHook();\n  const show = async (overrides) => {\n    if (!isSupported.value || !permissionGranted.value)\n      return;\n    const options2 = Object.assign({}, defaultWebNotificationOptions, overrides);\n    notification.value = new Notification(options2.title || \"\", options2);\n    notification.value.onclick = clickTrigger;\n    notification.value.onshow = showTrigger;\n    notification.value.onerror = errorTrigger;\n    notification.value.onclose = closeTrigger;\n    return notification.value;\n  };\n  const close = () => {\n    if (notification.value)\n      notification.value.close();\n    notification.value = null;\n  };\n  if (_requestForPermissions)\n    tryOnMounted(ensurePermissions);\n  tryOnScopeDispose(close);\n  if (isSupported.value && window) {\n    const document = window.document;\n    useEventListener(document, \"visibilitychange\", (e) => {\n      e.preventDefault();\n      if (document.visibilityState === \"visible\") {\n        close();\n      }\n    });\n  }\n  return {\n    isSupported,\n    notification,\n    ensurePermissions,\n    permissionGranted,\n    show,\n    close,\n    onClick,\n    onShow,\n    onError,\n    onClose\n  };\n}\n\nconst DEFAULT_PING_MESSAGE = \"ping\";\nfunction resolveNestedOptions(options) {\n  if (options === true)\n    return {};\n  return options;\n}\nfunction useWebSocket(url, options = {}) {\n  const {\n    onConnected,\n    onDisconnected,\n    onError,\n    onMessage,\n    immediate = true,\n    autoConnect = true,\n    autoClose = true,\n    protocols = []\n  } = options;\n  const data = ref(null);\n  const status = shallowRef(\"CLOSED\");\n  const wsRef = ref();\n  const urlRef = toRef(url);\n  let heartbeatPause;\n  let heartbeatResume;\n  let explicitlyClosed = false;\n  let retried = 0;\n  let bufferedData = [];\n  let retryTimeout;\n  let pongTimeoutWait;\n  const _sendBuffer = () => {\n    if (bufferedData.length && wsRef.value && status.value === \"OPEN\") {\n      for (const buffer of bufferedData)\n        wsRef.value.send(buffer);\n      bufferedData = [];\n    }\n  };\n  const resetRetry = () => {\n    if (retryTimeout != null) {\n      clearTimeout(retryTimeout);\n      retryTimeout = void 0;\n    }\n  };\n  const resetHeartbeat = () => {\n    clearTimeout(pongTimeoutWait);\n    pongTimeoutWait = void 0;\n  };\n  const close = (code = 1e3, reason) => {\n    resetRetry();\n    if (!isClient && !isWorker || !wsRef.value)\n      return;\n    explicitlyClosed = true;\n    resetHeartbeat();\n    heartbeatPause == null ? void 0 : heartbeatPause();\n    wsRef.value.close(code, reason);\n    wsRef.value = void 0;\n  };\n  const send = (data2, useBuffer = true) => {\n    if (!wsRef.value || status.value !== \"OPEN\") {\n      if (useBuffer)\n        bufferedData.push(data2);\n      return false;\n    }\n    _sendBuffer();\n    wsRef.value.send(data2);\n    return true;\n  };\n  const _init = () => {\n    if (explicitlyClosed || typeof urlRef.value === \"undefined\")\n      return;\n    const ws = new WebSocket(urlRef.value, protocols);\n    wsRef.value = ws;\n    status.value = \"CONNECTING\";\n    ws.onopen = () => {\n      status.value = \"OPEN\";\n      retried = 0;\n      onConnected == null ? void 0 : onConnected(ws);\n      heartbeatResume == null ? void 0 : heartbeatResume();\n      _sendBuffer();\n    };\n    ws.onclose = (ev) => {\n      status.value = \"CLOSED\";\n      resetHeartbeat();\n      heartbeatPause == null ? void 0 : heartbeatPause();\n      onDisconnected == null ? void 0 : onDisconnected(ws, ev);\n      if (!explicitlyClosed && options.autoReconnect && (wsRef.value == null || ws === wsRef.value)) {\n        const {\n          retries = -1,\n          delay = 1e3,\n          onFailed\n        } = resolveNestedOptions(options.autoReconnect);\n        const checkRetires = typeof retries === \"function\" ? retries : () => typeof retries === \"number\" && (retries < 0 || retried < retries);\n        if (checkRetires(retried)) {\n          retried += 1;\n          retryTimeout = setTimeout(_init, delay);\n        } else {\n          onFailed == null ? void 0 : onFailed();\n        }\n      }\n    };\n    ws.onerror = (e) => {\n      onError == null ? void 0 : onError(ws, e);\n    };\n    ws.onmessage = (e) => {\n      if (options.heartbeat) {\n        resetHeartbeat();\n        const {\n          message = DEFAULT_PING_MESSAGE,\n          responseMessage = message\n        } = resolveNestedOptions(options.heartbeat);\n        if (e.data === toValue(responseMessage))\n          return;\n      }\n      data.value = e.data;\n      onMessage == null ? void 0 : onMessage(ws, e);\n    };\n  };\n  if (options.heartbeat) {\n    const {\n      message = DEFAULT_PING_MESSAGE,\n      interval = 1e3,\n      pongTimeout = 1e3\n    } = resolveNestedOptions(options.heartbeat);\n    const { pause, resume } = useIntervalFn(\n      () => {\n        send(toValue(message), false);\n        if (pongTimeoutWait != null)\n          return;\n        pongTimeoutWait = setTimeout(() => {\n          close();\n          explicitlyClosed = false;\n        }, pongTimeout);\n      },\n      interval,\n      { immediate: false }\n    );\n    heartbeatPause = pause;\n    heartbeatResume = resume;\n  }\n  if (autoClose) {\n    if (isClient)\n      useEventListener(\"beforeunload\", () => close(), { passive: true });\n    tryOnScopeDispose(close);\n  }\n  const open = () => {\n    if (!isClient && !isWorker)\n      return;\n    close();\n    explicitlyClosed = false;\n    retried = 0;\n    _init();\n  };\n  if (immediate)\n    open();\n  if (autoConnect)\n    watch(urlRef, open);\n  return {\n    data,\n    status,\n    close,\n    send,\n    open,\n    ws: wsRef\n  };\n}\n\nfunction useWebWorker(arg0, workerOptions, options) {\n  const {\n    window = defaultWindow\n  } = options != null ? options : {};\n  const data = ref(null);\n  const worker = shallowRef();\n  const post = (...args) => {\n    if (!worker.value)\n      return;\n    worker.value.postMessage(...args);\n  };\n  const terminate = function terminate2() {\n    if (!worker.value)\n      return;\n    worker.value.terminate();\n  };\n  if (window) {\n    if (typeof arg0 === \"string\")\n      worker.value = new Worker(arg0, workerOptions);\n    else if (typeof arg0 === \"function\")\n      worker.value = arg0();\n    else\n      worker.value = arg0;\n    worker.value.onmessage = (e) => {\n      data.value = e.data;\n    };\n    tryOnScopeDispose(() => {\n      if (worker.value)\n        worker.value.terminate();\n    });\n  }\n  return {\n    data,\n    post,\n    terminate,\n    worker\n  };\n}\n\nfunction depsParser(deps, localDeps) {\n  if (deps.length === 0 && localDeps.length === 0)\n    return \"\";\n  const depsString = deps.map((dep) => `'${dep}'`).toString();\n  const depsFunctionString = localDeps.filter((dep) => typeof dep === \"function\").map((fn) => {\n    const str = fn.toString();\n    if (str.trim().startsWith(\"function\")) {\n      return str;\n    } else {\n      const name = fn.name;\n      return `const ${name} = ${str}`;\n    }\n  }).join(\";\");\n  const importString = `importScripts(${depsString});`;\n  return `${depsString.trim() === \"\" ? \"\" : importString} ${depsFunctionString}`;\n}\n\nfunction jobRunner(userFunc) {\n  return (e) => {\n    const userFuncArgs = e.data[0];\n    return Promise.resolve(userFunc.apply(void 0, userFuncArgs)).then((result) => {\n      postMessage([\"SUCCESS\", result]);\n    }).catch((error) => {\n      postMessage([\"ERROR\", error]);\n    });\n  };\n}\n\nfunction createWorkerBlobUrl(fn, deps, localDeps) {\n  const blobCode = `${depsParser(deps, localDeps)}; onmessage=(${jobRunner})(${fn})`;\n  const blob = new Blob([blobCode], { type: \"text/javascript\" });\n  const url = URL.createObjectURL(blob);\n  return url;\n}\n\nfunction useWebWorkerFn(fn, options = {}) {\n  const {\n    dependencies = [],\n    localDependencies = [],\n    timeout,\n    window = defaultWindow\n  } = options;\n  const worker = ref();\n  const workerStatus = shallowRef(\"PENDING\");\n  const promise = ref({});\n  const timeoutId = shallowRef();\n  const workerTerminate = (status = \"PENDING\") => {\n    if (worker.value && worker.value._url && window) {\n      worker.value.terminate();\n      URL.revokeObjectURL(worker.value._url);\n      promise.value = {};\n      worker.value = void 0;\n      window.clearTimeout(timeoutId.value);\n      workerStatus.value = status;\n    }\n  };\n  workerTerminate();\n  tryOnScopeDispose(workerTerminate);\n  const generateWorker = () => {\n    const blobUrl = createWorkerBlobUrl(fn, dependencies, localDependencies);\n    const newWorker = new Worker(blobUrl);\n    newWorker._url = blobUrl;\n    newWorker.onmessage = (e) => {\n      const { resolve = () => {\n      }, reject = () => {\n      } } = promise.value;\n      const [status, result] = e.data;\n      switch (status) {\n        case \"SUCCESS\":\n          resolve(result);\n          workerTerminate(status);\n          break;\n        default:\n          reject(result);\n          workerTerminate(\"ERROR\");\n          break;\n      }\n    };\n    newWorker.onerror = (e) => {\n      const { reject = () => {\n      } } = promise.value;\n      e.preventDefault();\n      reject(e);\n      workerTerminate(\"ERROR\");\n    };\n    if (timeout) {\n      timeoutId.value = setTimeout(\n        () => workerTerminate(\"TIMEOUT_EXPIRED\"),\n        timeout\n      );\n    }\n    return newWorker;\n  };\n  const callWorker = (...fnArgs) => new Promise((resolve, reject) => {\n    var _a;\n    promise.value = {\n      resolve,\n      reject\n    };\n    (_a = worker.value) == null ? void 0 : _a.postMessage([[...fnArgs]]);\n    workerStatus.value = \"RUNNING\";\n  });\n  const workerFn = (...fnArgs) => {\n    if (workerStatus.value === \"RUNNING\") {\n      console.error(\n        \"[useWebWorkerFn] You can only run one instance of the worker at a time.\"\n      );\n      return Promise.reject();\n    }\n    worker.value = generateWorker();\n    return callWorker(...fnArgs);\n  };\n  return {\n    workerFn,\n    workerStatus,\n    workerTerminate\n  };\n}\n\nfunction useWindowFocus(options = {}) {\n  const { window = defaultWindow } = options;\n  if (!window)\n    return shallowRef(false);\n  const focused = shallowRef(window.document.hasFocus());\n  const listenerOptions = { passive: true };\n  useEventListener(window, \"blur\", () => {\n    focused.value = false;\n  }, listenerOptions);\n  useEventListener(window, \"focus\", () => {\n    focused.value = true;\n  }, listenerOptions);\n  return focused;\n}\n\nfunction useWindowScroll(options = {}) {\n  const { window = defaultWindow, ...rest } = options;\n  return useScroll(window, rest);\n}\n\nfunction useWindowSize(options = {}) {\n  const {\n    window = defaultWindow,\n    initialWidth = Number.POSITIVE_INFINITY,\n    initialHeight = Number.POSITIVE_INFINITY,\n    listenOrientation = true,\n    includeScrollbar = true,\n    type = \"inner\"\n  } = options;\n  const width = shallowRef(initialWidth);\n  const height = shallowRef(initialHeight);\n  const update = () => {\n    if (window) {\n      if (type === \"outer\") {\n        width.value = window.outerWidth;\n        height.value = window.outerHeight;\n      } else if (type === \"visual\" && window.visualViewport) {\n        const { width: visualViewportWidth, height: visualViewportHeight, scale } = window.visualViewport;\n        width.value = Math.round(visualViewportWidth * scale);\n        height.value = Math.round(visualViewportHeight * scale);\n      } else if (includeScrollbar) {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      } else {\n        width.value = window.document.documentElement.clientWidth;\n        height.value = window.document.documentElement.clientHeight;\n      }\n    }\n  };\n  update();\n  tryOnMounted(update);\n  const listenerOptions = { passive: true };\n  useEventListener(\"resize\", update, listenerOptions);\n  if (window && type === \"visual\" && window.visualViewport) {\n    useEventListener(window.visualViewport, \"resize\", update, listenerOptions);\n  }\n  if (listenOrientation) {\n    const matches = useMediaQuery(\"(orientation: portrait)\");\n    watch(matches, () => update());\n  }\n  return { width, height };\n}\n\nexport { DefaultMagicKeysAliasMap, StorageSerializers, TransitionPresets, computedAsync as asyncComputed, breakpointsAntDesign, breakpointsBootstrapV5, breakpointsElement, breakpointsMasterCss, breakpointsPrimeFlex, breakpointsQuasar, breakpointsSematic, breakpointsTailwind, breakpointsVuetify, breakpointsVuetifyV2, breakpointsVuetifyV3, cloneFnJSON, computedAsync, computedInject, createFetch, createReusableTemplate, createTemplatePromise, createUnrefFn, customStorageEventName, defaultDocument, defaultLocation, defaultNavigator, defaultWindow, executeTransition, formatTimeAgo, getSSRHandler, mapGamepadToXbox360Controller, onClickOutside, onElementRemoval, onKeyDown, onKeyPressed, onKeyStroke, onKeyUp, onLongPress, onStartTyping, provideSSRWidth, setSSRHandler, templateRef, unrefElement, useActiveElement, useAnimate, useAsyncQueue, useAsyncState, useBase64, useBattery, useBluetooth, useBreakpoints, useBroadcastChannel, useBrowserLocation, useCached, useClipboard, useClipboardItems, useCloned, useColorMode, useConfirmDialog, useCountdown, useCssVar, useCurrentElement, useCycleList, useDark, useDebouncedRefHistory, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDisplayMedia, useDocumentVisibility, useDraggable, useDropZone, useElementBounding, useElementByPoint, useElementHover, useElementSize, useElementVisibility, useEventBus, useEventListener, useEventSource, useEyeDropper, useFavicon, useFetch, useFileDialog, useFileSystemAccess, useFocus, useFocusWithin, useFps, useFullscreen, useGamepad, useGeolocation, useIdle, useImage, useInfiniteScroll, useIntersectionObserver, useKeyModifier, useLocalStorage, useMagicKeys, useManualRefHistory, useMediaControls, useMediaQuery, useMemoize, useMemory, useMounted, useMouse, useMouseInElement, useMousePressed, useMutationObserver, useNavigatorLanguage, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, useParallax, useParentElement, usePerformanceObserver, usePermission, usePointer, usePointerLock, usePointerSwipe, usePreferredColorScheme, usePreferredContrast, usePreferredDark, usePreferredLanguages, usePreferredReducedMotion, usePreferredReducedTransparency, usePrevious, useRafFn, useRefHistory, useResizeObserver, useSSRWidth, useScreenOrientation, useScreenSafeArea, useScriptTag, useScroll, useScrollLock, useSessionStorage, useShare, useSorted, useSpeechRecognition, useSpeechSynthesis, useStepper, useStorage, useStorageAsync, useStyleTag, useSupported, useSwipe, useTemplateRefsList, useTextDirection, useTextSelection, useTextareaAutosize, useThrottledRefHistory, useTimeAgo, useTimeoutPoll, useTimestamp, useTitle, useTransition, useUrlSearchParams, useUserMedia, useVModel, useVModels, useVibrate, useVirtualList, useWakeLock, useWebNotification, useWebSocket, useWebWorker, useWebWorkerFn, useWindowFocus, useWindowScroll, useWindowSize };\n", "/**\n * name: @tresjs/core\n * version: v4.3.6\n * (c) 2025\n * description: Declarative ThreeJS using Vue Components\n * author: <PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)\n */\nvar At = Object.defineProperty;\nvar xt = (e, t, r) => t in e ? At(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;\nvar de = (e, t, r) => xt(e, typeof t != \"symbol\" ? t + \"\" : t, r);\nimport { defineComponent as ue, withAsyncContext as ot, reactive as Se, renderSlot as st, unref as W, ref as $, computed as q, watchEffect as Ae, onUnmounted as K, shallowRef as H, watch as ie, readonly as _e, provide as ae, inject as kt, isRef as Lt, useSlots as Rt, getCurrentInstance as Ue, onMounted as Ot, createElementBlock as Dt, openBlock as jt, normalizeStyle as Bt, normalizeClass as It, createRenderer as $t, h as Fe, Fragment as Ht } from \"vue\";\nimport * as it from \"three\";\nimport { Layers as Ut, Scene as at, MathUtils as lt, Vector3 as xe, MeshBasicMaterial as ct, DoubleSide as Ft, TextureLoader as Wt, PerspectiveCamera as we, Camera as Nt, Clock as ut, REVISION as zt, Color as Z, NoToneMapping as Gt, PCFSoftShadowMap as Vt, ACESFilmicToneMapping as ft, SRGBColorSpace as Yt, WebGLRenderer as me, Vector2 as ge, Raycaster as qt, Object3D as Kt, BufferAttribute as Jt, ArrowHelper as Qt, Line as Xt, BufferGeometry as We, Float32BufferAttribute as Ne, LineBasicMaterial as Zt, Mesh as er, BackSide as tr, HemisphereLightHelper as rr, SpotLightHelper as nr, PointLightHelper as or, DirectionalLightHelper as sr } from \"three\";\nimport { tryOnScopeDispose as ir, toValue as A, unrefElement as ar, useDevicePixelRatio as lr, useWindowSize as cr, useElementSize as ur, refDebounced as ze, usePointer as fr, useElementBounding as pr, createEventHook as D, useFps as dr, useMemory as mr, useRafFn as pt } from \"@vueuse/core\";\nconst gr = \"@tresjs/core\", hr = \"module\", vr = \"4.3.6\", yr = \"pnpm@10.6.3\", _r = \"Declarative ThreeJS using Vue Components\", wr = \"Alvaro Saburido <<EMAIL>> (https://github.com/alvarosabu/)\", br = \"MIT\", Mr = { type: \"git\", url: \"git+https://github.com/Tresjs/tres.git\" }, Pr = [\"vue\", \"3d\", \"threejs\", \"three\", \"threejs-vue\"], Cr = !1, Er = { \".\": { types: \"./dist/index.d.ts\", import: \"./dist/tres.js\", require: \"./dist/tres.umd.cjs\" }, \"./components\": { types: \"./dist/src/components/index.d.ts\" }, \"./composables\": { types: \"./dist/src/composables/index.d.ts\" }, \"./types\": { types: \"./dist/src/types/index.d.ts\" }, \"./utils\": { types: \"./dist/src/utils/index.d.ts\" }, \"./*\": \"./*\" }, Tr = \"./dist/tres.js\", Sr = \"./dist/tres.js\", Ar = \"./dist/index.d.ts\", xr = [\"*.d.ts\", \"dist\"], kr = { access: \"public\" }, Lr = { dev: \"pnpm --filter='./playground/vue' dev\", \"dev:nuxt\": \"pnpm --filter='./playground/nuxt' dev\", build: \"vite build\", test: \"vitest\", \"test:ci\": \"vitest run\", \"test:ui\": \"vitest --ui --coverage.enabled=true\", release: \"release-it\", coverage: \"vitest run --coverage\", lint: \"eslint .\", \"lint:fix\": \"eslint . --fix\", \"docs:dev\": \"vitepress dev docs\", \"docs:build\": \"vitepress build docs\", \"docs:serve\": \"vitepress serve docs\", \"docs:preview\": \"vitepress preview docs\", \"docs:contributors\": \"esno scripts/update-contributors.ts\" }, Rr = { three: \">=0.133\", vue: \">=3.4\" }, Or = { \"@alvarosabu/utils\": \"^3.2.0\", \"@vue/devtools-api\": \"^6.6.3\", \"@vueuse/core\": \"^12.5.0\" }, Dr = { \"@release-it/conventional-changelog\": \"^10.0.0\", \"@stackblitz/sdk\": \"^1.11.0\", \"@tresjs/cientos\": \"4.1.0\", \"@tresjs/eslint-config\": \"^1.4.0\", \"@types/three\": \"^0.173.0\", \"@typescript-eslint/eslint-plugin\": \"^8.23.0\", \"@typescript-eslint/parser\": \"^8.23.0\", \"@vitejs/plugin-vue\": \"^5.2.1\", \"@vitest/coverage-c8\": \"^0.33.0\", \"@vitest/coverage-v8\": \"^3.0.5\", \"@vitest/ui\": \"^3.0.5\", \"@vue/test-utils\": \"^2.4.6\", eslint: \"^9.19.0\", \"eslint-plugin-vue\": \"^9.32.0\", esno: \"^4.8.0\", gsap: \"^3.12.7\", jsdom: \"^26.0.0\", kolorist: \"^1.8.0\", ohmyfetch: \"^0.4.21\", pathe: \"^2.0.2\", \"release-it\": \"^18.1.2\", \"rollup-plugin-analyzer\": \"^4.0.0\", \"rollup-plugin-copy\": \"^3.5.0\", \"rollup-plugin-visualizer\": \"^5.14.0\", sponsorkit: \"^16.3.0\", three: \"^0.173.0\", unocss: \"^65.4.3\", unplugin: \"^2.1.2\", \"unplugin-vue-components\": \"^28.0.0\", vite: \"^6.1.0\", \"vite-plugin-banner\": \"^0.8.0\", \"vite-plugin-dts\": \"4.5.0\", \"vite-plugin-inspect\": \"^10.1.0\", \"vite-plugin-require-transform\": \"^1.0.21\", \"vite-svg-loader\": \"^5.1.0\", vitepress: \"1.6.3\", vitest: \"3.0.5\", vue: \"3.5.13\", \"vue-demi\": \"^0.14.10\" }, jr = {\n  name: gr,\n  type: hr,\n  version: vr,\n  packageManager: yr,\n  description: _r,\n  author: wr,\n  license: br,\n  repository: Mr,\n  keywords: Pr,\n  sideEffects: Cr,\n  exports: Er,\n  main: Tr,\n  module: Sr,\n  types: Ar,\n  files: xr,\n  publishConfig: kr,\n  scripts: Lr,\n  peerDependencies: Rr,\n  dependencies: Or,\n  devDependencies: Dr\n};\nfunction Br(e) {\n  const t = { nodes: {}, materials: {} };\n  return e && e.traverse((r) => {\n    r.name && (t.nodes[r.name] = r), r.material && !t.materials[r.material.name] && (t.materials[r.material.name] = r.material);\n  }), t;\n}\nasync function Ir(e, t, r, n, o) {\n  const { logError: l } = Q(), s = new e();\n  return o && o(s), r && r(s), await new Promise((a, c) => {\n    s.load(\n      t,\n      (i) => {\n        const f = i;\n        f.scene && Object.assign(f, Br(f.scene)), a(f);\n      },\n      n,\n      (i) => {\n        l(\"[useLoader] - Failed to load resource\", i), c(i);\n      }\n    );\n  });\n}\nconst eo = /* @__PURE__ */ ue({\n  __name: \"component\",\n  props: {\n    loader: {},\n    url: {}\n  },\n  async setup(e) {\n    let t, r;\n    const n = e, o = ([t, r] = ot(() => Se(Ir(n.loader, n.url))), t = await t, r(), t);\n    return (l, s) => st(l.$slots, \"default\", { data: W(o) });\n  }\n});\nclass $r extends it.Mesh {\n  constructor(...r) {\n    super(...r);\n    de(this, \"type\", \"HightlightMesh\");\n    de(this, \"createTime\");\n    this.createTime = Date.now();\n  }\n  onBeforeRender() {\n    const n = (Date.now() - this.createTime) / 1e3, s = 1 + 0.07 * Math.sin(2.5 * n);\n    this.scale.set(s, s, s);\n  }\n}\nfunction be(e) {\n  return typeof e > \"u\";\n}\nfunction ke(e) {\n  return Array.isArray(e);\n}\nfunction Hr(e) {\n  return typeof e == \"number\";\n}\nfunction dt(e) {\n  return typeof e == \"string\";\n}\nfunction Y(e) {\n  return typeof e == \"function\";\n}\nfunction j(e) {\n  return e === Object(e) && !ke(e) && !Y(e);\n}\nfunction N(e) {\n  return j(e) && !!e.isObject3D;\n}\nfunction mt(e) {\n  return j(e) && !!e.isColor;\n}\nfunction Ur(e) {\n  return e != null && (typeof e == \"string\" || typeof e == \"number\" || mt(e));\n}\nfunction Me(e) {\n  return e !== null && typeof e == \"object\" && \"set\" in e && typeof e.set == \"function\";\n}\nfunction Fr(e) {\n  return Me(e) && \"copy\" in e && typeof e.copy == \"function\";\n}\nfunction Wr(e) {\n  return !!(e != null && e.constructor);\n}\nfunction Ge(e) {\n  return e instanceof Ut;\n}\nfunction Ve(e) {\n  return j(e) && !!e.isCamera;\n}\nfunction Nr(e) {\n  return j(e) && !!e.isBufferGeometry;\n}\nfunction zr(e) {\n  return j(e) && !!e.isMaterial;\n}\nfunction Gr(e) {\n  return j(e) && !!e.isLight;\n}\nfunction Vr(e) {\n  return j(e) && !!e.isFog;\n}\nfunction Yr(e) {\n  return j(e) && !!e.isScene;\n}\nfunction ne(e) {\n  return N(e) || Nr(e) || zr(e) || Vr(e);\n}\nfunction qr(e) {\n  return j(e) && !!e.isPrimitive;\n}\nconst gt = (e, t) => {\n  for (const r of Object.keys(t))\n    t[r] instanceof Object && Object.assign(t[r], gt(e[r], t[r]));\n  return Object.assign(e || {}, t), e;\n}, Kr = \"html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot\", Jr = /* @__PURE__ */ Zr(Kr);\nfunction Ye(e) {\n  return e && e.nodeType === 1;\n}\nfunction he(e) {\n  return e.replace(/-([a-z])/g, (t, r) => r.toUpperCase());\n}\nconst Qr = /\\B([A-Z])/g;\nfunction Xr(e) {\n  return e.replace(Qr, \"-$1\").toLowerCase();\n}\nfunction Zr(e, t) {\n  const r = /* @__PURE__ */ Object.create(null), n = e.split(\",\");\n  for (let o = 0; o < n.length; o++)\n    r[n[o]] = !0;\n  return t ? (o) => !!r[o.toLowerCase()] : (o) => !!r[o];\n}\nconst qe = (e, t) => {\n  if (!t)\n    return;\n  const r = Array.isArray(t) ? t : t.match(/([^[.\\]])+/g);\n  return r == null ? void 0 : r.reduce((n, o) => n && n[o], e);\n}, en = (e, t, r) => {\n  const n = Array.isArray(t) ? t : t.match(/([^[.\\]])+/g);\n  n && n.reduce((o, l, s) => (o[l] === void 0 && (o[l] = {}), s === n.length - 1 && (o[l] = r), o[l]), e);\n};\nfunction ht(e, t) {\n  if (Ye(e) && Ye(t)) {\n    const o = e.attributes, l = t.attributes;\n    return o.length !== l.length ? !1 : Array.from(o).every(({ name: s, value: a }) => t.getAttribute(s) === a);\n  }\n  if (e === t)\n    return !0;\n  if (e === null || typeof e != \"object\" || t === null || typeof t != \"object\")\n    return !1;\n  const r = Object.keys(e), n = Object.keys(t);\n  if (r.length !== n.length)\n    return !1;\n  for (const o of r)\n    if (!n.includes(o) || !ht(e[o], t[o]))\n      return !1;\n  return !0;\n}\nfunction tn(e, t) {\n  if (!Array.isArray(e) || !Array.isArray(t) || e.length !== t.length)\n    return !1;\n  for (let r = 0; r < e.length; r++)\n    if (!ht(e[r], t[r]))\n      return !1;\n  return !0;\n}\nconst rn = Array.isArray;\nfunction nn(e, t, r, n) {\n  const o = (c) => {\n    if (c.uuid === t)\n      return c;\n    for (const i of c.children) {\n      const f = o(i);\n      if (f)\n        return f;\n    }\n  }, l = o(e);\n  if (!l) {\n    console.warn(\"Object with UUID not found in the scene.\");\n    return;\n  }\n  let s = l;\n  for (let c = 0; c < r.length - 1; c++)\n    if (s[r[c]] !== void 0)\n      s = s[r[c]];\n    else {\n      console.warn(`Property path is not valid: ${r.join(\".\")}`);\n      return;\n    }\n  const a = r[r.length - 1];\n  s[a] !== void 0 ? s[a] = n : console.warn(`Property path is not valid: ${r.join(\".\")}`);\n}\nfunction on(e) {\n  const t = new ct({\n    color: 11003607,\n    // Highlight color, e.g., yellow\n    transparent: !0,\n    opacity: 0.2,\n    depthTest: !1,\n    // So the highlight is always visible\n    side: Ft\n    // To e\n  });\n  return new $r(e.geometry.clone(), t);\n}\nfunction sn(e) {\n  var r;\n  let t = e.value;\n  return e.value && ((r = e.value) != null && r.isMesh) && (t = e.value.position), Array.isArray(e.value) && (t = new xe(...t)), t;\n}\nfunction an(e) {\n  return \"map\" in e;\n}\nfunction Ke(e) {\n  an(e) && e.map && e.map.dispose(), e.dispose();\n}\nfunction vt(e) {\n  var r, n;\n  if (e.parent && ((r = e.removeFromParent) == null || r.call(e)), delete e.__tres, [...e.children].forEach((o) => vt(o)), !(e instanceof at)) {\n    const o = e;\n    e && ((n = e.dispose) == null || n.call(e)), o.geometry && o.geometry.dispose(), Array.isArray(o.material) ? o.material.forEach((l) => Ke(l)) : o.material && Ke(o.material);\n  }\n}\nfunction ln(e, t) {\n  let r = 0;\n  for (let n = 0; n < e.length; n++)\n    t(e[n], n) && (e[r] = e[n], r++);\n  return e.length = r, e;\n}\nfunction Pe(e, t) {\n  let r = e;\n  if (t.includes(\"-\")) {\n    const n = t.split(\"-\");\n    let o = n.shift();\n    for (; r && n.length; )\n      o in r ? (r = r[o], o = n.shift()) : o = Je(o, n.shift());\n    return { target: r, key: Je(o, ...n) };\n  } else\n    return { target: r, key: t };\n}\nfunction Je(...e) {\n  return e.map((t, r) => r === 0 ? t : t.charAt(0).toUpperCase() + t.slice(1)).join(\"\");\n}\nconst Qe = /-\\d+$/;\nfunction cn(e, t, r) {\n  if (dt(r)) {\n    if (Qe.test(r)) {\n      const l = r.replace(Qe, \"\"), { target: s, key: a } = Pe(e, l);\n      if (!Array.isArray(s[a])) {\n        const c = s[a], i = [];\n        i.__tresDetach = () => {\n          i.every((f) => be(f)) && (s[a] = c);\n        }, s[a] = i;\n      }\n    }\n    const { target: n, key: o } = Pe(e, r);\n    t.__tres.previousAttach = n[o], n[o] = J(t);\n  } else\n    t.__tres.previousAttach = r(e, t);\n}\nfunction un(e, t, r) {\n  var n, o, l;\n  if (dt(r)) {\n    const { target: s, key: a } = Pe(e, r), c = t.__tres.previousAttach;\n    c === void 0 ? delete s[a] : s[a] = c, \"__tresDetach\" in s && s.__tresDetach();\n  } else\n    (o = (n = t.__tres) == null ? void 0 : n.previousAttach) == null || o.call(n, e, t);\n  (l = t.__tres) == null || delete l.previousAttach;\n}\nfunction z(e, t, r) {\n  const n = e;\n  return n.__tres = {\n    type: \"unknown\",\n    eventCount: 0,\n    root: r,\n    handlers: {},\n    memoizedProps: {},\n    objects: [],\n    parent: null,\n    previousAttach: null,\n    ...t\n  }, n.__tres.attach || (n.isMaterial ? n.__tres.attach = \"material\" : n.isBufferGeometry ? n.__tres.attach = \"geometry\" : n.isFog && (n.__tres.attach = \"fog\")), n;\n}\nfunction yt(e) {\n  var r;\n  const t = (r = e == null ? void 0 : e.__tres) == null ? void 0 : r.root;\n  t && t.render && t.render.canBeInvalidated.value && t.invalidate();\n}\nfunction fn(e, t, r) {\n  var o;\n  if (!Y(e.setPixelRatio))\n    return;\n  let n = 0;\n  if (r && ke(r) && r.length >= 2) {\n    const [l, s] = r;\n    n = lt.clamp(t, l, s);\n  } else Hr(r) ? n = r : n = t;\n  n !== ((o = e.getPixelRatio) == null ? void 0 : o.call(e)) && e.setPixelRatio(n);\n}\nfunction pn(e, t, r, n, o) {\n  const l = [...t.__tres.objects], s = J(t);\n  if (e = J(e), s === e)\n    return !0;\n  const a = z(e, t.__tres ?? {}, o), c = t.parent ?? t.__tres.parent ?? null, i = { ...t.__tres.memoizedProps };\n  delete i.object;\n  for (const f of l)\n    _t(f, o), wt(f, o);\n  s.__tres.objects = [], n.remove(t);\n  for (const [f, v] of Object.entries(i))\n    n.patchProp(a, f, a[f], v);\n  r(e), n.insert(t, c);\n  for (const f of l)\n    n.insert(f, t);\n  return !0;\n}\nfunction J(e) {\n  return qr(e) ? (e.object.__tres = e.__tres, e.object) : e;\n}\nfunction _t(e, t) {\n  var n, o, l, s;\n  const r = ((n = e.__tres) == null ? void 0 : n.parent) || t.scene.value;\n  e.__tres && (e.__tres.parent = null), r && r.__tres && \"objects\" in r.__tres && ln(r.__tres.objects, (a) => a !== e), (o = e.__tres) != null && o.attach ? un(r, e, e.__tres.attach) : ((s = (l = e.parent) == null ? void 0 : l.remove) == null || s.call(l, J(e)), e.parent = null);\n}\nfunction wt(e, t) {\n  var r;\n  (r = e.traverse) == null || r.call(e, (n) => {\n    var o;\n    t.deregisterCamera(n), (o = t.eventManager) == null || o.deregisterPointerMissedObject(n);\n  }), t.deregisterCamera(e), yt(e);\n}\nasync function dn(e, t) {\n  const r = new Wt(t), n = (o) => new Promise((l, s) => {\n    r.load(\n      o,\n      (a) => l(a),\n      () => null,\n      () => {\n        s(new Error(\"[useTextures] - Failed to load texture\"));\n      }\n    );\n  });\n  if (rn(e)) {\n    const o = await Promise.all(e.map((l) => n(l)));\n    return e.length > 1 ? o : o[0];\n  } else {\n    const {\n      map: o,\n      displacementMap: l,\n      normalMap: s,\n      roughnessMap: a,\n      metalnessMap: c,\n      aoMap: i,\n      alphaMap: f,\n      matcap: v\n    } = e;\n    return {\n      map: o ? await n(o) : null,\n      displacementMap: l ? await n(l) : null,\n      normalMap: s ? await n(s) : null,\n      roughnessMap: a ? await n(a) : null,\n      metalnessMap: c ? await n(c) : null,\n      aoMap: i ? await n(i) : null,\n      alphaMap: f ? await n(f) : null,\n      matcap: v ? await n(v) : null\n    };\n  }\n}\nconst to = /* @__PURE__ */ ue({\n  __name: \"component\",\n  props: {\n    map: {},\n    displacementMap: {},\n    normalMap: {},\n    roughnessMap: {},\n    metalnessMap: {},\n    aoMap: {},\n    alphaMap: {},\n    matcap: {}\n  },\n  async setup(e) {\n    let t, r;\n    const n = e, o = ([t, r] = ot(() => Se(dn(n))), t = await t, r(), t);\n    return (l, s) => st(l.$slots, \"default\", { textures: W(o) });\n  }\n}), mn = ({ sizes: e }) => {\n  const t = $([]), r = q(\n    () => t.value[0]\n  ), n = (s) => {\n    const a = s instanceof Nt ? s : t.value.find((i) => i.uuid === s);\n    if (!a)\n      return;\n    const c = t.value.filter(({ uuid: i }) => i !== a.uuid);\n    t.value = [a, ...c];\n  }, o = (s, a = !1) => {\n    if (Ve(s)) {\n      const c = s;\n      if (t.value.some(({ uuid: i }) => i === c.uuid))\n        return;\n      a ? n(c) : t.value.push(c);\n    }\n  }, l = (s) => {\n    if (Ve(s)) {\n      const a = s;\n      t.value = t.value.filter(({ uuid: c }) => c !== a.uuid);\n    }\n  };\n  return Ae(() => {\n    e.aspectRatio.value && t.value.forEach((s) => {\n      !s.manual && (s instanceof we || gn(s)) && (s instanceof we ? s.aspect = e.aspectRatio.value : (s.left = e.width.value * -0.5, s.right = e.width.value * 0.5, s.top = e.height.value * 0.5, s.bottom = e.height.value * -0.5), s.updateProjectionMatrix());\n    });\n  }), K(() => {\n    t.value = [];\n  }), {\n    camera: r,\n    cameras: t,\n    registerCamera: o,\n    deregisterCamera: l,\n    setCameraActive: n\n  };\n};\nfunction gn(e) {\n  return e.hasOwnProperty(\"isOrthographicCamera\") && e.isOrthographicCamera;\n}\nconst ro = !0, fe = \"[TresJS ▲ ■ ●] \";\nfunction hn(...e) {\n  typeof e[0] == \"string\" ? e[0] = fe + e[0] : e.unshift(fe), console.error(...e);\n}\nfunction vn(...e) {\n  typeof e[0] == \"string\" ? e[0] = fe + e[0] : e.unshift(fe), console.warn(...e);\n}\nfunction yn(e, t) {\n}\nfunction Q() {\n  return {\n    logError: hn,\n    logWarning: vn,\n    logMessage: yn\n  };\n}\nconst Ce = $({}), Ee = (e) => Object.assign(Ce.value, e);\nfunction ve() {\n  const e = /* @__PURE__ */ new Map(), t = /* @__PURE__ */ new Set();\n  let r = 0, n = !1;\n  const o = () => {\n    const i = Array.from(e.entries()).sort((f, v) => {\n      const u = f[1].priority - v[1].priority;\n      return u === 0 ? f[1].addI - v[1].addI : u;\n    });\n    t.clear(), i.forEach((f) => t.add(f[0]));\n  }, l = (i) => {\n    e.delete(i), t.delete(i);\n  };\n  return { on: (i, f = 0) => {\n    e.set(i, { priority: f, addI: r++ });\n    const v = () => l(i);\n    return ir(v), n = !0, {\n      off: v\n    };\n  }, off: l, trigger: (...i) => {\n    n && (o(), n = !1), t.forEach((f) => f(...i));\n  }, dispose: () => {\n    e.clear(), t.clear();\n  }, get count() {\n    return e.size;\n  } };\n}\nfunction _n() {\n  let e = !0, t = !0, r = !1;\n  const n = new ut(!1), o = $(n.running), l = $(!1);\n  let s;\n  const a = lt.generateUUID();\n  let c = null;\n  const i = ve(), f = ve(), v = ve();\n  S();\n  let u = {};\n  function _(M) {\n    u = M;\n  }\n  function d(M, x, m = 0) {\n    switch (x) {\n      case \"before\":\n        return i.on(M, m);\n      case \"render\":\n        return c || (c = M), f.dispose(), f.on(M);\n      case \"after\":\n        return v.on(M, m);\n    }\n  }\n  function y() {\n    t && (t = !1, S(), E());\n  }\n  function p() {\n    t = !0, S(), cancelAnimationFrame(s);\n  }\n  function h() {\n    r = !1, S();\n  }\n  function P() {\n    r = !0, S();\n  }\n  function C() {\n    l.value = !0;\n  }\n  function b() {\n    l.value = !1;\n  }\n  function E() {\n    if (!e) {\n      s = requestAnimationFrame(E);\n      return;\n    }\n    const M = n.getDelta(), x = n.getElapsedTime(), m = {\n      camera: W(u.camera),\n      scene: W(u.scene),\n      renderer: W(u.renderer),\n      raycaster: W(u.raycaster),\n      controls: W(u.controls),\n      invalidate: u.invalidate,\n      advance: u.advance\n    }, w = { delta: M, elapsed: x, clock: n, ...m };\n    o.value && i.trigger(w), l.value || (f.count ? f.trigger(w) : c && c(w)), o.value && v.trigger(w), s = requestAnimationFrame(E);\n  }\n  function S() {\n    const M = !t && !r;\n    n.running !== M && (n.running ? n.stop() : n.start()), o.value = n.running;\n  }\n  return {\n    loopId: a,\n    register: (M, x, m) => d(M, x, m),\n    start: y,\n    stop: p,\n    pause: P,\n    resume: h,\n    pauseRender: C,\n    resumeRender: b,\n    isRenderPaused: l,\n    isActive: o,\n    setContext: _,\n    setReady: (M) => e = M\n  };\n}\nfunction Le(e) {\n  let t = 0;\n  return e.traverse((r) => {\n    if (r.isMesh && r.geometry && r.type !== \"HightlightMesh\") {\n      const n = r.geometry, o = n.attributes.position.count * 3 * Float32Array.BYTES_PER_ELEMENT, l = n.index ? n.index.count * Uint32Array.BYTES_PER_ELEMENT : 0, s = n.attributes.normal ? n.attributes.normal.count * 3 * Float32Array.BYTES_PER_ELEMENT : 0, a = n.attributes.uv ? n.attributes.uv.count * 2 * Float32Array.BYTES_PER_ELEMENT : 0, c = o + l + s + a;\n      t += c;\n    }\n  }), t;\n}\nfunction wn(e) {\n  return (e / 1024).toFixed(2);\n}\nconst bn = Number.parseInt(zt.replace(\"dev\", \"\"));\nfunction no(e) {\n  return typeof e == \"number\" ? [e, e, e] : e instanceof xe ? [e.x, e.y, e.z] : e;\n}\nfunction Mn(e) {\n  return e instanceof Z ? e : Array.isArray(e) ? new Z(...e) : new Z(e);\n}\nconst oe = {\n  realistic: {\n    shadows: !0,\n    physicallyCorrectLights: !0,\n    outputColorSpace: Yt,\n    toneMapping: ft,\n    toneMappingExposure: 3,\n    shadowMap: {\n      enabled: !0,\n      type: Vt\n    }\n  },\n  flat: {\n    toneMapping: Gt,\n    toneMappingExposure: 1\n  }\n};\nfunction Pn({\n  canvas: e,\n  options: t,\n  contextParts: { sizes: r, render: n, invalidate: o, advance: l }\n}) {\n  const s = q(() => ({\n    alpha: A(t.alpha) ?? !0,\n    depth: A(t.depth),\n    canvas: ar(e),\n    context: A(t.context),\n    stencil: A(t.stencil),\n    antialias: A(t.antialias) ?? !0,\n    precision: A(t.precision),\n    powerPreference: A(t.powerPreference),\n    premultipliedAlpha: A(t.premultipliedAlpha),\n    preserveDrawingBuffer: A(t.preserveDrawingBuffer),\n    logarithmicDepthBuffer: A(t.logarithmicDepthBuffer),\n    failIfMajorPerformanceCaveat: A(t.failIfMajorPerformanceCaveat)\n  })), a = H(new me(s.value));\n  function c() {\n    t.renderMode === \"on-demand\" && o();\n  }\n  ie(s, () => {\n    a.value.dispose(), a.value = new me(s.value), c();\n  }), ie([r.width, r.height], () => {\n    a.value.setSize(r.width.value, r.height.value), c();\n  }, {\n    immediate: !0\n  }), ie(() => t.clearColor, c);\n  const { pixelRatio: i } = lr(), { logError: f } = Q(), u = (() => {\n    const d = new me(), y = {\n      shadowMap: {\n        enabled: d.shadowMap.enabled,\n        type: d.shadowMap.type\n      },\n      toneMapping: d.toneMapping,\n      toneMappingExposure: d.toneMappingExposure,\n      outputColorSpace: d.outputColorSpace\n    };\n    return d.dispose(), y;\n  })(), _ = A(t.renderMode);\n  return _ === \"on-demand\" && o(), _ === \"manual\" && setTimeout(() => {\n    l();\n  }, 100), Ae(() => {\n    const d = A(t.preset);\n    d && (d in oe || f(`Renderer Preset must be one of these: ${Object.keys(oe).join(\", \")}`), gt(a.value, oe[d])), fn(a.value, i.value, A(t.dpr)), _ === \"always\" && (n.frames.value = Math.max(1, n.frames.value));\n    const y = (P, C) => {\n      const b = A(P), E = () => {\n        if (d)\n          return qe(oe[d], C);\n      };\n      if (b !== void 0)\n        return b;\n      const S = E();\n      return S !== void 0 ? S : qe(u, C);\n    }, p = (P, C) => en(a.value, C, y(P, C));\n    p(t.shadows, \"shadowMap.enabled\"), p(t.toneMapping ?? ft, \"toneMapping\"), p(t.shadowMapType, \"shadowMap.type\"), bn < 150 && p(!t.useLegacyLights, \"physicallyCorrectLights\"), p(t.outputColorSpace, \"outputColorSpace\"), p(t.toneMappingExposure, \"toneMappingExposure\");\n    const h = y(t.clearColor, \"clearColor\");\n    h && a.value.setClearColor(\n      h ? Mn(h) : new Z(0)\n      // default clear color is not easily/efficiently retrievable from three\n    );\n  }), K(() => {\n    a.value.dispose(), a.value.forceContextLoss();\n  }), {\n    renderer: a\n  };\n}\nfunction Cn(e, t, r = 10) {\n  const n = A(e) ? cr() : ur(q(() => A(t).parentElement)), o = _e(ze(n.width, r)), l = _e(ze(n.height, r)), s = q(() => o.value / l.value);\n  return {\n    height: l,\n    width: o,\n    aspectRatio: s\n  };\n}\nconst En = (e, t) => {\n  const r = q(() => t.renderer.value.domElement), n = H([]), { x: o, y: l } = fr({ target: r });\n  let s = 0;\n  const { width: a, height: c, top: i, left: f } = pr(r), v = ({ x: g, y: T }) => {\n    if (r.value)\n      return {\n        x: (g - f.value) / a.value * 2 - 1,\n        y: -((T - i.value) / c.value) * 2 + 1\n      };\n  }, u = ({ x: g, y: T }) => {\n    if (t.camera.value)\n      return t.raycaster.value.setFromCamera(new ge(g, T), t.camera.value), n.value = t.raycaster.value.intersectObjects(e.value, !0), n.value;\n  }, _ = (g) => {\n    const T = v({\n      x: (g == null ? void 0 : g.clientX) ?? o.value,\n      y: (g == null ? void 0 : g.clientY) ?? l.value\n    });\n    return T ? u(T) || [] : [];\n  }, d = D(), y = D(), p = D(), h = D(), P = D(), C = D(), b = D(), E = D();\n  function S(g) {\n    const T = {};\n    for (const U in g)\n      typeof U != \"function\" && (T[U] = g[U]);\n    return T;\n  }\n  const M = (g, T) => {\n    var Ie, $e, He;\n    const U = S(T), re = new xe(T == null ? void 0 : T.clientX, T == null ? void 0 : T.clientY, 0).unproject((Ie = t.camera) == null ? void 0 : Ie.value);\n    g.trigger({\n      ...U,\n      intersections: n.value,\n      // The unprojectedPoint is wrong, math needs to be fixed\n      unprojectedPoint: re,\n      ray: ($e = t.raycaster) == null ? void 0 : $e.value.ray,\n      camera: (He = t.camera) == null ? void 0 : He.value,\n      sourceEvent: T,\n      delta: s,\n      stopPropagating: !1\n    });\n  };\n  let x;\n  const m = (g) => {\n    _(g), M(p, g), x = g;\n  }, w = () => {\n    x && m(x);\n  };\n  let k, L, R;\n  const B = (g) => {\n    var T;\n    k = (T = n.value[0]) == null ? void 0 : T.object, s = 0, L = new ge(\n      (g == null ? void 0 : g.clientX) ?? o.value,\n      (g == null ? void 0 : g.clientY) ?? l.value\n    ), M(P, g);\n  };\n  let O, G = !1;\n  const Oe = (g) => {\n    var T, U, re;\n    g instanceof PointerEvent && (n.value.length === 0 && M(C, g), k === ((T = n.value[0]) == null ? void 0 : T.object) && (R = new ge(\n      (g == null ? void 0 : g.clientX) ?? o.value,\n      (g == null ? void 0 : g.clientY) ?? l.value\n    ), s = L == null ? void 0 : L.distanceTo(R), g.button === 0 ? (M(d, g), O === ((U = n.value[0]) == null ? void 0 : U.object) ? G = !0 : (O = (re = n.value[0]) == null ? void 0 : re.object, G = !1)) : g.button === 2 && M(b, g)), M(h, g));\n  }, De = (g) => {\n    G && (M(y, g), O = void 0, G = !1);\n  }, je = (g) => M(p, g), Be = (g) => M(E, g);\n  return r.value.addEventListener(\"pointerup\", Oe), r.value.addEventListener(\"pointerdown\", B), r.value.addEventListener(\"pointermove\", m), r.value.addEventListener(\"pointerleave\", je), r.value.addEventListener(\"dblclick\", De), r.value.addEventListener(\"wheel\", Be), K(() => {\n    r != null && r.value && (r.value.removeEventListener(\"pointerup\", Oe), r.value.removeEventListener(\"pointerdown\", B), r.value.removeEventListener(\"pointermove\", m), r.value.removeEventListener(\"pointerleave\", je), r.value.removeEventListener(\"dblclick\", De), r.value.removeEventListener(\"wheel\", Be));\n  }), {\n    intersects: n,\n    onClick: (g) => d.on(g).off,\n    onDblClick: (g) => y.on(g).off,\n    onContextMenu: (g) => b.on(g).off,\n    onPointerMove: (g) => p.on(g).off,\n    onPointerUp: (g) => h.on(g).off,\n    onPointerDown: (g) => P.on(g).off,\n    onPointerMissed: (g) => C.on(g).off,\n    onWheel: (g) => E.on(g).off,\n    forceUpdate: w\n  };\n};\nfunction ye(e, t) {\n  if (Array.isArray(e))\n    for (const r of e)\n      r(t);\n  typeof e == \"function\" && e(t);\n}\nfunction Tn(e, t, r) {\n  var x;\n  const n = H(), o = H();\n  e && (n.value = e), t && (o.value = t);\n  const l = (m) => {\n    var w;\n    return ((w = m.__tres) == null ? void 0 : w.eventCount) > 0;\n  }, s = (m) => {\n    var w;\n    return ((w = m.children) == null ? void 0 : w.some((k) => s(k))) || l(m);\n  }, a = H(((x = n.value) == null ? void 0 : x.children).filter(s) || []);\n  function c(m, w) {\n    const k = [], L = () => w.stopPropagating = !0;\n    w.stopPropagation = L;\n    for (const R of w == null ? void 0 : w.intersections) {\n      if (w.stopPropagating)\n        return;\n      w = { ...w, ...R };\n      const { object: B } = R;\n      w.eventObject = B, ye(B[m], w), k.push(B);\n      let O = B.parent;\n      for (; O !== null && !w.stopPropagating && !k.includes(O); )\n        w.eventObject = O, ye(O[m], w), k.push(O), O = O.parent;\n      const G = Xr(m.slice(2));\n      r(G, { intersection: R, event: w });\n    }\n  }\n  const {\n    onClick: i,\n    onDblClick: f,\n    onContextMenu: v,\n    onPointerMove: u,\n    onPointerDown: _,\n    onPointerUp: d,\n    onPointerMissed: y,\n    onWheel: p,\n    forceUpdate: h\n  } = En(a, t);\n  d((m) => c(\"onPointerUp\", m)), _((m) => c(\"onPointerDown\", m)), i((m) => c(\"onClick\", m)), f((m) => c(\"onDoubleClick\", m)), v((m) => c(\"onContextMenu\", m)), p((m) => c(\"onWheel\", m));\n  let P = [];\n  u((m) => {\n    const w = m.intersections.map(({ object: L }) => L), k = m.intersections;\n    P.forEach(({ object: L }) => {\n      w.includes(L) || (m.intersections = P, c(\"onPointerLeave\", m), c(\"onPointerOut\", m));\n    }), m.intersections = k, m.intersections.forEach(({ object: L }) => {\n      P.includes(L) || (c(\"onPointerEnter\", m), c(\"onPointerOver\", m));\n    }), c(\"onPointerMove\", m), P = m.intersections;\n  });\n  const C = [];\n  y((m) => {\n    const w = () => m.stopPropagating = !0;\n    m.stopPropagation = w, C.forEach((k) => {\n      m.stopPropagating || (m.eventObject = k, ye(k.onPointerMissed, m));\n    }), r(\"pointer-missed\", { event: m });\n  });\n  function b(m) {\n    ne(m) && N(m) && a.value.push(m);\n  }\n  function E(m) {\n    if (ne(m) && N(m)) {\n      const w = a.value.indexOf(m);\n      w > -1 && a.value.splice(w, 1);\n    }\n  }\n  function S(m) {\n    ne(m) && N(m) && m.onPointerMissed && C.push(m);\n  }\n  function M(m) {\n    if (ne(m) && N(m)) {\n      const w = C.indexOf(m);\n      w > -1 && C.splice(w, 1);\n    }\n  }\n  return t.eventManager = {\n    forceUpdate: h,\n    registerObject: b,\n    deregisterObject: E,\n    registerPointerMissedObject: S,\n    deregisterPointerMissedObject: M\n  }, {\n    forceUpdate: h,\n    registerObject: b,\n    deregisterObject: E,\n    registerPointerMissedObject: S,\n    deregisterPointerMissedObject: M\n  };\n}\nfunction Sn(e, t, r = 100) {\n  r = r <= 0 ? 100 : r;\n  const n = D(), o = /* @__PURE__ */ new Set();\n  let l = !1, s = !1, a = null;\n  function c() {\n    a && clearTimeout(a), !s && !l && e() ? (n.trigger(t), o.forEach((u) => u()), o.clear(), l = !0) : !s && !l && (a = setTimeout(c, r));\n  }\n  function i() {\n    s = !0, a && clearTimeout(a);\n  }\n  c();\n  const f = (u, ..._) => {\n    u(..._);\n  };\n  return {\n    on: (u) => {\n      if (l)\n        return f(u, t), { off: () => {\n        } };\n      {\n        const _ = n.on(u);\n        return o.add(_.off), n.on(u);\n      }\n    },\n    off: n.off,\n    trigger: n.trigger,\n    clear: n.clear,\n    cancel: i\n  };\n}\nconst ee = /* @__PURE__ */ new WeakMap();\nfunction bt(e) {\n  if (e = e || pe(), ee.has(e))\n    return ee.get(e);\n  const t = 100, r = Date.now(), l = Sn(() => {\n    if (Date.now() - r >= t)\n      return !0;\n    {\n      const s = e.renderer.value, a = (s == null ? void 0 : s.domElement) || { width: 0, height: 0 };\n      return !!(s && a.width > 0 && a.height > 0);\n    }\n  }, e);\n  return ee.set(e, l), l;\n}\nfunction oo(e) {\n  const t = pe();\n  if (t)\n    return ee.has(t) ? ee.get(t).on(e) : bt(t).on(e);\n}\nfunction An({\n  scene: e,\n  canvas: t,\n  windowSize: r,\n  rendererOptions: n,\n  emit: o\n}) {\n  const l = H(e), s = Cn(r, t), {\n    camera: a,\n    cameras: c,\n    registerCamera: i,\n    deregisterCamera: f,\n    setCameraActive: v\n  } = mn({ sizes: s }), u = {\n    mode: $(n.renderMode || \"always\"),\n    priority: $(0),\n    frames: $(0),\n    maxFrames: 60,\n    canBeInvalidated: q(() => u.mode.value === \"on-demand\" && u.frames.value === 0)\n  };\n  function _(R = 1) {\n    n.renderMode === \"on-demand\" && (u.frames.value = Math.min(u.maxFrames, u.frames.value + R));\n  }\n  function d() {\n    n.renderMode === \"manual\" && (u.frames.value = 1);\n  }\n  const { renderer: y } = Pn(\n    {\n      canvas: t,\n      options: n,\n      // TODO: replace contextParts with full ctx at https://github.com/Tresjs/tres/issues/516\n      contextParts: { sizes: s, render: u, invalidate: _, advance: d }\n    }\n  ), p = {\n    sizes: s,\n    scene: l,\n    camera: a,\n    cameras: _e(c),\n    renderer: y,\n    raycaster: H(new qt()),\n    controls: $(null),\n    perf: {\n      maxFrames: 160,\n      fps: {\n        value: 0,\n        accumulator: []\n      },\n      memory: {\n        currentMem: 0,\n        allocatedMem: 0,\n        accumulator: []\n      }\n    },\n    render: u,\n    advance: d,\n    extend: Ee,\n    invalidate: _,\n    registerCamera: i,\n    setCameraActive: v,\n    deregisterCamera: f,\n    loop: _n()\n  };\n  ae(\"useTres\", p), p.scene.value.__tres = {\n    root: p\n  }, p.loop.register(() => {\n    a.value && u.frames.value > 0 && (y.value.render(e, a.value), o(\"render\", p.renderer.value)), u.priority.value = 0, u.mode.value === \"always\" ? u.frames.value = 1 : u.frames.value = Math.max(0, u.frames.value - 1);\n  }, \"render\");\n  const { on: h, cancel: P } = bt(p);\n  p.loop.setReady(!1), p.loop.start(), h(() => {\n    o(\"ready\", p), p.loop.setReady(!0), Tn(e, p, o);\n  }), K(() => {\n    P(), p.loop.stop();\n  });\n  const C = 100, b = dr({ every: C }), { isSupported: E, memory: S } = mr({ interval: C }), M = 160;\n  let x = performance.now();\n  const m = ({ timestamp: R }) => {\n    p.scene.value && (p.perf.memory.allocatedMem = Le(p.scene.value)), R - x >= C && (x = R, p.perf.fps.accumulator.push(b.value), p.perf.fps.accumulator.length > M && p.perf.fps.accumulator.shift(), p.perf.fps.value = b.value, E.value && S.value && (p.perf.memory.accumulator.push(S.value.usedJSHeapSize / 1024 / 1024), p.perf.memory.accumulator.length > M && p.perf.memory.accumulator.shift(), p.perf.memory.currentMem = p.perf.memory.accumulator.reduce((B, O) => B + O, 0) / p.perf.memory.accumulator.length));\n  };\n  let w = 0;\n  const k = 1, { pause: L } = pt(({ delta: R }) => {\n    window.__TRES__DEVTOOLS__ && (m({ timestamp: performance.now() }), w += R, w >= k && (window.__TRES__DEVTOOLS__.cb(p), w = 0));\n  }, { immediate: !0 });\n  return K(() => {\n    L();\n  }), p;\n}\nfunction pe() {\n  const e = kt(\"useTres\");\n  if (!e)\n    throw new Error(\"useTresContext must be used together with useTresContextProvider\");\n  return e;\n}\nconst so = pe;\nfunction io() {\n  const {\n    camera: e,\n    scene: t,\n    renderer: r,\n    loop: n,\n    raycaster: o,\n    controls: l,\n    invalidate: s,\n    advance: a\n  } = pe();\n  n.setContext({\n    camera: e,\n    scene: t,\n    renderer: r,\n    raycaster: o,\n    controls: l,\n    invalidate: s,\n    advance: a\n  });\n  function c(v, u = 0) {\n    return n.register(v, \"before\", u);\n  }\n  function i(v) {\n    return n.register(v, \"render\");\n  }\n  function f(v, u = 0) {\n    return n.register(v, \"after\", u);\n  }\n  return {\n    pause: n.pause,\n    resume: n.resume,\n    pauseRender: n.pauseRender,\n    resumeRender: n.resumeRender,\n    isActive: n.isActive,\n    onBeforeRender: c,\n    render: i,\n    onAfterRender: f\n  };\n}\nconst Mt = D(), Pt = D(), Re = D(), te = new ut();\nlet le = 0, ce = 0;\nconst { pause: xn, resume: Xe, isActive: kn } = pt(\n  () => {\n    Mt.trigger({ delta: le, elapsed: ce, clock: te }), Pt.trigger({ delta: le, elapsed: ce, clock: te }), Re.trigger({ delta: le, elapsed: ce, clock: te });\n  },\n  { immediate: !1 }\n);\nRe.on(() => {\n  le = te.getDelta(), ce = te.getElapsedTime();\n});\nlet Ze = !1;\nconst ao = () => (Ze || (Ze = !0, Xe()), {\n  onBeforeLoop: Mt.on,\n  onLoop: Pt.on,\n  onAfterLoop: Re.on,\n  pause: xn,\n  resume: Xe,\n  isActive: kn\n});\nfunction lo() {\n  const { logWarning: e } = Q();\n  function t(l, s, a) {\n    let c = null;\n    return l.traverse((i) => {\n      i[s] === a && (c = i);\n    }), c || e(`Child with ${s} '${a}' not found.`), c;\n  }\n  function r(l, s, a) {\n    const c = [];\n    return l.traverse((i) => {\n      i[s].includes(a) && c.push(i);\n    }), c.length || e(`Children with ${s} '${a}' not found.`), c;\n  }\n  function n(l, s) {\n    return t(l, \"name\", s);\n  }\n  function o(l, s) {\n    return r(l, \"name\", s);\n  }\n  return {\n    seek: t,\n    seekByName: n,\n    seekAll: r,\n    seekAllByName: o\n  };\n}\nfunction Ln(e, t = {}, r = {}) {\n  let n = e;\n  const o = (a) => {\n    n = a;\n  };\n  let l = new Proxy({}, {});\n  const s = {\n    has(a, c) {\n      return c in t || c in n;\n    },\n    get(a, c, i) {\n      return c in t ? t[c](n) : n[c];\n    },\n    set(a, c, i) {\n      return r[c] ? r[c](i, n, l, o) : n[c] = i, !0;\n    }\n  };\n  return l = new Proxy({}, s), l;\n}\nconst { logError: et } = Q(), tt = [\n  \"onClick\",\n  \"onContextMenu\",\n  \"onPointerMove\",\n  \"onPointerEnter\",\n  \"onPointerLeave\",\n  \"onPointerOver\",\n  \"onPointerOut\",\n  \"onDoubleClick\",\n  \"onPointerDown\",\n  \"onPointerUp\",\n  \"onPointerCancel\",\n  \"onPointerMissed\",\n  \"onLostPointerCapture\",\n  \"onWheel\"\n], Rn = (e) => {\n  const t = e.scene.value;\n  function r(i, f, v, u) {\n    if (u || (u = {}), u.args || (u.args = []), i === \"template\" || Jr(i))\n      return null;\n    let _ = i.replace(\"Tres\", \"\"), d;\n    if (i === \"primitive\") {\n      (!j(u.object) || Lt(u.object)) && et(\n        \"Tres primitives need an 'object' prop, whose value is an object or shallowRef<object>\"\n      ), _ = u.object.type;\n      const y = {};\n      d = Ln(\n        u.object,\n        {\n          object: (h) => h,\n          isPrimitive: () => !0,\n          __tres: () => y\n        },\n        {\n          object: (h, P, C, b) => {\n            pn(h, C, b, { patchProp: l, remove: o, insert: n }, e);\n          },\n          __tres: (h) => {\n            Object.assign(y, h);\n          }\n        }\n      );\n    } else {\n      const y = Ce.value[_];\n      y || et(\n        `${_} is not defined on the THREE namespace. Use extend to add it to the catalog.`\n      ), d = new y(...u.args);\n    }\n    return d ? (d.isCamera && (u != null && u.position || d.position.set(3, 3, 3), u != null && u.lookAt || d.lookAt(0, 0, 0)), d = z(d, {\n      ...d.__tres,\n      type: _,\n      memoizedProps: u,\n      eventCount: 0,\n      primitive: i === \"primitive\",\n      attach: u.attach\n    }, e), d) : null;\n  }\n  function n(i, f) {\n    var _, d, y;\n    if (!i)\n      return;\n    f = f || t;\n    const v = i.__tres ? i : z(i, {}, e), u = f.__tres ? f : z(f, {}, e);\n    i = J(v), f = J(u), i.__tres && ((_ = i.__tres) == null ? void 0 : _.eventCount) > 0 && ((d = e.eventManager) == null || d.registerObject(i)), e.registerCamera(i), (y = e.eventManager) == null || y.registerPointerMissedObject(i), v.__tres.attach ? cn(u, v, v.__tres.attach) : N(i) && N(u) && (u.add(i), i.dispatchEvent({ type: \"added\" })), v.__tres.parent = u, u.__tres.objects && !u.__tres.objects.includes(v) && u.__tres.objects.push(v);\n  }\n  function o(i, f) {\n    var d, y, p, h;\n    if (!i)\n      return;\n    i != null && i.__tres && ((d = i.__tres) == null ? void 0 : d.eventCount) > 0 && ((y = e.eventManager) == null || y.deregisterObject(i)), f = be(f) ? \"default\" : f;\n    const v = (p = i.__tres) == null ? void 0 : p.dispose;\n    be(v) || (v === null ? f = !1 : f = v);\n    const u = (h = i.__tres) == null ? void 0 : h.primitive, _ = f === \"default\" ? !u : !!f;\n    if (i.__tres && \"objects\" in i.__tres && [...i.__tres.objects].forEach((P) => o(P, f)), _ && i.children && [...i.children].forEach((P) => o(P, f)), _t(i, e), wt(i, e), _ && !Yr(i)) {\n      if (Y(f))\n        f(i);\n      else if (Y(i.dispose))\n        try {\n          i.dispose();\n        } catch {\n        }\n    }\n    \"__tres\" in i && delete i.__tres;\n  }\n  function l(i, f, v, u) {\n    var P, C;\n    if (!i)\n      return;\n    let _ = i, d = f;\n    if (i.__tres && (i.__tres.memoizedProps[f] = u), f === \"attach\") {\n      const b = ((P = i.__tres) == null ? void 0 : P.parent) || i.parent;\n      o(i), z(i, { attach: u }, e), b && n(i, b);\n      return;\n    }\n    if (f === \"dispose\") {\n      i.__tres || (i = z(i, {}, e)), i.__tres.dispose = u;\n      return;\n    }\n    if (N(i) && d === \"blocks-pointer-events\") {\n      u || u === \"\" ? i[d] = u : delete i[d];\n      return;\n    }\n    tt.includes(f) && i.__tres && (i.__tres.eventCount += 1);\n    let y = he(d), p = _ == null ? void 0 : _[y];\n    if (d === \"args\") {\n      const b = i, E = v ?? [], S = u ?? [], M = ((C = i.__tres) == null ? void 0 : C.type) || i.type;\n      M && E.length && !tn(E, S) && (_ = Object.assign(\n        b,\n        new Ce.value[M](...u)\n      ));\n      return;\n    }\n    if (_.type === \"BufferGeometry\") {\n      if (d === \"args\")\n        return;\n      _.setAttribute(\n        he(d),\n        new Jt(...u)\n      );\n      return;\n    }\n    if (d.includes(\"-\") && p === void 0) {\n      p = _;\n      for (const b of d.split(\"-\"))\n        y = d = he(b), _ = p, p = p == null ? void 0 : p[d];\n    }\n    let h = u;\n    if (h === \"\" && (h = !0), Y(p)) {\n      tt.includes(f) || (ke(h) ? i[y](...h) : i[y](h)), y.startsWith(\"on\") && Y(h) && (_[y] = h);\n      return;\n    }\n    Ge(p) && Ge(h) ? p.mask = h.mask : mt(p) && Ur(h) ? p.set(h) : Fr(p) && Wr(h) && p.constructor === h.constructor ? p.copy(h) : Me(p) && Array.isArray(h) ? \"fromArray\" in p && typeof p.fromArray == \"function\" ? p.fromArray(h) : p.set(...h) : Me(p) && typeof h == \"number\" ? \"setScalar\" in p && typeof p.setScalar == \"function\" ? p.setScalar(h) : p.set(h) : _[y] = h, yt(i);\n  }\n  function s(i) {\n    var f;\n    return ((f = i == null ? void 0 : i.__tres) == null ? void 0 : f.parent) || null;\n  }\n  function a(i) {\n    const f = z(new Kt(), { type: \"Comment\" }, e);\n    return f.name = i, f;\n  }\n  function c(i) {\n    var _;\n    const f = s(i), v = ((_ = f == null ? void 0 : f.__tres) == null ? void 0 : _.objects) || [], u = v.indexOf(i);\n    return u < 0 || u >= v.length - 1 ? null : v[u + 1];\n  }\n  return {\n    insert: n,\n    remove: o,\n    createElement: r,\n    patchProp: l,\n    parentNode: s,\n    createText: () => void 0,\n    createComment: a,\n    setText: () => void 0,\n    setElementText: () => void 0,\n    nextSibling: c,\n    querySelector: () => void 0,\n    setScopeId: () => void 0,\n    cloneNode: () => void 0,\n    insertStaticContent: () => void 0\n  };\n};\nfunction On() {\n  return Ct().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nfunction Ct() {\n  return typeof navigator < \"u\" && typeof window < \"u\" ? window : typeof globalThis < \"u\" ? globalThis : {};\n}\nconst Dn = typeof Proxy == \"function\", jn = \"devtools-plugin:setup\", Bn = \"plugin:settings:set\";\nlet V, Te;\nfunction In() {\n  var e;\n  return V !== void 0 || (typeof window < \"u\" && window.performance ? (V = !0, Te = window.performance) : typeof globalThis < \"u\" && (!((e = globalThis.perf_hooks) === null || e === void 0) && e.performance) ? (V = !0, Te = globalThis.perf_hooks.performance) : V = !1), V;\n}\nfunction $n() {\n  return In() ? Te.now() : Date.now();\n}\nclass Hn {\n  constructor(t, r) {\n    this.target = null, this.targetQueue = [], this.onQueue = [], this.plugin = t, this.hook = r;\n    const n = {};\n    if (t.settings)\n      for (const s in t.settings) {\n        const a = t.settings[s];\n        n[s] = a.defaultValue;\n      }\n    const o = `__vue-devtools-plugin-settings__${t.id}`;\n    let l = Object.assign({}, n);\n    try {\n      const s = localStorage.getItem(o), a = JSON.parse(s);\n      Object.assign(l, a);\n    } catch {\n    }\n    this.fallbacks = {\n      getSettings() {\n        return l;\n      },\n      setSettings(s) {\n        try {\n          localStorage.setItem(o, JSON.stringify(s));\n        } catch {\n        }\n        l = s;\n      },\n      now() {\n        return $n();\n      }\n    }, r && r.on(Bn, (s, a) => {\n      s === this.plugin.id && this.fallbacks.setSettings(a);\n    }), this.proxiedOn = new Proxy({}, {\n      get: (s, a) => this.target ? this.target.on[a] : (...c) => {\n        this.onQueue.push({\n          method: a,\n          args: c\n        });\n      }\n    }), this.proxiedTarget = new Proxy({}, {\n      get: (s, a) => this.target ? this.target[a] : a === \"on\" ? this.proxiedOn : Object.keys(this.fallbacks).includes(a) ? (...c) => (this.targetQueue.push({\n        method: a,\n        args: c,\n        resolve: () => {\n        }\n      }), this.fallbacks[a](...c)) : (...c) => new Promise((i) => {\n        this.targetQueue.push({\n          method: a,\n          args: c,\n          resolve: i\n        });\n      })\n    });\n  }\n  async setRealTarget(t) {\n    this.target = t;\n    for (const r of this.onQueue)\n      this.target.on[r.method](...r.args);\n    for (const r of this.targetQueue)\n      r.resolve(await this.target[r.method](...r.args));\n  }\n}\nfunction Un(e, t) {\n  const r = e, n = Ct(), o = On(), l = Dn && r.enableEarlyProxy;\n  if (o && (n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !l))\n    o.emit(jn, e, t);\n  else {\n    const s = l ? new Hn(r, o) : null;\n    (n.__VUE_DEVTOOLS_PLUGINS__ = n.__VUE_DEVTOOLS_PLUGINS__ || []).push({\n      pluginDescriptor: r,\n      setupFn: t,\n      proxy: s\n    }), s && t(s.proxiedTarget);\n  }\n}\nfunction Fn(e, t) {\n  const r = `▲ ■ ●${e}`;\n  typeof rt == \"function\" ? rt(r, t) : console.log(r);\n}\nfunction rt(e, t) {\n  throw new Error(e + t);\n}\nconst Et = (e) => {\n  const t = {\n    id: e.uuid,\n    label: e.type,\n    children: [],\n    tags: []\n  };\n  e.name !== \"\" && t.tags.push({\n    label: e.name,\n    textColor: 5750629,\n    backgroundColor: 15793395\n  });\n  const r = Le(e);\n  return r > 0 && t.tags.push({\n    label: `${wn(r)} KB`,\n    textColor: 15707189,\n    backgroundColor: 16775644,\n    tooltip: \"Memory usage\"\n  }), e.type.includes(\"Light\") && (Gr(e) && t.tags.push({\n    label: `${e.intensity}`,\n    textColor: 9738662,\n    backgroundColor: 16316922,\n    tooltip: \"Intensity\"\n  }), t.tags.push({\n    label: `#${new Z(e.color).getHexString()}`,\n    textColor: 9738662,\n    backgroundColor: 16316922,\n    tooltip: \"Color\"\n  })), e.type.includes(\"Camera\") && (t.tags.push({\n    label: `${e.fov}°`,\n    textColor: 9738662,\n    backgroundColor: 16316922,\n    tooltip: \"Field of view\"\n  }), t.tags.push({\n    label: `x: ${Math.round(e.position.x)} y: ${Math.round(e.position.y)} z: ${Math.round(e.position.z)}`,\n    textColor: 9738662,\n    backgroundColor: 16316922,\n    tooltip: \"Position\"\n  })), t;\n};\nfunction Tt(e, t, r = \"\") {\n  e.children.forEach((n) => {\n    if (n.type === \"HightlightMesh\" || r && !n.type.includes(r) && !n.name.includes(r))\n      return;\n    const o = Et(n);\n    t.children.push(o), Tt(n, o, r);\n  });\n}\nconst Wn = [], X = \"tres:inspector\", Nn = Se({\n  sceneGraph: null\n});\nfunction zn(e, t) {\n  Un(\n    {\n      id: \"dev.esm.tres\",\n      label: \"TresJS 🪐\",\n      logo: \"https://raw.githubusercontent.com/Tresjs/tres/main/public/favicon.svg\",\n      packageName: \"tresjs\",\n      homepage: \"https://tresjs.org\",\n      componentStateTypes: Wn,\n      app: e\n    },\n    (r) => {\n      typeof r.now != \"function\" && Fn(\n        \"You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.\"\n      ), r.addInspector({\n        id: X,\n        label: \"TresJS 🪐\",\n        icon: \"account_tree\",\n        treeFilterPlaceholder: \"Search instances\"\n      }), setInterval(() => {\n        r.sendInspectorTree(X);\n      }, 1e3), setInterval(() => {\n        r.notifyComponentUpdate();\n      }, 5e3), r.on.getInspectorTree((l) => {\n        if (l.inspectorId === X) {\n          const s = Et(t.scene.value);\n          Tt(t.scene.value, s, l.filter), Nn.sceneGraph = s, l.rootNodes = [s];\n        }\n      });\n      let n = null, o = null;\n      r.on.getInspectorState((l) => {\n        var s;\n        if (l.inspectorId === X) {\n          const [a] = t.scene.value.getObjectsByProperty(\"uuid\", l.nodeId);\n          if (!a)\n            return;\n          if (o && n && n.parent && o.remove(n), a.isMesh) {\n            const c = on(a);\n            a.add(c), n = c, o = a;\n          }\n          l.state = {\n            object: Object.entries(a).map(([c, i]) => c === \"children\" ? { key: c, value: i.filter((f) => f.type !== \"HightlightMesh\") } : { key: c, value: i, editable: !0 }).filter(({ key: c }) => c !== \"parent\")\n          }, a.isScene && (l.state = {\n            ...l.state,\n            state: [\n              {\n                key: \"Scene Info\",\n                value: {\n                  objects: a.children.length,\n                  memory: Le(a),\n                  calls: t.renderer.value.info.render.calls,\n                  triangles: t.renderer.value.info.render.triangles,\n                  points: t.renderer.value.info.render.points,\n                  lines: t.renderer.value.info.render.lines\n                }\n              },\n              {\n                key: \"Programs\",\n                value: ((s = t.renderer.value.info.programs) == null ? void 0 : s.map((c) => ({\n                  ...c,\n                  programName: c.name\n                }))) || []\n              }\n            ]\n          });\n        }\n      }), r.on.editInspectorState((l) => {\n        l.inspectorId === X && nn(t.scene.value, l.nodeId, l.path, l.state.value);\n      });\n    }\n  );\n}\nconst Gn = [\"data-scene\", \"data-tres\"], Vn = /* @__PURE__ */ ue({\n  __name: \"TresCanvas\",\n  props: {\n    shadows: { type: Boolean, default: void 0 },\n    clearColor: {},\n    toneMapping: {},\n    shadowMapType: {},\n    useLegacyLights: { type: Boolean, default: void 0 },\n    outputColorSpace: {},\n    toneMappingExposure: {},\n    renderMode: { default: \"always\" },\n    dpr: {},\n    camera: {},\n    preset: {},\n    windowSize: { type: Boolean, default: void 0 },\n    enableProvideBridge: { type: Boolean, default: !0 },\n    context: {},\n    alpha: { type: Boolean, default: void 0 },\n    premultipliedAlpha: { type: Boolean },\n    antialias: { type: Boolean, default: void 0 },\n    stencil: { type: Boolean, default: void 0 },\n    preserveDrawingBuffer: { type: Boolean, default: void 0 },\n    powerPreference: {},\n    depth: { type: Boolean, default: void 0 },\n    failIfMajorPerformanceCaveat: { type: Boolean, default: void 0 },\n    precision: {},\n    logarithmicDepthBuffer: { type: Boolean, default: void 0 },\n    reverseDepthBuffer: { type: Boolean }\n  },\n  emits: [\n    \"render\",\n    \"click\",\n    \"double-click\",\n    \"context-menu\",\n    \"pointer-move\",\n    \"pointer-up\",\n    \"pointer-down\",\n    \"pointer-enter\",\n    \"pointer-leave\",\n    \"pointer-over\",\n    \"pointer-out\",\n    \"pointer-missed\",\n    \"wheel\",\n    \"ready\"\n  ],\n  setup(e, { expose: t, emit: r }) {\n    const n = e, o = r, l = Rt(), s = $(), a = H(new at()), c = Ue();\n    Ee(it);\n    const i = (d, y = !1) => ue({\n      setup() {\n        var C;\n        const p = (C = Ue()) == null ? void 0 : C.appContext;\n        p && (p.app = c == null ? void 0 : c.appContext.app);\n        const h = {};\n        function P(b) {\n          b && (b.parent && P(b.parent), b.provides && Object.assign(h, b.provides));\n        }\n        return c != null && c.parent && n.enableProvideBridge && (P(c.parent), Reflect.ownKeys(h).forEach((b) => {\n          ae(b, h[b]);\n        })), ae(\"useTres\", d), ae(\"extend\", Ee), typeof window < \"u\" && zn(p == null ? void 0 : p.app, d), () => Fe(Ht, null, y ? [] : l.default());\n      }\n    }), f = (d, y = !1) => {\n      const p = i(d, y), { render: h } = $t(Rn(d));\n      h(Fe(p), a.value);\n    }, v = (d, y = !1) => {\n      vt(d.scene.value), y && (d.renderer.value.dispose(), d.renderer.value.renderLists.dispose(), d.renderer.value.forceContextLoss()), a.value.__tres = {\n        root: d\n      };\n    }, u = H(null);\n    t({ context: u, dispose: () => v(u.value, !0) });\n    const _ = () => {\n      v(u.value), f(u.value, !0);\n    };\n    return Ot(() => {\n      const d = s;\n      u.value = An({\n        scene: a.value,\n        canvas: d,\n        windowSize: n.windowSize ?? !1,\n        rendererOptions: n,\n        emit: o\n      });\n      const { registerCamera: y, camera: p, cameras: h, deregisterCamera: P } = u.value;\n      f(u.value);\n      const C = () => {\n        const b = new we(\n          45,\n          window.innerWidth / window.innerHeight,\n          0.1,\n          1e3\n        );\n        b.position.set(3, 3, 3), b.lookAt(0, 0, 0), y(b);\n        const E = Ae(() => {\n          h.value.length >= 2 && (b.removeFromParent(), P(b), E == null || E());\n        });\n      };\n      ie(\n        () => n.camera,\n        (b, E) => {\n          b && y(b), E && (E.removeFromParent(), P(E));\n        },\n        {\n          immediate: !0\n        }\n      ), p.value || C();\n    }), K(_), (d, y) => (jt(), Dt(\"canvas\", {\n      ref_key: \"canvas\",\n      ref: s,\n      \"data-scene\": a.value.uuid,\n      class: It(d.$attrs.class),\n      \"data-tres\": `tresjs ${W(jr).version}`,\n      style: Bt({\n        display: \"block\",\n        width: \"100%\",\n        height: \"100%\",\n        position: d.windowSize ? \"fixed\" : \"relative\",\n        top: 0,\n        left: 0,\n        pointerEvents: \"auto\",\n        touchAction: \"none\",\n        ...d.$attrs.style\n      })\n    }, null, 14, Gn));\n  }\n}), Yn = [\n  \"TresCanvas\",\n  \"TresLeches\",\n  \"TresScene\"\n], co = {\n  template: {\n    compilerOptions: {\n      isCustomElement: (e) => e.startsWith(\"Tres\") && !Yn.includes(e) || e === \"primitive\"\n    }\n  }\n}, { logWarning: qn } = Q();\nlet I = null;\nconst uo = {\n  updated: (e, t) => {\n    var o;\n    const r = sn(t);\n    if (!r) {\n      qn(`v-distance-to: problem with binding value: ${t.value}`);\n      return;\n    }\n    I && (I.dispose(), e.parent.remove(I));\n    const n = r.clone().sub(e.position);\n    n.normalize(), I = new Qt(n, e.position, e.position.distanceTo(r), 16776960), e.parent.add(I), console.table(\n      [\n        [\"Distance:\", e.position.distanceTo(r)],\n        [`origin: ${e.name || e.type}`, `x:${e.position.x}, y:${e.position.y}, z:${(o = e.position) == null ? void 0 : o.z}`],\n        [`Destiny: ${e.name || e.type}`, `x:${r.x}, y:${r.y}, z:${r == null ? void 0 : r.z}`]\n      ]\n    );\n  },\n  unmounted: (e) => {\n    I == null || I.dispose(), e.parent && e.parent.remove(I);\n  }\n};\nclass St extends Xt {\n  constructor(t, r) {\n    const n = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 1, 1, 0], o = new We();\n    o.setAttribute(\"position\", new Ne(n, 3)), o.computeBoundingSphere();\n    const l = new Zt({ fog: !1 });\n    super(o, l), this.light = t, this.color = r, this.type = \"RectAreaLightHelper\";\n    const s = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0], a = new We();\n    a.setAttribute(\"position\", new Ne(s, 3)), a.computeBoundingSphere(), this.add(new er(a, new ct({ side: tr, fog: !1 })));\n  }\n  updateMatrixWorld() {\n    if (this.scale.set(0.5 * this.light.width, 0.5 * this.light.height, 1), this.color !== void 0)\n      this.material.color.set(this.color), this.children[0].material.color.set(this.color);\n    else {\n      this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity);\n      const t = this.material.color, r = Math.max(t.r, t.g, t.b);\n      r > 1 && t.multiplyScalar(1 / r), this.children[0].material.color.copy(this.material.color);\n    }\n    this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld), this.children[0].matrixWorld.copy(this.matrixWorld);\n  }\n  dispose() {\n    this.geometry.dispose(), this.material.dispose(), this.children[0].geometry.dispose(), this.children[0].material.dispose();\n  }\n}\nconst { logWarning: nt } = Q();\nlet se, F;\nconst Kn = {\n  DirectionalLight: sr,\n  PointLight: or,\n  SpotLight: nr,\n  HemisphereLight: rr,\n  RectAreaLight: St\n}, fo = {\n  mounted: (e) => {\n    if (!e.isLight) {\n      nt(`${e.type} is not a light`);\n      return;\n    }\n    se = Kn[e.type], e.parent.add(new se(e, 1, e.color.getHex()));\n  },\n  updated: (e) => {\n    F = e.parent.children.find((t) => t instanceof se), !(F instanceof St) && F.update();\n  },\n  unmounted: (e) => {\n    if (!e.isLight) {\n      nt(`${e.type} is not a light`);\n      return;\n    }\n    F = e.parent.children.find((t) => t instanceof se), F && F.dispose && F.dispose(), e.parent && e.parent.remove(F);\n  }\n}, po = {\n  mounted: (e, t) => {\n    if (t.arg) {\n      console.log(`v-log:${t.arg}`, e[t.arg]);\n      return;\n    }\n    console.log(\"v-log\", e);\n  }\n}, mo = {\n  install(e) {\n    e.component(\"TresCanvas\", Vn);\n  }\n};\nexport {\n  Vn as TresCanvas,\n  eo as UseLoader,\n  to as UseTexture,\n  Ce as catalogue,\n  _n as createRenderLoop,\n  mo as default,\n  vt as dispose,\n  Ee as extend,\n  ro as isProd,\n  Mn as normalizeColor,\n  no as normalizeVectorFlexibleParam,\n  oo as onTresReady,\n  co as templateCompilerOptions,\n  Br as traverseObjects,\n  mn as useCamera,\n  Ir as useLoader,\n  Q as useLogger,\n  io as useLoop,\n  En as useRaycaster,\n  ao as useRenderLoop,\n  Pn as useRenderer,\n  lo as useSeek,\n  dn as useTexture,\n  so as useTres,\n  pe as useTresContext,\n  An as useTresContextProvider,\n  Tn as useTresEventManager,\n  uo as vDistanceTo,\n  fo as vLightHelper,\n  po as vLog\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA,SAAS,kBAAkBA,KAAI;AAC7B,MAAI,gBAAgB,GAAG;AACrB,mBAAeA,GAAE;AACjB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB;AACzB,QAAM,MAAsB,oBAAI,IAAI;AACpC,QAAM,MAAM,CAACA,QAAO;AAClB,QAAI,OAAOA,GAAE;AAAA,EACf;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,MAAM;AAAA,EACZ;AACA,QAAMC,MAAK,CAACD,QAAO;AACjB,QAAI,IAAIA,GAAE;AACV,UAAM,QAAQ,MAAM,IAAIA,GAAE;AAC1B,sBAAkB,KAAK;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,UAAU,IAAI,SAAS;AAC3B,WAAO,QAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,IAAI,CAACA,QAAOA,IAAG,GAAG,IAAI,CAAC,CAAC;AAAA,EAC7D;AACA,SAAO;AAAA,IACL,IAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAeA,IAAM,wBAAwC,oBAAI,QAAQ;AAE1D,IAAM,cAAc,IAAI,SAAS;AAC/B,MAAI;AACJ,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,YAAY,KAAK,mBAAmB,MAAM,OAAO,SAAS,GAAG;AACnE,MAAI,YAAY,QAAQ,CAAC,oBAAoB;AAC3C,UAAM,IAAI,MAAM,qCAAqC;AACvD,MAAI,YAAY,sBAAsB,IAAI,QAAQ,KAAK,OAAO,sBAAsB,IAAI,QAAQ;AAC9F,WAAO,sBAAsB,IAAI,QAAQ,EAAE,GAAG;AAChD,SAAO,OAAO,GAAG,IAAI;AACvB;AAsLA,IAAM,WAAW,OAAO,WAAW,eAAe,OAAO,aAAa;AACtE,IAAM,WAAW,OAAO,sBAAsB,eAAe,sBAAsB;AAEnF,IAAM,aAAa,CAAC,QAAQ,OAAO;AAKnC,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,WAAW,CAAC,QAAQ,SAAS,KAAK,GAAG,MAAM;AAIjD,IAAM,OAAO,MAAM;AACnB;AAOA,IAAM,QAAwB,SAAS;AACvC,SAAS,WAAW;AAClB,MAAI,IAAI;AACR,SAAO,cAAc,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,eAAe,mBAAmB,KAAK,OAAO,UAAU,SAAS,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,KAAK,UAAU,OAAO,SAAS,OAAO,UAAU,SAAS;AAC9U;AAEA,SAAS,oBAAoB,QAAQC,KAAI;AACvC,WAAS,WAAW,MAAM;AACxB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ,QAAQ,OAAO,MAAMA,IAAG,MAAM,MAAM,IAAI,GAAG,EAAE,IAAAA,KAAI,SAAS,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,IAC7G,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,SAAS,eAAe,IAAI,UAAU,CAAC,GAAG;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACnB,QAAM,gBAAgB,CAAC,WAAW;AAChC,iBAAa,MAAM;AACnB,iBAAa;AACb,mBAAe;AAAA,EACjB;AACA,MAAI;AACJ,QAAM,SAAS,CAAC,WAAW;AACzB,UAAM,WAAW,QAAU,EAAE;AAC7B,UAAM,cAAc,QAAU,QAAQ,OAAO;AAC7C,QAAI;AACF,oBAAc,KAAK;AACrB,QAAI,YAAY,KAAK,gBAAgB,UAAU,eAAe,GAAG;AAC/D,UAAI,UAAU;AACZ,sBAAc,QAAQ;AACtB,mBAAW;AAAA,MACb;AACA,aAAO,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACjC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,QAAQ,iBAAiB,SAAS;AACjD,oBAAc;AACd,UAAI,eAAe,CAAC,UAAU;AAC5B,mBAAW,WAAW,MAAM;AAC1B,cAAI;AACF,0BAAc,KAAK;AACrB,qBAAW;AACX,kBAAQ,YAAY,CAAC;AAAA,QACvB,GAAG,WAAW;AAAA,MAChB;AACA,cAAQ,WAAW,MAAM;AACvB,YAAI;AACF,wBAAc,QAAQ;AACxB,mBAAW;AACX,gBAAQ,OAAO,CAAC;AAAA,MAClB,GAAG,QAAQ;AAAA,IACb,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAyEA,SAAS,oBAAoBC,KAAI;AAC/B,QAAM,QAAwB,uBAAO,OAAO,IAAI;AAChD,SAAO,CAAC,QAAQ;AACd,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAIA,IAAG,GAAG;AAAA,EACpC;AACF;AACA,IAAM,cAAc;AACpB,IAAM,YAAY,oBAAoB,CAAC,QAAQ,IAAI,QAAQ,aAAa,KAAK,EAAE,YAAY,CAAC;AAC5F,IAAM,aAAa;AACnB,IAAM,WAAW,oBAAoB,CAAC,QAAQ;AAC5C,SAAO,IAAI,QAAQ,YAAY,CAAC,GAAG,MAAM,IAAI,EAAE,YAAY,IAAI,EAAE;AACnE,CAAC;AAUD,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AAiCA,SAAS,QAAQ,IAAI;AACnB,SAAO,GAAG,SAAS,KAAK,IAAI,OAAO,WAAW,EAAE,IAAI,KAAK,OAAO,WAAW,EAAE;AAC/E;AACA,SAAS,WAAW,KAAKC,OAAM,gBAAgB,OAAO;AACpD,SAAOA,MAAK,OAAO,CAAC,GAAG,MAAM;AAC3B,QAAI,KAAK,KAAK;AACZ,UAAI,CAAC,iBAAiB,IAAI,CAAC,MAAM;AAC/B,UAAE,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AASA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,UAAU,mBAAmB;AACtC;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AA0CA,SAAS,cAAcC,KAAI,KAAK,KAAK,UAAU,CAAC,GAAG;AACjD,SAAO;AAAA,IACL,eAAe,IAAI,OAAO;AAAA,IAC1BA;AAAA,EACF;AACF;AAEA,SAAS,aAAa,OAAO,KAAK,KAAK,UAAU,CAAC,GAAG;AACnD,QAAM,YAAY,IAAI,MAAM,KAAK;AACjC,QAAM,UAAU,cAAc,MAAM;AAClC,cAAU,QAAQ,MAAM;AAAA,EAC1B,GAAG,IAAI,OAAO;AACd,QAAM,OAAO,MAAM,QAAQ,CAAC;AAC5B,SAAO;AACT;AAqLA,SAASC,QAAO,WAAW,UAAU,CAAC,GAAG;AACvC,MAAI,CAAC,MAAM,SAAS;AAClB,WAAO,OAAS,SAAS;AAC3B,QAAM,SAAS,MAAM,QAAQ,UAAU,KAAK,IAAI,MAAM,KAAK,EAAE,QAAQ,UAAU,MAAM,OAAO,CAAC,IAAI,CAAC;AAClG,aAAW,OAAO,UAAU,OAAO;AACjC,WAAO,GAAG,IAAI,UAAU,OAAO;AAAA,MAC7B,MAAM;AACJ,eAAO,UAAU,MAAM,GAAG;AAAA,MAC5B;AAAA,MACA,IAAI,GAAG;AACL,YAAI;AACJ,cAAM,cAAc,KAAK,QAAU,QAAQ,UAAU,MAAM,OAAO,KAAK;AACvE,YAAI,YAAY;AACd,cAAI,MAAM,QAAQ,UAAU,KAAK,GAAG;AAClC,kBAAM,OAAO,CAAC,GAAG,UAAU,KAAK;AAChC,iBAAK,GAAG,IAAI;AACZ,sBAAU,QAAQ;AAAA,UACpB,OAAO;AACL,kBAAM,YAAY,EAAE,GAAG,UAAU,OAAO,CAAC,GAAG,GAAG,EAAE;AACjD,mBAAO,eAAe,WAAW,OAAO,eAAe,UAAU,KAAK,CAAC;AACvE,sBAAU,QAAQ;AAAA,UACpB;AAAA,QACF,OAAO;AACL,oBAAU,MAAM,GAAG,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF,EAAE;AAAA,EACJ;AACA,SAAO;AACT;AAEA,IAAMC,WAAU;AAmBhB,SAAS,aAAaC,KAAI,OAAO,MAAM,QAAQ;AAC7C,QAAM,WAAW,mBAAmB;AACpC,MAAI;AACF,cAAUA,KAAI,MAAM;AAAA,WACb;AACP,IAAAA,IAAG;AAAA;AAEH,aAASA,GAAE;AACf;AA0WA,SAAS,cAAc,IAAI,WAAW,KAAK,UAAU,CAAC,GAAG;AACvD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB,IAAI;AACJ,MAAI,QAAQ;AACZ,QAAM,WAAW,WAAW,KAAK;AACjC,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,oBAAc,KAAK;AACnB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AACjB,UAAM;AAAA,EACR;AACA,WAAS,SAAS;AAChB,UAAM,gBAAgB,QAAU,QAAQ;AACxC,QAAI,iBAAiB;AACnB;AACF,aAAS,QAAQ;AACjB,QAAI;AACF,SAAG;AACL,UAAM;AACN,QAAI,SAAS;AACX,cAAQ,YAAY,IAAI,aAAa;AAAA,EACzC;AACA,MAAI,aAAa;AACf,WAAO;AACT,MAAI,MAAM,QAAQ,KAAK,OAAO,aAAa,YAAY;AACrD,UAAM,YAAY,MAAM,UAAU,MAAM;AACtC,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS;AAAA,EAC7B;AACA,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAkSA,SAAS,eAAe,QAAQ,IAAI,SAAS;AAC3C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,WAAW;AAAA,IACb;AAAA,EACF;AACF;;;ACv0CA,IAAM,gBAAgB,WAAW,SAAS;AAC1C,IAAM,kBAAkB,WAAW,OAAO,WAAW;AACrD,IAAM,mBAAmB,WAAW,OAAO,YAAY;AACvD,IAAM,kBAAkB,WAAW,OAAO,WAAW;AAErD,SAAS,aAAa,OAAO;AAC3B,MAAI;AACJ,QAAM,QAAQ,QAAQ,KAAK;AAC3B,UAAQ,KAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAO,KAAK;AAClE;AAEA,SAAS,oBAAoB,MAAM;AACjC,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,MAAM;AACpB,aAAS,QAAQ,CAACC,QAAOA,IAAG,CAAC;AAC7B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,WAAW,CAAC,IAAI,OAAO,UAAU,YAAY;AACjD,OAAG,iBAAiB,OAAO,UAAU,OAAO;AAC5C,WAAO,MAAM,GAAG,oBAAoB,OAAO,UAAU,OAAO;AAAA,EAC9D;AACA,QAAM,oBAAoB,SAAS,MAAM;AACvC,UAAM,OAAO,QAAQ,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAC9D,WAAO,KAAK,MAAM,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI,OAAO;AAAA,EAC3D,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,MAAM;AACJ,UAAI,IAAI;AACR,aAAO;AAAA,SACJ,MAAM,KAAK,kBAAkB,UAAU,OAAO,SAAS,GAAG,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,MAAM,OAAO,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI;AAAA,QAC9I,QAAQ,QAAQ,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,QAC5D,QAAQ,MAAM,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA;AAAA,QAE1D,QAAQ,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,IACA,CAAC,CAAC,aAAa,YAAY,eAAe,WAAW,MAAM;AACzD,cAAQ;AACR,UAAI,EAAE,eAAe,OAAO,SAAS,YAAY,WAAW,EAAE,cAAc,OAAO,SAAS,WAAW,WAAW,EAAE,iBAAiB,OAAO,SAAS,cAAc;AACjK;AACF,YAAM,eAAe,SAAS,WAAW,IAAI,EAAE,GAAG,YAAY,IAAI;AAClE,eAAS;AAAA,QACP,GAAG,YAAY;AAAA,UACb,CAAC,OAAO,WAAW;AAAA,YACjB,CAAC,UAAU,cAAc,IAAI,CAAC,aAAa,SAAS,IAAI,OAAO,UAAU,YAAY,CAAC;AAAA,UACxF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,OAAO,OAAO;AAAA,EAClB;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,OAAO;AACzB,SAAO;AACT;AA8FA,SAAS,aAAa;AACpB,QAAM,YAAY,WAAW,KAAK;AAClC,QAAM,WAAW,mBAAmB;AACpC,MAAI,UAAU;AACZ,cAAU,MAAM;AACd,gBAAU,QAAQ;AAAA,IACpB,GAAG,QAAQ;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,aAAa,UAAU;AAC9B,QAAM,YAAY,WAAW;AAC7B,SAAO,SAAS,MAAM;AACpB,cAAU;AACV,WAAO,QAAQ,SAAS,CAAC;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,oBAAoB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC3D,QAAM,EAAE,QAAAC,UAAS,eAAe,GAAG,gBAAgB,IAAI;AACvD,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,QAAQ,QAAQ,MAAM;AAC5B,UAAM,QAAQ,QAAQ,KAAK,EAAE,IAAI,YAAY,EAAE,OAAO,UAAU;AAChE,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,CAAC,aAAa;AACZ,cAAQ;AACR,UAAI,YAAY,SAAS,SAAS,MAAM;AACtC,mBAAW,IAAI,iBAAiB,QAAQ;AACxC,iBAAS,QAAQ,CAAC,OAAO,SAAS,QAAQ,IAAI,eAAe,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,cAAc,MAAM;AACxB,WAAO,YAAY,OAAO,SAAS,SAAS,YAAY;AAAA,EAC1D;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAqSA,SAAS,SAASC,KAAI,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAAC,UAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,gBAAgB,SAAS,MAAM;AACnC,WAAO,WAAW,MAAM,QAAQ,QAAQ,IAAI;AAAA,EAC9C,CAAC;AACD,MAAI,yBAAyB;AAC7B,MAAI,QAAQ;AACZ,WAAS,KAAKC,YAAW;AACvB,QAAI,CAAC,SAAS,SAAS,CAACD;AACtB;AACF,QAAI,CAAC;AACH,+BAAyBC;AAC3B,UAAM,QAAQA,aAAY;AAC1B,QAAI,cAAc,SAAS,QAAQ,cAAc,OAAO;AACtD,cAAQD,QAAO,sBAAsB,IAAI;AACzC;AAAA,IACF;AACA,6BAAyBC;AACzB,IAAAF,IAAG,EAAE,OAAO,WAAAE,WAAU,CAAC;AACvB,QAAI,MAAM;AACR,eAAS,QAAQ;AACjB,cAAQ;AACR;AAAA,IACF;AACA,YAAQD,QAAO,sBAAsB,IAAI;AAAA,EAC3C;AACA,WAAS,SAAS;AAChB,QAAI,CAAC,SAAS,SAASA,SAAQ;AAC7B,eAAS,QAAQ;AACjB,+BAAyB;AACzB,cAAQA,QAAO,sBAAsB,IAAI;AAAA,IAC3C;AAAA,EACF;AACA,WAAS,QAAQ;AACf,aAAS,QAAQ;AACjB,QAAI,SAAS,QAAQA,SAAQ;AAC3B,MAAAA,QAAO,qBAAqB,KAAK;AACjC,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI;AACF,WAAO;AACT,oBAAkB,KAAK;AACvB,SAAO;AAAA,IACL,UAAU,SAAS,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACF;AAwiBA,IAAM,iBAAiB,OAAO,kBAAkB;AAChD,SAAS,cAAc;AACrB,QAAM,WAAW,oBAAoB,IAAI,YAAY,gBAAgB,IAAI,IAAI;AAC7E,SAAO,OAAO,aAAa,WAAW,WAAW;AACnD;AASA,SAAS,cAAc,OAAO,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAE,UAAS,eAAe,WAAW,YAAY,EAAE,IAAI;AAC7D,QAAM,cAAc,aAAa,MAAMA,WAAU,gBAAgBA,WAAU,OAAOA,QAAO,eAAe,UAAU;AAClH,QAAM,aAAa,WAAW,OAAO,aAAa,QAAQ;AAC1D,QAAM,aAAa,WAAW;AAC9B,QAAM,UAAU,WAAW,KAAK;AAChC,QAAM,UAAU,CAAC,UAAU;AACzB,YAAQ,QAAQ,MAAM;AAAA,EACxB;AACA,cAAY,MAAM;AAChB,QAAI,WAAW,OAAO;AACpB,iBAAW,QAAQ,CAAC,YAAY;AAChC,YAAM,eAAe,QAAQ,KAAK,EAAE,MAAM,GAAG;AAC7C,cAAQ,QAAQ,aAAa,KAAK,CAAC,gBAAgB;AACjD,cAAM,MAAM,YAAY,SAAS,SAAS;AAC1C,cAAM,WAAW,YAAY,MAAM,gDAAgD;AACnF,cAAM,WAAW,YAAY,MAAM,gDAAgD;AACnF,YAAI,MAAM,QAAQ,YAAY,QAAQ;AACtC,YAAI,YAAY,KAAK;AACnB,gBAAM,YAAY,QAAQ,SAAS,CAAC,CAAC;AAAA,QACvC;AACA,YAAI,YAAY,KAAK;AACnB,gBAAM,YAAY,QAAQ,SAAS,CAAC,CAAC;AAAA,QACvC;AACA,eAAO,MAAM,CAAC,MAAM;AAAA,MACtB,CAAC;AACD;AAAA,IACF;AACA,QAAI,CAAC,YAAY;AACf;AACF,eAAW,QAAQA,QAAO,WAAW,QAAQ,KAAK,CAAC;AACnD,YAAQ,QAAQ,WAAW,MAAM;AAAA,EACnC,CAAC;AACD,mBAAiB,YAAY,UAAU,SAAS,EAAE,SAAS,KAAK,CAAC;AACjE,SAAO,SAAS,MAAM,QAAQ,KAAK;AACrC;AAucA,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,IAAM,WAA2B,YAAY;AAC7C,SAAS,cAAc;AACrB,MAAI,EAAE,aAAa;AACjB,YAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC9C,SAAO,QAAQ,SAAS;AAC1B;AAouBA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,QAAAC,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,aAAa,WAAW,CAAC;AAC/B,QAAM,QAAQ,cAAc,MAAM,gBAAgB,WAAW,KAAK,SAAS,OAAO;AAClF,MAAI,OAAO;AACX,MAAIA,SAAQ;AACV,WAAO,eAAe,OAAO,MAAM,WAAW,QAAQA,QAAO,gBAAgB;AAAA,EAC/E;AACA,SAAO;AAAA,IACL,YAAY,SAAS,UAAU;AAAA,IAC/B;AAAA,EACF;AACF;AAuUA,SAAS,kBAAkB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACzD,QAAM,EAAE,QAAAC,UAAS,eAAe,GAAG,gBAAgB,IAAI;AACvD,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,oBAAoBA,OAAM;AAC3E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,WAAW,QAAQ,MAAM;AAC/B,WAAO,MAAM,QAAQ,QAAQ,IAAI,SAAS,IAAI,CAAC,OAAO,aAAa,EAAE,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC;AAAA,EACnG,CAAC;AACD,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,CAAC,QAAQ;AACP,cAAQ;AACR,UAAI,YAAY,SAASA,SAAQ;AAC/B,mBAAW,IAAI,eAAe,QAAQ;AACtC,mBAAW,OAAO,KAAK;AACrB,cAAI;AACF,qBAAS,QAAQ,KAAK,eAAe;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,QAAQ,UAAU,CAAC,GAAG;AAChD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB,IAAI;AACJ,QAAM,SAAS,WAAW,CAAC;AAC3B,QAAM,SAAS,WAAW,CAAC;AAC3B,QAAM,OAAO,WAAW,CAAC;AACzB,QAAM,QAAQ,WAAW,CAAC;AAC1B,QAAM,MAAM,WAAW,CAAC;AACxB,QAAM,QAAQ,WAAW,CAAC;AAC1B,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,WAAS,cAAc;AACrB,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,CAAC,IAAI;AACP,UAAI,OAAO;AACT,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf,aAAK,QAAQ;AACb,cAAM,QAAQ;AACd,YAAI,QAAQ;AACZ,cAAM,QAAQ;AACd,UAAE,QAAQ;AACV,UAAE,QAAQ;AAAA,MACZ;AACA;AAAA,IACF;AACA,UAAM,OAAO,GAAG,sBAAsB;AACtC,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,KAAK;AACpB,SAAK,QAAQ,KAAK;AAClB,UAAM,QAAQ,KAAK;AACnB,QAAI,QAAQ,KAAK;AACjB,UAAM,QAAQ,KAAK;AACnB,MAAE,QAAQ,KAAK;AACf,MAAE,QAAQ,KAAK;AAAA,EACjB;AACA,WAAS,SAAS;AAChB,QAAI,iBAAiB;AACnB,kBAAY;AAAA,aACL,iBAAiB;AACxB,4BAAsB,MAAM,YAAY,CAAC;AAAA,EAC7C;AACA,oBAAkB,QAAQ,MAAM;AAChC,QAAM,MAAM,aAAa,MAAM,GAAG,CAAC,QAAQ,CAAC,OAAO,OAAO,CAAC;AAC3D,sBAAoB,QAAQ,QAAQ;AAAA,IAClC,iBAAiB,CAAC,SAAS,OAAO;AAAA,EACpC,CAAC;AACD,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,MAAM,SAAS,KAAK,CAAC;AACrE,MAAI;AACF,qBAAiB,UAAU,QAAQ,EAAE,SAAS,KAAK,CAAC;AACtD,eAAa,MAAM;AACjB,QAAI;AACF,aAAO;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AA8DA,SAAS,eAAe,QAAQ,cAAc,EAAE,OAAO,GAAG,QAAQ,EAAE,GAAG,UAAU,CAAC,GAAG;AACnF,QAAM,EAAE,QAAAC,UAAS,eAAe,MAAM,cAAc,IAAI;AACxD,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,aAAa,MAAM,MAAM,OAAO,SAAS,GAAG,iBAAiB,OAAO,SAAS,GAAG,SAAS,KAAK;AAAA,EACnH,CAAC;AACD,QAAM,QAAQ,WAAW,YAAY,KAAK;AAC1C,QAAM,SAAS,WAAW,YAAY,MAAM;AAC5C,QAAM,EAAE,MAAM,MAAM,IAAI;AAAA,IACtB;AAAA,IACA,CAAC,CAAC,KAAK,MAAM;AACX,YAAM,UAAU,QAAQ,eAAe,MAAM,gBAAgB,QAAQ,gBAAgB,MAAM,iBAAiB,MAAM;AAClH,UAAIA,WAAU,MAAM,OAAO;AACzB,cAAM,QAAQ,aAAa,MAAM;AACjC,YAAI,OAAO;AACT,gBAAM,OAAO,MAAM,sBAAsB;AACzC,gBAAM,QAAQ,KAAK;AACnB,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,SAAS;AACX,gBAAM,gBAAgB,QAAQ,OAAO;AACrC,gBAAM,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,WAAW,MAAM,MAAM,YAAY,CAAC;AAC/E,iBAAO,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,UAAU,MAAM,MAAM,WAAW,CAAC;AAAA,QAChF,OAAO;AACL,gBAAM,QAAQ,MAAM,YAAY;AAChC,iBAAO,QAAQ,MAAM,YAAY;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,eAAa,MAAM;AACjB,UAAM,MAAM,aAAa,MAAM;AAC/B,QAAI,KAAK;AACP,YAAM,QAAQ,iBAAiB,MAAM,IAAI,cAAc,YAAY;AACnE,aAAO,QAAQ,kBAAkB,MAAM,IAAI,eAAe,YAAY;AAAA,IACxE;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,MAAM,aAAa,MAAM;AAAA,IACzB,CAAC,QAAQ;AACP,YAAM,QAAQ,MAAM,YAAY,QAAQ;AACxC,aAAO,QAAQ,MAAM,YAAY,SAAS;AAAA,IAC5C;AAAA,EACF;AACA,WAAS,OAAO;AACd,UAAM;AACN,UAAM;AAAA,EACR;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAy0BA,SAAS,OAAO,SAAS;AACvB,MAAI;AACJ,QAAM,MAAM,WAAW,CAAC;AACxB,MAAI,OAAO,gBAAgB;AACzB,WAAO;AACT,QAAM,SAAS,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK;AAC7E,MAAI,OAAO,YAAY,IAAI;AAC3B,MAAI,QAAQ;AACZ,WAAS,MAAM;AACb,aAAS;AACT,QAAI,SAAS,OAAO;AAClB,YAAM,MAAM,YAAY,IAAI;AAC5B,YAAM,OAAO,MAAM;AACnB,UAAI,QAAQ,KAAK,MAAM,OAAO,OAAO,MAAM;AAC3C,aAAO;AACP,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,SAAO;AACT;AA4jCA,SAAS,UAAU,UAAU,CAAC,GAAG;AAC/B,QAAM,SAAS,IAAI;AACnB,QAAM,cAAc,aAAa,MAAM,OAAO,gBAAgB,eAAe,YAAY,WAAW;AACpG,MAAI,YAAY,OAAO;AACrB,UAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,kBAAc,MAAM;AAClB,aAAO,QAAQ,YAAY;AAAA,IAC7B,GAAG,UAAU,EAAE,WAAW,QAAQ,WAAW,mBAAmB,QAAQ,kBAAkB,CAAC;AAAA,EAC7F;AACA,SAAO,EAAE,aAAa,OAAO;AAC/B;AAiiBA,IAAM,eAAe;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,WAAW;AAAA,EACX,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,OAAuB,OAAO,KAAK,YAAY;AACrD,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,QAAQ,IAAI,QAAQ,gBAAgB,CAAC,CAAC;AAC5C,SAAO,OAAO,MAAM,OAAO,cAAc,MAAM,KAAK;AACpD,QAAM,UAAU,CAAC,UAAU;AACzB,aAAS,QAAQ;AACjB,QAAI,QAAQ,gBAAgB,CAAC,QAAQ,aAAa,SAAS,MAAM,WAAW;AAC1E;AACF,UAAM,QAAQ,WAAW,OAAO,MAAM,KAAK;AAAA,EAC7C;AACA,MAAI,QAAQ;AACV,UAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,qBAAiB,QAAQ,CAAC,eAAe,eAAe,WAAW,GAAG,SAAS,eAAe;AAC9F,qBAAiB,QAAQ,gBAAgB,MAAM,SAAS,QAAQ,OAAO,eAAe;AAAA,EACxF;AACA,SAAO;AAAA,IACL,GAAGC,QAAO,KAAK;AAAA,IACf;AAAA,EACF;AACF;AAwjCA,IAAM,gBAAgB;AAAA,EACpB,EAAE,KAAK,KAAK,OAAO,KAAK,MAAM,SAAS;AAAA,EACvC,EAAE,KAAK,OAAO,OAAO,KAAK,MAAM,SAAS;AAAA,EACzC,EAAE,KAAK,MAAM,OAAO,MAAM,MAAM,OAAO;AAAA,EACvC,EAAE,KAAK,QAAQ,OAAO,OAAO,MAAM,MAAM;AAAA,EACzC,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AAAA,EAC5C,EAAE,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ;AAAA,EAC7C,EAAE,KAAK,OAAO,mBAAmB,OAAO,SAAS,MAAM,OAAO;AAChE;AAsLA,IAAM,qBAAqB;AAAA,EACzB,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7B,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC5B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC7B,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC9B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EACjC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;AAAA,EAC5B,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7B,aAAa,CAAC,GAAG,MAAM,MAAM,CAAC;AAAA,EAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,EAChC,YAAY,CAAC,MAAM,GAAG,MAAM,KAAK;AAAA,EACjC,aAAa,CAAC,MAAM,MAAM,MAAM,CAAC;AAAA,EACjC,eAAe,CAAC,MAAM,MAAM,MAAM,GAAG;AACvC;AACA,IAAM,oBAAoC,OAAO,OAAO,CAAC,GAAG,EAAE,QAAQ,SAAS,GAAG,kBAAkB;AAyhCpG,SAAS,cAAc,UAAU,CAAC,GAAG;AACnC,QAAM;AAAA,IACJ,QAAAC,UAAS;AAAA,IACT,eAAe,OAAO;AAAA,IACtB,gBAAgB,OAAO;AAAA,IACvB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,QAAQ,WAAW,YAAY;AACrC,QAAM,SAAS,WAAW,aAAa;AACvC,QAAM,SAAS,MAAM;AACnB,QAAIA,SAAQ;AACV,UAAI,SAAS,SAAS;AACpB,cAAM,QAAQA,QAAO;AACrB,eAAO,QAAQA,QAAO;AAAA,MACxB,WAAW,SAAS,YAAYA,QAAO,gBAAgB;AACrD,cAAM,EAAE,OAAO,qBAAqB,QAAQ,sBAAsB,MAAM,IAAIA,QAAO;AACnF,cAAM,QAAQ,KAAK,MAAM,sBAAsB,KAAK;AACpD,eAAO,QAAQ,KAAK,MAAM,uBAAuB,KAAK;AAAA,MACxD,WAAW,kBAAkB;AAC3B,cAAM,QAAQA,QAAO;AACrB,eAAO,QAAQA,QAAO;AAAA,MACxB,OAAO;AACL,cAAM,QAAQA,QAAO,SAAS,gBAAgB;AAC9C,eAAO,QAAQA,QAAO,SAAS,gBAAgB;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACP,eAAa,MAAM;AACnB,QAAM,kBAAkB,EAAE,SAAS,KAAK;AACxC,mBAAiB,UAAU,QAAQ,eAAe;AAClD,MAAIA,WAAU,SAAS,YAAYA,QAAO,gBAAgB;AACxD,qBAAiBA,QAAO,gBAAgB,UAAU,QAAQ,eAAe;AAAA,EAC3E;AACA,MAAI,mBAAmB;AACrB,UAAM,UAAU,cAAc,yBAAyB;AACvD,UAAM,SAAS,MAAM,OAAO,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE,OAAO,OAAO;AACzB;;;AC30PA,IAAI,KAAK,OAAO;AAChB,IAAI,KAAK,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC/G,IAAI,KAAK,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC;AAKhE,IAAM,KAAK;AAAX,IAA2B,KAAK;AAAhC,IAA0C,KAAK;AAA/C,IAAwD,KAAK;AAA7D,IAA4E,KAAK;AAAjF,IAA6H,KAAK;AAAlI,IAAgN,KAAK;AAArN,IAA4N,KAAK,EAAE,MAAM,OAAO,KAAK,yCAAyC;AAA9R,IAAiS,KAAK,CAAC,OAAO,MAAM,WAAW,SAAS,aAAa;AAArV,IAAwV,KAAK;AAA7V,IAAiW,KAAK,EAAE,KAAK,EAAE,OAAO,qBAAqB,QAAQ,kBAAkB,SAAS,sBAAsB,GAAG,gBAAgB,EAAE,OAAO,mCAAmC,GAAG,iBAAiB,EAAE,OAAO,oCAAoC,GAAG,WAAW,EAAE,OAAO,8BAA8B,GAAG,WAAW,EAAE,OAAO,8BAA8B,GAAG,OAAO,MAAM;AAA9rB,IAAisB,KAAK;AAAtsB,IAAwtB,KAAK;AAA7tB,IAA+uB,KAAK;AAApvB,IAAywB,KAAK,CAAC,UAAU,MAAM;AAA/xB,IAAkyB,KAAK,EAAE,QAAQ,SAAS;AAA1zB,IAA6zB,KAAK,EAAE,KAAK,wCAAwC,YAAY,yCAAyC,OAAO,cAAc,MAAM,UAAU,WAAW,cAAc,WAAW,uCAAuC,SAAS,cAAc,UAAU,yBAAyB,MAAM,YAAY,YAAY,kBAAkB,YAAY,sBAAsB,cAAc,wBAAwB,cAAc,wBAAwB,gBAAgB,0BAA0B,qBAAqB,sCAAsC;AAAn1C,IAAs1C,KAAK,EAAE,OAAO,WAAW,KAAK,QAAQ;AAA53C,IAA+3C,KAAK,EAAE,qBAAqB,UAAU,qBAAqB,UAAU,gBAAgB,UAAU;AAA99C,IAAi+C,KAAK,EAAE,sCAAsC,WAAW,mBAAmB,WAAW,mBAAmB,SAAS,yBAAyB,UAAU,gBAAgB,YAAY,oCAAoC,WAAW,6BAA6B,WAAW,sBAAsB,UAAU,uBAAuB,WAAW,uBAAuB,UAAU,cAAc,UAAU,mBAAmB,UAAU,QAAQ,WAAW,qBAAqB,WAAW,MAAM,UAAU,MAAM,WAAW,OAAO,WAAW,UAAU,UAAU,WAAW,WAAW,OAAO,UAAU,cAAc,WAAW,0BAA0B,UAAU,sBAAsB,UAAU,4BAA4B,WAAW,YAAY,WAAW,OAAO,YAAY,QAAQ,WAAW,UAAU,UAAU,2BAA2B,WAAW,MAAM,UAAU,sBAAsB,UAAU,mBAAmB,SAAS,uBAAuB,WAAW,iCAAiC,WAAW,mBAAmB,UAAU,WAAW,SAAS,QAAQ,SAAS,KAAK,UAAU,YAAY,WAAW;AAA/hF,IAAkiF,KAAK;AAAA,EACriF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,eAAe;AAAA,EACf,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,iBAAiB;AACnB;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,EAAE,OAAO,CAAC,GAAG,WAAW,CAAC,EAAE;AACrC,SAAO,KAAK,EAAE,SAAS,CAAC,MAAM;AAC5B,MAAE,SAAS,EAAE,MAAM,EAAE,IAAI,IAAI,IAAI,EAAE,YAAY,CAAC,EAAE,UAAU,EAAE,SAAS,IAAI,MAAM,EAAE,UAAU,EAAE,SAAS,IAAI,IAAI,EAAE;AAAA,EACpH,CAAC,GAAG;AACN;AACA,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/B,QAAM,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE;AACvC,SAAO,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,IAAI,QAAQ,CAAC,GAAG,MAAM;AACvD,MAAE;AAAA,MACA;AAAA,MACA,CAAC,MAAM;AACL,cAAM,IAAI;AACV,UAAE,SAAS,OAAO,OAAO,GAAG,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAAA,MAC/C;AAAA,MACA;AAAA,MACA,CAAC,MAAM;AACL,UAAE,yCAAyC,CAAC,GAAG,EAAE,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAM,KAAqB,gBAAG;AAAA,EAC5B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,QAAQ,CAAC;AAAA,IACT,KAAK,CAAC;AAAA,EACR;AAAA,EACA,MAAM,MAAM,GAAG;AACb,QAAI,GAAG;AACP,UAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,iBAAG,MAAM,SAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,MAAM,GAAG,EAAE,GAAG;AAChF,WAAO,CAAC,GAAG,MAAM,WAAG,EAAE,QAAQ,WAAW,EAAE,MAAM,MAAE,CAAC,EAAE,CAAC;AAAA,EACzD;AACF,CAAC;AACD,IAAM,KAAN,cAAoB,KAAK;AAAA,EACvB,eAAe,GAAG;AAChB,UAAM,GAAG,CAAC;AACV,OAAG,MAAM,QAAQ,gBAAgB;AACjC,OAAG,MAAM,YAAY;AACrB,SAAK,aAAa,KAAK,IAAI;AAAA,EAC7B;AAAA,EACA,iBAAiB;AACf,UAAM,KAAK,KAAK,IAAI,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,MAAM,CAAC;AAC/E,SAAK,MAAM,IAAI,GAAG,GAAG,CAAC;AAAA,EACxB;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,OAAO,IAAI;AACpB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,QAAQ,CAAC;AACxB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,OAAO,KAAK;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,OAAO,KAAK;AACrB;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,OAAO,KAAK;AACrB;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1C;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,SAAS,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,GAAG,CAAC;AAC3E;AACA,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,QAAQ,OAAO,KAAK,YAAY,SAAS,KAAK,OAAO,EAAE,OAAO;AAC7E;AACA,SAAS,GAAG,GAAG;AACb,SAAO,GAAG,CAAC,KAAK,UAAU,KAAK,OAAO,EAAE,QAAQ;AAClD;AACA,SAAS,GAAG,GAAG;AACb,SAAO,CAAC,EAAE,KAAK,QAAQ,EAAE;AAC3B;AACA,SAAS,GAAG,GAAG;AACb,SAAO,aAAa;AACtB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC;AACvC;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACrB;AACA,IAAM,KAAK,CAAC,GAAG,MAAM;AACnB,aAAW,KAAK,OAAO,KAAK,CAAC;AAC3B,MAAE,CAAC,aAAa,UAAU,OAAO,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9D,SAAO,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG;AACpC;AAJA,IAIG,KAAK;AAJR,IAI0lB,KAAqB,GAAG,EAAE;AACpnB,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,EAAE,aAAa;AAC7B;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,QAAQ,aAAa,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC;AACzD;AACA,IAAM,KAAK;AACX,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,QAAQ,IAAI,KAAK,EAAE,YAAY;AAC1C;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,IAAoB,uBAAO,OAAO,IAAI,GAAG,IAAI,EAAE,MAAM,GAAG;AAC9D,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,MAAE,EAAE,CAAC,CAAC,IAAI;AACZ,SAAO,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AACvD;AACA,IAAM,KAAK,CAAC,GAAG,MAAM;AACnB,MAAI,CAAC;AACH;AACF,QAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,EAAE,MAAM,aAAa;AACtD,SAAO,KAAK,OAAO,SAAS,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,GAAG,CAAC;AAC7D;AALA,IAKG,KAAK,CAAC,GAAG,GAAG,MAAM;AACnB,QAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,EAAE,MAAM,aAAa;AACtD,OAAK,EAAE,OAAO,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC,MAAM,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,SAAS,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC;AACxG;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG;AAClB,UAAM,IAAI,EAAE,YAAY,IAAI,EAAE;AAC9B,WAAO,EAAE,WAAW,EAAE,SAAS,QAAK,MAAM,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC;AAAA,EAC5G;AACA,MAAI,MAAM;AACR,WAAO;AACT,MAAI,MAAM,QAAQ,OAAO,KAAK,YAAY,MAAM,QAAQ,OAAO,KAAK;AAClE,WAAO;AACT,QAAM,IAAI,OAAO,KAAK,CAAC,GAAG,IAAI,OAAO,KAAK,CAAC;AAC3C,MAAI,EAAE,WAAW,EAAE;AACjB,WAAO;AACT,aAAW,KAAK;AACd,QAAI,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAClC,aAAO;AACX,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,KAAK,EAAE,WAAW,EAAE;AAC3D,WAAO;AACT,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,QAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAChB,aAAO;AACX,SAAO;AACT;AACA,IAAM,KAAK,MAAM;AACjB,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,QAAM,IAAI,CAAC,MAAM;AACf,QAAI,EAAE,SAAS;AACb,aAAO;AACT,eAAW,KAAK,EAAE,UAAU;AAC1B,YAAM,IAAI,EAAE,CAAC;AACb,UAAI;AACF,eAAO;AAAA,IACX;AAAA,EACF,GAAG,IAAI,EAAE,CAAC;AACV,MAAI,CAAC,GAAG;AACN,YAAQ,KAAK,0CAA0C;AACvD;AAAA,EACF;AACA,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG;AAChC,QAAI,EAAE,EAAE,CAAC,CAAC,MAAM;AACd,UAAI,EAAE,EAAE,CAAC,CAAC;AAAA,SACP;AACH,cAAQ,KAAK,+BAA+B,EAAE,KAAK,GAAG,CAAC,EAAE;AACzD;AAAA,IACF;AACF,QAAM,IAAI,EAAE,EAAE,SAAS,CAAC;AACxB,IAAE,CAAC,MAAM,SAAS,EAAE,CAAC,IAAI,IAAI,QAAQ,KAAK,+BAA+B,EAAE,KAAK,GAAG,CAAC,EAAE;AACxF;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,IAAI,kBAAG;AAAA,IACf,OAAO;AAAA;AAAA,IAEP,aAAa;AAAA,IACb,SAAS;AAAA,IACT,WAAW;AAAA;AAAA,IAEX,MAAM;AAAA;AAAA,EAER,CAAC;AACD,SAAO,IAAI,GAAG,EAAE,SAAS,MAAM,GAAG,CAAC;AACrC;AACA,SAAS,GAAG,GAAG;AACb,MAAI;AACJ,MAAI,IAAI,EAAE;AACV,SAAO,EAAE,WAAW,IAAI,EAAE,UAAU,QAAQ,EAAE,YAAY,IAAI,EAAE,MAAM,WAAW,MAAM,QAAQ,EAAE,KAAK,MAAM,IAAI,IAAI,QAAG,GAAG,CAAC,IAAI;AACjI;AACA,SAAS,GAAG,GAAG;AACb,SAAO,SAAS;AAClB;AACA,SAAS,GAAG,GAAG;AACb,KAAG,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,QAAQ,GAAG,EAAE,QAAQ;AAC/C;AACA,SAAS,GAAG,GAAG;AACb,MAAI,GAAG;AACP,MAAI,EAAE,YAAY,IAAI,EAAE,qBAAqB,QAAQ,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,aAAa,QAAK;AAC3I,UAAM,IAAI;AACV,WAAO,IAAI,EAAE,YAAY,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,SAAS,QAAQ,GAAG,MAAM,QAAQ,EAAE,QAAQ,IAAI,EAAE,SAAS,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,YAAY,GAAG,EAAE,QAAQ;AAAA,EAC7K;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,MAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AAC9B,SAAO,EAAE,SAAS,GAAG;AACvB;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI;AACR,MAAI,EAAE,SAAS,GAAG,GAAG;AACnB,UAAM,IAAI,EAAE,MAAM,GAAG;AACrB,QAAI,IAAI,EAAE,MAAM;AAChB,WAAO,KAAK,EAAE;AACZ,WAAK,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,IAAI,GAAG,GAAG,EAAE,MAAM,CAAC;AAC1D,WAAO,EAAE,QAAQ,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,EACvC;AACE,WAAO,EAAE,QAAQ,GAAG,KAAK,EAAE;AAC/B;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE,IAAI,CAAC,GAAG,MAAM,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE;AACtF;AACA,IAAM,KAAK;AACX,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,GAAG,CAAC,GAAG;AACT,QAAI,GAAG,KAAK,CAAC,GAAG;AACd,YAAM,IAAI,EAAE,QAAQ,IAAI,EAAE,GAAG,EAAE,QAAQ,GAAG,KAAK,EAAE,IAAI,GAAG,GAAG,CAAC;AAC5D,UAAI,CAAC,MAAM,QAAQ,EAAE,CAAC,CAAC,GAAG;AACxB,cAAM,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;AACrB,UAAE,eAAe,MAAM;AACrB,YAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,QACnC,GAAG,EAAE,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,UAAM,EAAE,QAAQ,GAAG,KAAK,EAAE,IAAI,GAAG,GAAG,CAAC;AACrC,MAAE,OAAO,iBAAiB,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EAC5C;AACE,MAAE,OAAO,iBAAiB,EAAE,GAAG,CAAC;AACpC;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,GAAG,GAAG;AACV,MAAI,GAAG,CAAC,GAAG;AACT,UAAM,EAAE,QAAQ,GAAG,KAAK,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,OAAO;AACrD,UAAM,SAAS,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,kBAAkB,KAAK,EAAE,aAAa;AAAA,EAC/E;AACE,KAAC,KAAK,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,mBAAmB,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC;AACpF,GAAC,IAAI,EAAE,WAAW,QAAQ,OAAO,EAAE;AACrC;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,QAAM,IAAI;AACV,SAAO,EAAE,SAAS;AAAA,IAChB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,UAAU,CAAC;AAAA,IACX,eAAe,CAAC;AAAA,IAChB,SAAS,CAAC;AAAA,IACV,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL,GAAG,EAAE,OAAO,WAAW,EAAE,aAAa,EAAE,OAAO,SAAS,aAAa,EAAE,mBAAmB,EAAE,OAAO,SAAS,aAAa,EAAE,UAAU,EAAE,OAAO,SAAS,SAAS;AAClK;AACA,SAAS,GAAG,GAAG;AACb,MAAI;AACJ,QAAM,KAAK,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,OAAO,SAAS,EAAE;AACnE,OAAK,EAAE,UAAU,EAAE,OAAO,iBAAiB,SAAS,EAAE,WAAW;AACnE;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI;AACJ,MAAI,CAAC,EAAE,EAAE,aAAa;AACpB;AACF,MAAI,IAAI;AACR,MAAI,KAAK,GAAG,CAAC,KAAK,EAAE,UAAU,GAAG;AAC/B,UAAM,CAAC,GAAG,CAAC,IAAI;AACf,QAAI,UAAG,MAAM,GAAG,GAAG,CAAC;AAAA,EACtB,MAAO,IAAG,CAAC,IAAI,IAAI,IAAI,IAAI;AAC3B,UAAQ,IAAI,EAAE,kBAAkB,OAAO,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC;AACjF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,QAAM,IAAI,CAAC,GAAG,EAAE,OAAO,OAAO,GAAG,IAAI,EAAE,CAAC;AACxC,MAAI,IAAI,EAAE,CAAC,GAAG,MAAM;AAClB,WAAO;AACT,QAAM,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,OAAO,UAAU,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO,cAAc;AAC5G,SAAO,EAAE;AACT,aAAW,KAAK;AACd,OAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAE,OAAO,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC;AACjC,aAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,CAAC;AACnC,MAAE,UAAU,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;AAC3B,IAAE,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC;AACnB,aAAW,KAAK;AACd,MAAE,OAAO,GAAG,CAAC;AACf,SAAO;AACT;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,GAAG,CAAC,KAAK,EAAE,OAAO,SAAS,EAAE,QAAQ,EAAE,UAAU;AAC1D;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG,GAAG,GAAG;AACb,QAAM,MAAM,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,WAAW,EAAE,MAAM;AAClE,IAAE,WAAW,EAAE,OAAO,SAAS,OAAO,KAAK,EAAE,UAAU,aAAa,EAAE,UAAU,GAAG,EAAE,OAAO,SAAS,CAAC,MAAM,MAAM,CAAC,IAAI,IAAI,EAAE,WAAW,QAAQ,EAAE,SAAS,GAAG,GAAG,GAAG,EAAE,OAAO,MAAM,MAAM,KAAK,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,WAAW,QAAQ,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,SAAS;AAClR;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI;AACJ,GAAC,IAAI,EAAE,aAAa,QAAQ,EAAE,KAAK,GAAG,CAAC,MAAM;AAC3C,QAAI;AACJ,MAAE,iBAAiB,CAAC,IAAI,IAAI,EAAE,iBAAiB,QAAQ,EAAE,8BAA8B,CAAC;AAAA,EAC1F,CAAC,GAAG,EAAE,iBAAiB,CAAC,GAAG,GAAG,CAAC;AACjC;AACA,eAAe,GAAG,GAAG,GAAG;AACtB,QAAM,IAAI,IAAI,cAAG,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,MAAM;AACpD,MAAE;AAAA,MACA;AAAA,MACA,CAAC,MAAM,EAAE,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AACJ,UAAE,IAAI,MAAM,wCAAwC,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,GAAG,CAAC,GAAG;AACT,UAAM,IAAI,MAAM,QAAQ,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC9C,WAAO,EAAE,SAAS,IAAI,IAAI,EAAE,CAAC;AAAA,EAC/B,OAAO;AACL,UAAM;AAAA,MACJ,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,MACP,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,IAAI;AACJ,WAAO;AAAA,MACL,KAAK,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MACtB,iBAAiB,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MAClC,WAAW,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MAC5B,cAAc,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MAC/B,cAAc,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MAC/B,OAAO,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MACxB,UAAU,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,MAC3B,QAAQ,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAM,KAAqB,gBAAG;AAAA,EAC5B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,KAAK,CAAC;AAAA,IACN,iBAAiB,CAAC;AAAA,IAClB,WAAW,CAAC;AAAA,IACZ,cAAc,CAAC;AAAA,IACf,cAAc,CAAC;AAAA,IACf,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,IACX,QAAQ,CAAC;AAAA,EACX;AAAA,EACA,MAAM,MAAM,GAAG;AACb,QAAI,GAAG;AACP,UAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,iBAAG,MAAM,SAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,GAAG,EAAE,GAAG;AAClE,WAAO,CAAC,GAAG,MAAM,WAAG,EAAE,QAAQ,WAAW,EAAE,UAAU,MAAE,CAAC,EAAE,CAAC;AAAA,EAC7D;AACF,CAAC;AAjBD,IAiBI,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM;AACzB,QAAM,IAAI,IAAE,CAAC,CAAC,GAAG,IAAI;AAAA,IACnB,MAAM,EAAE,MAAM,CAAC;AAAA,EACjB,GAAG,IAAI,CAAC,MAAM;AACZ,UAAM,IAAI,aAAa,SAAK,IAAI,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC;AAChE,QAAI,CAAC;AACH;AACF,UAAM,IAAI,EAAE,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,MAAM,EAAE,IAAI;AACtD,MAAE,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,EACpB,GAAG,IAAI,CAAC,GAAG,IAAI,UAAO;AACpB,QAAI,GAAG,CAAC,GAAG;AACT,YAAM,IAAI;AACV,UAAI,EAAE,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,MAAM,MAAM,EAAE,IAAI;AAC5C;AACF,UAAI,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC;AAAA,IAC3B;AAAA,EACF,GAAG,IAAI,CAAC,MAAM;AACZ,QAAI,GAAG,CAAC,GAAG;AACT,YAAM,IAAI;AACV,QAAE,QAAQ,EAAE,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,MAAM,EAAE,IAAI;AAAA,IACxD;AAAA,EACF;AACA,SAAO,YAAG,MAAM;AACd,MAAE,YAAY,SAAS,EAAE,MAAM,QAAQ,CAAC,MAAM;AAC5C,OAAC,EAAE,WAAW,aAAa,qBAAM,GAAG,CAAC,OAAO,aAAa,oBAAK,EAAE,SAAS,EAAE,YAAY,SAAS,EAAE,OAAO,EAAE,MAAM,QAAQ,MAAM,EAAE,QAAQ,EAAE,MAAM,QAAQ,KAAK,EAAE,MAAM,EAAE,OAAO,QAAQ,KAAK,EAAE,SAAS,EAAE,OAAO,QAAQ,OAAO,EAAE,uBAAuB;AAAA,IAC1P,CAAC;AAAA,EACH,CAAC,GAAG,YAAE,MAAM;AACV,MAAE,QAAQ,CAAC;AAAA,EACb,CAAC,GAAG;AAAA,IACF,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EACnB;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,eAAe,sBAAsB,KAAK,EAAE;AACvD;AACA,IAAM,KAAK;AAAX,IAAe,KAAK;AACpB,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE,CAAC,KAAK,WAAW,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,QAAQ,MAAM,GAAG,CAAC;AAChF;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE,CAAC,KAAK,WAAW,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,QAAQ,KAAK,GAAG,CAAC;AAC/E;AACA,SAAS,GAAG,GAAG,GAAG;AAClB;AACA,SAAS,IAAI;AACX,SAAO;AAAA,IACL,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AACF;AACA,IAAM,KAAK,IAAE,CAAC,CAAC;AAAf,IAAkB,KAAK,CAAC,MAAM,OAAO,OAAO,GAAG,OAAO,CAAC;AACvD,SAAS,KAAK;AACZ,QAAM,IAAoB,oBAAI,IAAI,GAAG,IAAoB,oBAAI,IAAI;AACjE,MAAI,IAAI,GAAG,IAAI;AACf,QAAM,IAAI,MAAM;AACd,UAAM,IAAI,MAAM,KAAK,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAC/C,YAAM,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AAC/B,aAAO,MAAM,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO;AAAA,IAC3C,CAAC;AACD,MAAE,MAAM,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,EACzC,GAAG,IAAI,CAAC,MAAM;AACZ,MAAE,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC;AAAA,EACzB;AACA,SAAO,EAAE,IAAI,CAAC,GAAG,IAAI,MAAM;AACzB,MAAE,IAAI,GAAG,EAAE,UAAU,GAAG,MAAM,IAAI,CAAC;AACnC,UAAM,IAAI,MAAM,EAAE,CAAC;AACnB,WAAO,kBAAG,CAAC,GAAG,IAAI,MAAI;AAAA,MACpB,KAAK;AAAA,IACP;AAAA,EACF,GAAG,KAAK,GAAG,SAAS,IAAI,MAAM;AAC5B,UAAM,EAAE,GAAG,IAAI,QAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAAA,EAC9C,GAAG,SAAS,MAAM;AAChB,MAAE,MAAM,GAAG,EAAE,MAAM;AAAA,EACrB,GAAG,IAAI,QAAQ;AACb,WAAO,EAAE;AAAA,EACX,EAAE;AACJ;AACA,SAAS,KAAK;AACZ,MAAI,IAAI,MAAI,IAAI,MAAI,IAAI;AACxB,QAAM,IAAI,IAAI,MAAG,KAAE,GAAG,IAAI,IAAE,EAAE,OAAO,GAAG,IAAI,IAAE,KAAE;AAChD,MAAI;AACJ,QAAM,IAAI,UAAG,aAAa;AAC1B,MAAI,IAAI;AACR,QAAM,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG;AACjC,IAAE;AACF,MAAI,IAAI,CAAC;AACT,WAAS,EAAE,GAAG;AACZ,QAAI;AAAA,EACN;AACA,WAAS,EAAE,GAAG,GAAG,IAAI,GAAG;AACtB,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,eAAO,EAAE,GAAG,GAAG,CAAC;AAAA,MAClB,KAAK;AACH,eAAO,MAAM,IAAI,IAAI,EAAE,QAAQ,GAAG,EAAE,GAAG,CAAC;AAAA,MAC1C,KAAK;AACH,eAAO,EAAE,GAAG,GAAG,CAAC;AAAA,IACpB;AAAA,EACF;AACA,WAAS,IAAI;AACX,UAAM,IAAI,OAAI,EAAE,GAAG,EAAE;AAAA,EACvB;AACA,WAAS,IAAI;AACX,QAAI,MAAI,EAAE,GAAG,qBAAqB,CAAC;AAAA,EACrC;AACA,WAASC,KAAI;AACX,QAAI,OAAI,EAAE;AAAA,EACZ;AACA,WAAS,IAAI;AACX,QAAI,MAAI,EAAE;AAAA,EACZ;AACA,WAAS,IAAI;AACX,MAAE,QAAQ;AAAA,EACZ;AACA,WAAS,IAAI;AACX,MAAE,QAAQ;AAAA,EACZ;AACA,WAAS,IAAI;AACX,QAAI,CAAC,GAAG;AACN,UAAI,sBAAsB,CAAC;AAC3B;AAAA,IACF;AACA,UAAM,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI;AAAA,MAClD,QAAQ,MAAE,EAAE,MAAM;AAAA,MAClB,OAAO,MAAE,EAAE,KAAK;AAAA,MAChB,UAAU,MAAE,EAAE,QAAQ;AAAA,MACtB,WAAW,MAAE,EAAE,SAAS;AAAA,MACxB,UAAU,MAAE,EAAE,QAAQ;AAAA,MACtB,YAAY,EAAE;AAAA,MACd,SAAS,EAAE;AAAA,IACb,GAAG,IAAI,EAAE,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,GAAG,EAAE;AAC9C,MAAE,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,sBAAsB,CAAC;AAAA,EAChI;AACA,WAAS,IAAI;AACX,UAAM,IAAI,CAAC,KAAK,CAAC;AACjB,MAAE,YAAY,MAAM,EAAE,UAAU,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI,EAAE,QAAQ,EAAE;AAAA,EACrE;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,UAAU,CAAC,GAAG,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC;AAAA,IAChC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQA;AAAA,IACR,aAAa;AAAA,IACb,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,UAAU,CAAC,MAAM,IAAI;AAAA,EACvB;AACF;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI;AACR,SAAO,EAAE,SAAS,CAAC,MAAM;AACvB,QAAI,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,kBAAkB;AACzD,YAAM,IAAI,EAAE,UAAU,IAAI,EAAE,WAAW,SAAS,QAAQ,IAAI,aAAa,mBAAmB,IAAI,EAAE,QAAQ,EAAE,MAAM,QAAQ,YAAY,oBAAoB,GAAG,IAAI,EAAE,WAAW,SAAS,EAAE,WAAW,OAAO,QAAQ,IAAI,aAAa,oBAAoB,GAAG,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,GAAG,QAAQ,IAAI,aAAa,oBAAoB,GAAG,IAAI,IAAI,IAAI,IAAI;AACjW,WAAK;AAAA,IACP;AAAA,EACF,CAAC,GAAG;AACN;AACA,SAAS,GAAG,GAAG;AACb,UAAQ,IAAI,MAAM,QAAQ,CAAC;AAC7B;AACA,IAAM,KAAK,OAAO,SAAS,SAAG,QAAQ,OAAO,EAAE,CAAC;AAChD,SAAS,GAAG,GAAG;AACb,SAAO,OAAO,KAAK,WAAW,CAAC,GAAG,GAAG,CAAC,IAAI,aAAa,UAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI;AAChF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,aAAa,QAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAE,GAAG,CAAC,IAAI,IAAI,MAAE,CAAC;AACtE;AACA,IAAM,KAAK;AAAA,EACT,WAAW;AAAA,IACT,SAAS;AAAA,IACT,yBAAyB;AAAA,IACzB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,qBAAqB;AAAA,IACrB,WAAW;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,aAAa;AAAA,IACb,qBAAqB;AAAA,EACvB;AACF;AACA,SAAS,GAAG;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,cAAc,EAAE,OAAO,GAAG,QAAQ,GAAG,YAAY,GAAG,SAAS,EAAE;AACjE,GAAG;AACD,QAAM,IAAI,SAAE,OAAO;AAAA,IACjB,OAAOC,SAAE,EAAE,KAAK,KAAK;AAAA,IACrB,OAAOA,SAAE,EAAE,KAAK;AAAA,IAChB,QAAQ,aAAG,CAAC;AAAA,IACZ,SAASA,SAAE,EAAE,OAAO;AAAA,IACpB,SAASA,SAAE,EAAE,OAAO;AAAA,IACpB,WAAWA,SAAE,EAAE,SAAS,KAAK;AAAA,IAC7B,WAAWA,SAAE,EAAE,SAAS;AAAA,IACxB,iBAAiBA,SAAE,EAAE,eAAe;AAAA,IACpC,oBAAoBA,SAAE,EAAE,kBAAkB;AAAA,IAC1C,uBAAuBA,SAAE,EAAE,qBAAqB;AAAA,IAChD,wBAAwBA,SAAE,EAAE,sBAAsB;AAAA,IAClD,8BAA8BA,SAAE,EAAE,4BAA4B;AAAA,EAChE,EAAE,GAAG,IAAI,WAAE,IAAI,cAAG,EAAE,KAAK,CAAC;AAC1B,WAAS,IAAI;AACX,MAAE,eAAe,eAAe,EAAE;AAAA,EACpC;AACA,QAAG,GAAG,MAAM;AACV,MAAE,MAAM,QAAQ,GAAG,EAAE,QAAQ,IAAI,cAAG,EAAE,KAAK,GAAG,EAAE;AAAA,EAClD,CAAC,GAAG,MAAG,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM;AAChC,MAAE,MAAM,QAAQ,EAAE,MAAM,OAAO,EAAE,OAAO,KAAK,GAAG,EAAE;AAAA,EACpD,GAAG;AAAA,IACD,WAAW;AAAA,EACb,CAAC,GAAG,MAAG,MAAM,EAAE,YAAY,CAAC;AAC5B,QAAM,EAAE,YAAY,EAAE,IAAI,oBAAG,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,KAAK,MAAM;AAChE,UAAM,IAAI,IAAI,cAAG,GAAG,IAAI;AAAA,MACtB,WAAW;AAAA,QACT,SAAS,EAAE,UAAU;AAAA,QACrB,MAAM,EAAE,UAAU;AAAA,MACpB;AAAA,MACA,aAAa,EAAE;AAAA,MACf,qBAAqB,EAAE;AAAA,MACvB,kBAAkB,EAAE;AAAA,IACtB;AACA,WAAO,EAAE,QAAQ,GAAG;AAAA,EACtB,GAAG,GAAG,IAAIA,SAAE,EAAE,UAAU;AACxB,SAAO,MAAM,eAAe,EAAE,GAAG,MAAM,YAAY,WAAW,MAAM;AAClE,MAAE;AAAA,EACJ,GAAG,GAAG,GAAG,YAAG,MAAM;AAChB,UAAM,IAAIA,SAAE,EAAE,MAAM;AACpB,UAAM,KAAK,MAAM,EAAE,yCAAyC,OAAO,KAAK,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,OAAOA,SAAE,EAAE,GAAG,CAAC,GAAG,MAAM,aAAa,EAAE,OAAO,QAAQ,KAAK,IAAI,GAAG,EAAE,OAAO,KAAK;AAC9M,UAAM,IAAI,CAAC,GAAG,MAAM;AAClB,YAAM,IAAIA,SAAE,CAAC,GAAG,IAAI,MAAM;AACxB,YAAI;AACF,iBAAO,GAAG,GAAG,CAAC,GAAG,CAAC;AAAA,MACtB;AACA,UAAI,MAAM;AACR,eAAO;AACT,YAAM,IAAI,EAAE;AACZ,aAAO,MAAM,SAAS,IAAI,GAAG,GAAG,CAAC;AAAA,IACnC,GAAG,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC;AACvC,MAAE,EAAE,SAAS,mBAAmB,GAAG,EAAE,EAAE,eAAe,uBAAI,aAAa,GAAG,EAAE,EAAE,eAAe,gBAAgB,GAAG,KAAK,OAAO,EAAE,CAAC,EAAE,iBAAiB,yBAAyB,GAAG,EAAE,EAAE,kBAAkB,kBAAkB,GAAG,EAAE,EAAE,qBAAqB,qBAAqB;AACvQ,UAAMD,KAAI,EAAE,EAAE,YAAY,YAAY;AACtC,IAAAA,MAAK,EAAE,MAAM;AAAA,MACXA,KAAI,GAAGA,EAAC,IAAI,IAAI,MAAE,CAAC;AAAA;AAAA,IAErB;AAAA,EACF,CAAC,GAAG,YAAE,MAAM;AACV,MAAE,MAAM,QAAQ,GAAG,EAAE,MAAM,iBAAiB;AAAA,EAC9C,CAAC,GAAG;AAAA,IACF,UAAU;AAAA,EACZ;AACF;AACA,SAAS,GAAG,GAAG,GAAG,IAAI,IAAI;AACxB,QAAM,IAAIC,SAAE,CAAC,IAAI,cAAG,IAAI,eAAG,SAAE,MAAMA,SAAE,CAAC,EAAE,aAAa,CAAC,GAAG,IAAI,SAAG,aAAG,EAAE,OAAO,CAAC,CAAC,GAAG,IAAI,SAAG,aAAG,EAAE,QAAQ,CAAC,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,QAAQ,EAAE,KAAK;AACvI,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AACF;AACA,IAAM,KAAK,CAAC,GAAG,MAAM;AACnB,QAAM,IAAI,SAAE,MAAM,EAAE,SAAS,MAAM,UAAU,GAAG,IAAI,WAAE,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,WAAG,EAAE,QAAQ,EAAE,CAAC;AAC5F,MAAI,IAAI;AACR,QAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,KAAK,GAAG,MAAM,EAAE,IAAI,mBAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,MAAM;AAC9E,QAAI,EAAE;AACJ,aAAO;AAAA,QACL,IAAI,IAAI,EAAE,SAAS,EAAE,QAAQ,IAAI;AAAA,QACjC,GAAG,GAAG,IAAI,EAAE,SAAS,EAAE,SAAS,IAAI;AAAA,MACtC;AAAA,EACJ,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,MAAM;AACzB,QAAI,EAAE,OAAO;AACX,aAAO,EAAE,UAAU,MAAM,cAAc,IAAI,QAAG,GAAG,CAAC,GAAG,EAAE,OAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU,MAAM,iBAAiB,EAAE,OAAO,IAAE,GAAG,EAAE;AAAA,EACvI,GAAG,IAAI,CAAC,MAAM;AACZ,UAAM,IAAI,EAAE;AAAA,MACV,IAAI,KAAK,OAAO,SAAS,EAAE,YAAY,EAAE;AAAA,MACzC,IAAI,KAAK,OAAO,SAAS,EAAE,YAAY,EAAE;AAAA,IAC3C,CAAC;AACD,WAAO,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;AAAA,EAC3B,GAAG,IAAI,gBAAE,GAAG,IAAI,gBAAE,GAAG,IAAI,gBAAE,GAAGD,KAAI,gBAAE,GAAG,IAAI,gBAAE,GAAG,IAAI,gBAAE,GAAG,IAAI,gBAAE,GAAG,IAAI,gBAAE;AACxE,WAAS,EAAE,GAAG;AACZ,UAAM,IAAI,CAAC;AACX,eAAW,KAAK;AACd,aAAO,KAAK,eAAe,EAAE,CAAC,IAAI,EAAE,CAAC;AACvC,WAAO;AAAA,EACT;AACA,QAAM,IAAI,CAAC,GAAG,MAAM;AAClB,QAAI,IAAI,IAAI;AACZ,UAAM,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,QAAG,KAAK,OAAO,SAAS,EAAE,SAAS,KAAK,OAAO,SAAS,EAAE,SAAS,CAAC,EAAE,WAAW,KAAK,EAAE,WAAW,OAAO,SAAS,GAAG,KAAK;AACpJ,MAAE,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,eAAe,EAAE;AAAA;AAAA,MAEjB,kBAAkB;AAAA,MAClB,MAAM,KAAK,EAAE,cAAc,OAAO,SAAS,GAAG,MAAM;AAAA,MACpD,SAAS,KAAK,EAAE,WAAW,OAAO,SAAS,GAAG;AAAA,MAC9C,aAAa;AAAA,MACb,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACA,MAAI;AACJ,QAAM,IAAI,CAAC,MAAM;AACf,MAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI;AAAA,EACrB,GAAG,IAAI,MAAM;AACX,SAAK,EAAE,CAAC;AAAA,EACV;AACA,MAAI,GAAG,GAAG;AACV,QAAM,IAAI,CAAC,MAAM;AACf,QAAI;AACJ,SAAK,IAAI,EAAE,MAAM,CAAC,MAAM,OAAO,SAAS,EAAE,QAAQ,IAAI,GAAG,IAAI,IAAI;AAAA,OAC9D,KAAK,OAAO,SAAS,EAAE,YAAY,EAAE;AAAA,OACrC,KAAK,OAAO,SAAS,EAAE,YAAY,EAAE;AAAA,IACxC,GAAG,EAAE,GAAG,CAAC;AAAA,EACX;AACA,MAAI,GAAG,IAAI;AACX,QAAM,KAAK,CAAC,MAAM;AAChB,QAAI,GAAG,GAAG;AACV,iBAAa,iBAAiB,EAAE,MAAM,WAAW,KAAK,EAAE,GAAG,CAAC,GAAG,QAAQ,IAAI,EAAE,MAAM,CAAC,MAAM,OAAO,SAAS,EAAE,YAAY,IAAI,IAAI;AAAA,OAC7H,KAAK,OAAO,SAAS,EAAE,YAAY,EAAE;AAAA,OACrC,KAAK,OAAO,SAAS,EAAE,YAAY,EAAE;AAAA,IACxC,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,CAAC,GAAG,EAAE,WAAW,KAAK,EAAE,GAAG,CAAC,GAAG,QAAQ,IAAI,EAAE,MAAM,CAAC,MAAM,OAAO,SAAS,EAAE,UAAU,IAAI,QAAM,KAAK,KAAK,EAAE,MAAM,CAAC,MAAM,OAAO,SAAS,GAAG,QAAQ,IAAI,UAAO,EAAE,WAAW,KAAK,EAAE,GAAG,CAAC,IAAI,EAAEA,IAAG,CAAC;AAAA,EAC5O,GAAG,KAAK,CAAC,MAAM;AACb,UAAM,EAAE,GAAG,CAAC,GAAG,IAAI,QAAQ,IAAI;AAAA,EACjC,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;AAC1C,SAAO,EAAE,MAAM,iBAAiB,aAAa,EAAE,GAAG,EAAE,MAAM,iBAAiB,eAAe,CAAC,GAAG,EAAE,MAAM,iBAAiB,eAAe,CAAC,GAAG,EAAE,MAAM,iBAAiB,gBAAgB,EAAE,GAAG,EAAE,MAAM,iBAAiB,YAAY,EAAE,GAAG,EAAE,MAAM,iBAAiB,SAAS,EAAE,GAAG,YAAE,MAAM;AAC/Q,SAAK,QAAQ,EAAE,UAAU,EAAE,MAAM,oBAAoB,aAAa,EAAE,GAAG,EAAE,MAAM,oBAAoB,eAAe,CAAC,GAAG,EAAE,MAAM,oBAAoB,eAAe,CAAC,GAAG,EAAE,MAAM,oBAAoB,gBAAgB,EAAE,GAAG,EAAE,MAAM,oBAAoB,YAAY,EAAE,GAAG,EAAE,MAAM,oBAAoB,SAAS,EAAE;AAAA,EAC5S,CAAC,GAAG;AAAA,IACF,YAAY;AAAA,IACZ,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IACxB,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IAC3B,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IAC9B,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IAC9B,aAAa,CAAC,MAAMA,GAAE,GAAG,CAAC,EAAE;AAAA,IAC5B,eAAe,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IAC9B,iBAAiB,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IAChC,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAAA,IACxB,aAAa;AAAA,EACf;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,MAAM,QAAQ,CAAC;AACjB,eAAW,KAAK;AACd,QAAE,CAAC;AACP,SAAO,KAAK,cAAc,EAAE,CAAC;AAC/B;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI;AACJ,QAAM,IAAI,WAAE,GAAG,IAAI,WAAE;AACrB,QAAM,EAAE,QAAQ,IAAI,MAAM,EAAE,QAAQ;AACpC,QAAM,IAAI,CAAC,MAAM;AACf,QAAI;AACJ,aAAS,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,cAAc;AAAA,EAC5D,GAAG,IAAI,CAAC,MAAM;AACZ,QAAI;AACJ,aAAS,IAAI,EAAE,aAAa,OAAO,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;AAAA,EACzE,GAAG,IAAI,aAAI,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,UAAU,OAAO,CAAC,KAAK,CAAC,CAAC;AACtE,WAAS,EAAE,GAAG,GAAG;AACf,UAAM,IAAI,CAAC,GAAG,IAAI,MAAM,EAAE,kBAAkB;AAC5C,MAAE,kBAAkB;AACpB,eAAW,KAAK,KAAK,OAAO,SAAS,EAAE,eAAe;AACpD,UAAI,EAAE;AACJ;AACF,UAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AACjB,YAAM,EAAE,QAAQ,EAAE,IAAI;AACtB,QAAE,cAAc,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC,UAAI,IAAI,EAAE;AACV,aAAO,MAAM,QAAQ,CAAC,EAAE,mBAAmB,CAAC,EAAE,SAAS,CAAC;AACtD,UAAE,cAAc,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,EAAE;AACnD,YAAM,IAAI,GAAG,EAAE,MAAM,CAAC,CAAC;AACvB,QAAE,GAAG,EAAE,cAAc,GAAG,OAAO,EAAE,CAAC;AAAA,IACpC;AAAA,EACF;AACA,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,aAAaA;AAAA,EACf,IAAI,GAAG,GAAG,CAAC;AACX,IAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACrL,MAAI,IAAI,CAAC;AACT,IAAE,CAAC,MAAM;AACP,UAAM,IAAI,EAAE,cAAc,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,IAAI,EAAE;AAC3D,MAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,MAAM;AAC3B,QAAE,SAAS,CAAC,MAAM,EAAE,gBAAgB,GAAG,EAAE,kBAAkB,CAAC,GAAG,EAAE,gBAAgB,CAAC;AAAA,IACpF,CAAC,GAAG,EAAE,gBAAgB,GAAG,EAAE,cAAc,QAAQ,CAAC,EAAE,QAAQ,EAAE,MAAM;AAClE,QAAE,SAAS,CAAC,MAAM,EAAE,kBAAkB,CAAC,GAAG,EAAE,iBAAiB,CAAC;AAAA,IAChE,CAAC,GAAG,EAAE,iBAAiB,CAAC,GAAG,IAAI,EAAE;AAAA,EACnC,CAAC;AACD,QAAM,IAAI,CAAC;AACX,IAAE,CAAC,MAAM;AACP,UAAM,IAAI,MAAM,EAAE,kBAAkB;AACpC,MAAE,kBAAkB,GAAG,EAAE,QAAQ,CAAC,MAAM;AACtC,QAAE,oBAAoB,EAAE,cAAc,GAAG,GAAG,EAAE,iBAAiB,CAAC;AAAA,IAClE,CAAC,GAAG,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC;AAAA,EACtC,CAAC;AACD,WAAS,EAAE,GAAG;AACZ,OAAG,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC;AAAA,EACjC;AACA,WAAS,EAAE,GAAG;AACZ,QAAI,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG;AACjB,YAAM,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC3B,UAAI,MAAM,EAAE,MAAM,OAAO,GAAG,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,WAAS,EAAE,GAAG;AACZ,OAAG,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC;AAAA,EAChD;AACA,WAAS,EAAE,GAAG;AACZ,QAAI,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG;AACjB,YAAM,IAAI,EAAE,QAAQ,CAAC;AACrB,UAAI,MAAM,EAAE,OAAO,GAAG,CAAC;AAAA,IACzB;AAAA,EACF;AACA,SAAO,EAAE,eAAe;AAAA,IACtB,aAAaA;AAAA,IACb,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,EACjC,GAAG;AAAA,IACD,aAAaA;AAAA,IACb,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,EACjC;AACF;AACA,SAAS,GAAG,GAAG,GAAG,IAAI,KAAK;AACzB,MAAI,KAAK,IAAI,MAAM;AACnB,QAAM,IAAI,gBAAE,GAAG,IAAoB,oBAAI,IAAI;AAC3C,MAAI,IAAI,OAAI,IAAI,OAAI,IAAI;AACxB,WAAS,IAAI;AACX,SAAK,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,QAAM,CAAC,KAAK,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC;AAAA,EACrI;AACA,WAAS,IAAI;AACX,QAAI,MAAI,KAAK,aAAa,CAAC;AAAA,EAC7B;AACA,IAAE;AACF,QAAM,IAAI,CAAC,MAAM,MAAM;AACrB,MAAE,GAAG,CAAC;AAAA,EACR;AACA,SAAO;AAAA,IACL,IAAI,CAAC,MAAM;AACT,UAAI;AACF,eAAO,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,MAAM;AAAA,QAC7B,EAAE;AACJ;AACE,cAAM,IAAI,EAAE,GAAG,CAAC;AAChB,eAAO,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,KAAK,EAAE;AAAA,IACP,SAAS,EAAE;AAAA,IACX,OAAO,EAAE;AAAA,IACT,QAAQ;AAAA,EACV;AACF;AACA,IAAM,KAAqB,oBAAI,QAAQ;AACvC,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;AACzB,WAAO,GAAG,IAAI,CAAC;AACjB,QAAM,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM;AAC1C,QAAI,KAAK,IAAI,IAAI,KAAK;AACpB,aAAO;AACT;AACE,YAAM,IAAI,EAAE,SAAS,OAAO,KAAK,KAAK,OAAO,SAAS,EAAE,eAAe,EAAE,OAAO,GAAG,QAAQ,EAAE;AAC7F,aAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,KAAK,EAAE,SAAS;AAAA,IAC3C;AAAA,EACF,GAAG,CAAC;AACJ,SAAO,GAAG,IAAI,GAAG,CAAC,GAAG;AACvB;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,GAAG;AACb,MAAI;AACF,WAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC;AACnD;AACA,SAAS,GAAG;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,MAAM;AACR,GAAG;AACD,QAAM,IAAI,WAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;AAAA,IAC5B,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EACnB,IAAI,GAAG,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI;AAAA,IACxB,MAAM,IAAE,EAAE,cAAc,QAAQ;AAAA,IAChC,UAAU,IAAE,CAAC;AAAA,IACb,QAAQ,IAAE,CAAC;AAAA,IACX,WAAW;AAAA,IACX,kBAAkB,SAAE,MAAM,EAAE,KAAK,UAAU,eAAe,EAAE,OAAO,UAAU,CAAC;AAAA,EAChF;AACA,WAAS,EAAE,IAAI,GAAG;AAChB,MAAE,eAAe,gBAAgB,EAAE,OAAO,QAAQ,KAAK,IAAI,EAAE,WAAW,EAAE,OAAO,QAAQ,CAAC;AAAA,EAC5F;AACA,WAAS,IAAI;AACX,MAAE,eAAe,aAAa,EAAE,OAAO,QAAQ;AAAA,EACjD;AACA,QAAM,EAAE,UAAU,EAAE,IAAI;AAAA,IACtB;AAAA,MACE,QAAQ;AAAA,MACR,SAAS;AAAA;AAAA,MAET,cAAc,EAAE,OAAO,GAAG,QAAQ,GAAG,YAAY,GAAG,SAAS,EAAE;AAAA,IACjE;AAAA,EACF,GAAG,IAAI;AAAA,IACL,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS,SAAG,CAAC;AAAA,IACb,UAAU;AAAA,IACV,WAAW,WAAE,IAAI,UAAG,CAAC;AAAA,IACrB,UAAU,IAAE,IAAI;AAAA,IAChB,MAAM;AAAA,MACJ,WAAW;AAAA,MACX,KAAK;AAAA,QACH,OAAO;AAAA,QACP,aAAa,CAAC;AAAA,MAChB;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,aAAa,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,MAAM,GAAG;AAAA,EACX;AACA,UAAG,WAAW,CAAC,GAAG,EAAE,MAAM,MAAM,SAAS;AAAA,IACvC,MAAM;AAAA,EACR,GAAG,EAAE,KAAK,SAAS,MAAM;AACvB,MAAE,SAAS,EAAE,OAAO,QAAQ,MAAM,EAAE,MAAM,OAAO,GAAG,EAAE,KAAK,GAAG,EAAE,UAAU,EAAE,SAAS,KAAK,IAAI,EAAE,SAAS,QAAQ,GAAG,EAAE,KAAK,UAAU,WAAW,EAAE,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,KAAK,IAAI,GAAG,EAAE,OAAO,QAAQ,CAAC;AAAA,EACtN,GAAG,QAAQ;AACX,QAAM,EAAE,IAAIA,IAAG,QAAQ,EAAE,IAAI,GAAG,CAAC;AACjC,IAAE,KAAK,SAAS,KAAE,GAAG,EAAE,KAAK,MAAM,GAAGA,GAAE,MAAM;AAC3C,MAAE,SAAS,CAAC,GAAG,EAAE,KAAK,SAAS,IAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAChD,CAAC,GAAG,YAAE,MAAM;AACV,MAAE,GAAG,EAAE,KAAK,KAAK;AAAA,EACnB,CAAC;AACD,QAAM,IAAI,KAAK,IAAI,OAAG,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,aAAa,GAAG,QAAQ,EAAE,IAAI,UAAG,EAAE,UAAU,EAAE,CAAC,GAAG,IAAI;AAC9F,MAAI,IAAI,YAAY,IAAI;AACxB,QAAM,IAAI,CAAC,EAAE,WAAW,EAAE,MAAM;AAC9B,MAAE,MAAM,UAAU,EAAE,KAAK,OAAO,eAAe,GAAG,EAAE,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI,GAAG,EAAE,KAAK,IAAI,YAAY,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,YAAY,SAAS,KAAK,EAAE,KAAK,IAAI,YAAY,MAAM,GAAG,EAAE,KAAK,IAAI,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,OAAO,YAAY,KAAK,EAAE,MAAM,iBAAiB,OAAO,IAAI,GAAG,EAAE,KAAK,OAAO,YAAY,SAAS,KAAK,EAAE,KAAK,OAAO,YAAY,MAAM,GAAG,EAAE,KAAK,OAAO,aAAa,EAAE,KAAK,OAAO,YAAY,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,OAAO,YAAY;AAAA,EACtf;AACA,MAAI,IAAI;AACR,QAAM,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,SAAG,CAAC,EAAE,OAAO,EAAE,MAAM;AAC/C,WAAO,uBAAuB,EAAE,EAAE,WAAW,YAAY,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK,MAAM,OAAO,mBAAmB,GAAG,CAAC,GAAG,IAAI;AAAA,EAC7H,GAAG,EAAE,WAAW,KAAG,CAAC;AACpB,SAAO,YAAE,MAAM;AACb,MAAE;AAAA,EACJ,CAAC,GAAG;AACN;AACA,SAAS,KAAK;AACZ,QAAM,IAAI,OAAG,SAAS;AACtB,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kEAAkE;AACpF,SAAO;AACT;AACA,IAAM,KAAK;AACX,SAAS,KAAK;AACZ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,EACX,IAAI,GAAG;AACP,IAAE,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,EACX,CAAC;AACD,WAAS,EAAE,GAAG,IAAI,GAAG;AACnB,WAAO,EAAE,SAAS,GAAG,UAAU,CAAC;AAAA,EAClC;AACA,WAAS,EAAE,GAAG;AACZ,WAAO,EAAE,SAAS,GAAG,QAAQ;AAAA,EAC/B;AACA,WAAS,EAAE,GAAG,IAAI,GAAG;AACnB,WAAO,EAAE,SAAS,GAAG,SAAS,CAAC;AAAA,EACjC;AACA,SAAO;AAAA,IACL,OAAO,EAAE;AAAA,IACT,QAAQ,EAAE;AAAA,IACV,aAAa,EAAE;AAAA,IACf,cAAc,EAAE;AAAA,IAChB,UAAU,EAAE;AAAA,IACZ,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,eAAe;AAAA,EACjB;AACF;AACA,IAAM,KAAK,gBAAE;AAAb,IAAgB,KAAK,gBAAE;AAAvB,IAA0B,KAAK,gBAAE;AAAjC,IAAoC,KAAK,IAAI,MAAG;AAChD,IAAI,KAAK;AAAT,IAAY,KAAK;AACjB,IAAM,EAAE,OAAO,IAAI,QAAQ,IAAI,UAAU,GAAG,IAAI;AAAA,EAC9C,MAAM;AACJ,OAAG,QAAQ,EAAE,OAAO,IAAI,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,QAAQ,EAAE,OAAO,IAAI,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,QAAQ,EAAE,OAAO,IAAI,SAAS,IAAI,OAAO,GAAG,CAAC;AAAA,EACxJ;AAAA,EACA,EAAE,WAAW,MAAG;AAClB;AACA,GAAG,GAAG,MAAM;AACV,OAAK,GAAG,SAAS,GAAG,KAAK,GAAG,eAAe;AAC7C,CAAC;AACD,IAAI,KAAK;AACT,IAAM,KAAK,OAAO,OAAO,KAAK,MAAI,GAAG,IAAI;AAAA,EACvC,cAAc,GAAG;AAAA,EACjB,QAAQ,GAAG;AAAA,EACX,aAAa,GAAG;AAAA,EAChB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,SAAS,KAAK;AACZ,QAAM,EAAE,YAAY,EAAE,IAAI,EAAE;AAC5B,WAAS,EAAE,GAAG,GAAG,GAAG;AAClB,QAAI,IAAI;AACR,WAAO,EAAE,SAAS,CAAC,MAAM;AACvB,QAAE,CAAC,MAAM,MAAM,IAAI;AAAA,IACrB,CAAC,GAAG,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,cAAc,GAAG;AAAA,EACnD;AACA,WAAS,EAAE,GAAG,GAAG,GAAG;AAClB,UAAM,IAAI,CAAC;AACX,WAAO,EAAE,SAAS,CAAC,MAAM;AACvB,QAAE,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;AAAA,IAC9B,CAAC,GAAG,EAAE,UAAU,EAAE,iBAAiB,CAAC,KAAK,CAAC,cAAc,GAAG;AAAA,EAC7D;AACA,WAAS,EAAE,GAAG,GAAG;AACf,WAAO,EAAE,GAAG,QAAQ,CAAC;AAAA,EACvB;AACA,WAAS,EAAE,GAAG,GAAG;AACf,WAAO,EAAE,GAAG,QAAQ,CAAC;AAAA,EACvB;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,eAAe;AAAA,EACjB;AACF;AACA,SAAS,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG;AAC7B,MAAI,IAAI;AACR,QAAM,IAAI,CAAC,MAAM;AACf,QAAI;AAAA,EACN;AACA,MAAI,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AACxB,QAAM,IAAI;AAAA,IACR,IAAI,GAAG,GAAG;AACR,aAAO,KAAK,KAAK,KAAK;AAAA,IACxB;AAAA,IACA,IAAI,GAAG,GAAG,GAAG;AACX,aAAO,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/B;AAAA,IACA,IAAI,GAAG,GAAG,GAAG;AACX,aAAO,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAC7C;AAAA,EACF;AACA,SAAO,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG;AAC/B;AACA,IAAM,EAAE,UAAU,GAAG,IAAI,EAAE;AAA3B,IAA8B,KAAK;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAfA,IAeG,KAAK,CAAC,MAAM;AACb,QAAM,IAAI,EAAE,MAAM;AAClB,WAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,QAAI,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,IAAI,MAAM,cAAc,GAAG,CAAC;AAClE,aAAO;AACT,QAAI,IAAI,EAAE,QAAQ,QAAQ,EAAE,GAAG;AAC/B,QAAI,MAAM,aAAa;AACrB,OAAC,CAAC,EAAE,EAAE,MAAM,KAAK,MAAG,EAAE,MAAM,MAAM;AAAA,QAChC;AAAA,MACF,GAAG,IAAI,EAAE,OAAO;AAChB,YAAM,IAAI,CAAC;AACX,UAAI;AAAA,QACF,EAAE;AAAA,QACF;AAAA,UACE,QAAQ,CAACA,OAAMA;AAAA,UACf,aAAa,MAAM;AAAA,UACnB,QAAQ,MAAM;AAAA,QAChB;AAAA,QACA;AAAA,UACE,QAAQ,CAACA,IAAG,GAAG,GAAG,MAAM;AACtB,eAAGA,IAAG,GAAG,GAAG,EAAE,WAAW,GAAG,QAAQ,GAAG,QAAQ,EAAE,GAAG,CAAC;AAAA,UACvD;AAAA,UACA,QAAQ,CAACA,OAAM;AACb,mBAAO,OAAO,GAAGA,EAAC;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI,GAAG,MAAM,CAAC;AACpB,WAAK;AAAA,QACH,GAAG,CAAC;AAAA,MACN,GAAG,IAAI,IAAI,EAAE,GAAG,EAAE,IAAI;AAAA,IACxB;AACA,WAAO,KAAK,EAAE,aAAa,KAAK,QAAQ,EAAE,YAAY,EAAE,SAAS,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,UAAU,EAAE,OAAO,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG;AAAA,MACnI,GAAG,EAAE;AAAA,MACL,MAAM;AAAA,MACN,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,WAAW,MAAM;AAAA,MACjB,QAAQ,EAAE;AAAA,IACZ,GAAG,CAAC,GAAG,KAAK;AAAA,EACd;AACA,WAAS,EAAE,GAAG,GAAG;AACf,QAAI,GAAG,GAAG;AACV,QAAI,CAAC;AACH;AACF,QAAI,KAAK;AACT,UAAM,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;AACnE,QAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,YAAY,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,cAAc,OAAO,IAAI,EAAE,iBAAiB,QAAQ,EAAE,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,IAAI,EAAE,iBAAiB,QAAQ,EAAE,4BAA4B,CAAC,GAAG,EAAE,OAAO,SAAS,GAAG,GAAG,GAAG,EAAE,OAAO,MAAM,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,SAAS,GAAG,EAAE,OAAO,WAAW,CAAC,EAAE,OAAO,QAAQ,SAAS,CAAC,KAAK,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,EACvb;AACA,WAAS,EAAE,GAAG,GAAG;AACf,QAAI,GAAG,GAAG,GAAGA;AACb,QAAI,CAAC;AACH;AACF,SAAK,QAAQ,EAAE,YAAY,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,cAAc,OAAO,IAAI,EAAE,iBAAiB,QAAQ,EAAE,iBAAiB,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,YAAY;AAClK,UAAM,KAAK,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE;AAC9C,OAAG,CAAC,MAAM,MAAM,OAAO,IAAI,QAAK,IAAI;AACpC,UAAM,KAAKA,KAAI,EAAE,WAAW,OAAO,SAASA,GAAE,WAAW,IAAI,MAAM,YAAY,CAAC,IAAI,CAAC,CAAC;AACtF,QAAI,EAAE,UAAU,aAAa,EAAE,UAAU,CAAC,GAAG,EAAE,OAAO,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AACnL,UAAI,EAAE,CAAC;AACL,UAAE,CAAC;AAAA,eACI,EAAE,EAAE,OAAO;AAClB,YAAI;AACF,YAAE,QAAQ;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,IACJ;AACA,gBAAY,KAAK,OAAO,EAAE;AAAA,EAC5B;AACA,WAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,QAAI,GAAG;AACP,QAAI,CAAC;AACH;AACF,QAAI,IAAI,GAAG,IAAI;AACf,QAAI,EAAE,WAAW,EAAE,OAAO,cAAc,CAAC,IAAI,IAAI,MAAM,UAAU;AAC/D,YAAM,MAAM,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,WAAW,EAAE;AAC5D,QAAE,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC;AACzC;AAAA,IACF;AACA,QAAI,MAAM,WAAW;AACnB,QAAE,WAAW,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,UAAU;AAClD;AAAA,IACF;AACA,QAAI,EAAE,CAAC,KAAK,MAAM,yBAAyB;AACzC,WAAK,MAAM,KAAK,EAAE,CAAC,IAAI,IAAI,OAAO,EAAE,CAAC;AACrC;AAAA,IACF;AACA,OAAG,SAAS,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,cAAc;AACtD,QAAI,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,CAAC;AAC3C,QAAI,MAAM,QAAQ;AAChB,YAAM,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,MAAM,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,SAAS,EAAE;AAC3F,WAAK,EAAE,UAAU,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,OAAO;AAAA,QACxC;AAAA,QACA,IAAI,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC;AAAA,MACtB;AACA;AAAA,IACF;AACA,QAAI,EAAE,SAAS,kBAAkB;AAC/B,UAAI,MAAM;AACR;AACF,QAAE;AAAA,QACA,GAAG,CAAC;AAAA,QACJ,IAAI,gBAAG,GAAG,CAAC;AAAA,MACb;AACA;AAAA,IACF;AACA,QAAI,EAAE,SAAS,GAAG,KAAK,MAAM,QAAQ;AACnC,UAAI;AACJ,iBAAW,KAAK,EAAE,MAAM,GAAG;AACzB,YAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,CAAC;AAAA,IACtD;AACA,QAAIA,KAAI;AACR,QAAIA,OAAM,OAAOA,KAAI,OAAK,EAAE,CAAC,GAAG;AAC9B,SAAG,SAAS,CAAC,MAAM,GAAGA,EAAC,IAAI,EAAE,CAAC,EAAE,GAAGA,EAAC,IAAI,EAAE,CAAC,EAAEA,EAAC,IAAI,EAAE,WAAW,IAAI,KAAK,EAAEA,EAAC,MAAM,EAAE,CAAC,IAAIA;AACxF;AAAA,IACF;AACA,OAAG,CAAC,KAAK,GAAGA,EAAC,IAAI,EAAE,OAAOA,GAAE,OAAO,GAAG,CAAC,KAAK,GAAGA,EAAC,IAAI,EAAE,IAAIA,EAAC,IAAI,GAAG,CAAC,KAAK,GAAGA,EAAC,KAAK,EAAE,gBAAgBA,GAAE,cAAc,EAAE,KAAKA,EAAC,IAAI,GAAG,CAAC,KAAK,MAAM,QAAQA,EAAC,IAAI,eAAe,KAAK,OAAO,EAAE,aAAa,aAAa,EAAE,UAAUA,EAAC,IAAI,EAAE,IAAI,GAAGA,EAAC,IAAI,GAAG,CAAC,KAAK,OAAOA,MAAK,WAAW,eAAe,KAAK,OAAO,EAAE,aAAa,aAAa,EAAE,UAAUA,EAAC,IAAI,EAAE,IAAIA,EAAC,IAAI,EAAE,CAAC,IAAIA,IAAG,GAAG,CAAC;AAAA,EACpX;AACA,WAAS,EAAE,GAAG;AACZ,QAAI;AACJ,aAAS,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,OAAO,SAAS,EAAE,WAAW;AAAA,EAC9E;AACA,WAAS,EAAE,GAAG;AACZ,UAAM,IAAI,EAAE,IAAI,SAAG,GAAG,EAAE,MAAM,UAAU,GAAG,CAAC;AAC5C,WAAO,EAAE,OAAO,GAAG;AAAA,EACrB;AACA,WAAS,EAAE,GAAG;AACZ,QAAI;AACJ,UAAM,IAAI,EAAE,CAAC,GAAG,MAAM,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,OAAO,SAAS,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,QAAQ,CAAC;AAC7G,WAAO,IAAI,KAAK,KAAK,EAAE,SAAS,IAAI,OAAO,EAAE,IAAI,CAAC;AAAA,EACpD;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY,MAAM;AAAA,IAClB,eAAe;AAAA,IACf,SAAS,MAAM;AAAA,IACf,gBAAgB,MAAM;AAAA,IACtB,aAAa;AAAA,IACb,eAAe,MAAM;AAAA,IACrB,YAAY,MAAM;AAAA,IAClB,WAAW,MAAM;AAAA,IACjB,qBAAqB,MAAM;AAAA,EAC7B;AACF;AACA,SAAS,KAAK;AACZ,SAAO,GAAG,EAAE;AACd;AACA,SAAS,KAAK;AACZ,SAAO,OAAO,YAAY,OAAO,OAAO,SAAS,MAAM,SAAS,OAAO,aAAa,MAAM,aAAa,CAAC;AAC1G;AACA,IAAM,KAAK,OAAO,SAAS;AAA3B,IAAuC,KAAK;AAA5C,IAAqE,KAAK;AAC1E,IAAI;AAAJ,IAAO;AACP,SAAS,KAAK;AACZ,MAAI;AACJ,SAAO,MAAM,WAAW,OAAO,SAAS,OAAO,OAAO,eAAe,IAAI,MAAI,KAAK,OAAO,eAAe,OAAO,aAAa,QAAQ,GAAG,IAAI,WAAW,gBAAgB,QAAQ,MAAM,WAAW,EAAE,gBAAgB,IAAI,MAAI,KAAK,WAAW,WAAW,eAAe,IAAI,QAAK;AAC9Q;AACA,SAAS,KAAK;AACZ,SAAO,GAAG,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AACpC;AACA,IAAM,KAAN,MAAS;AAAA,EACP,YAAY,GAAG,GAAG;AAChB,SAAK,SAAS,MAAM,KAAK,cAAc,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,KAAK,SAAS,GAAG,KAAK,OAAO;AAC3F,UAAM,IAAI,CAAC;AACX,QAAI,EAAE;AACJ,iBAAW,KAAK,EAAE,UAAU;AAC1B,cAAM,IAAI,EAAE,SAAS,CAAC;AACtB,UAAE,CAAC,IAAI,EAAE;AAAA,MACX;AACF,UAAM,IAAI,mCAAmC,EAAE,EAAE;AACjD,QAAI,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC;AAC3B,QAAI;AACF,YAAM,IAAI,aAAa,QAAQ,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC;AACnD,aAAO,OAAO,GAAG,CAAC;AAAA,IACpB,QAAQ;AAAA,IACR;AACA,SAAK,YAAY;AAAA,MACf,cAAc;AACZ,eAAO;AAAA,MACT;AAAA,MACA,YAAY,GAAG;AACb,YAAI;AACF,uBAAa,QAAQ,GAAG,KAAK,UAAU,CAAC,CAAC;AAAA,QAC3C,QAAQ;AAAA,QACR;AACA,YAAI;AAAA,MACN;AAAA,MACA,MAAM;AACJ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,MAAM;AACzB,YAAM,KAAK,OAAO,MAAM,KAAK,UAAU,YAAY,CAAC;AAAA,IACtD,CAAC,GAAG,KAAK,YAAY,IAAI,MAAM,CAAC,GAAG;AAAA,MACjC,KAAK,CAAC,GAAG,MAAM,KAAK,SAAS,KAAK,OAAO,GAAG,CAAC,IAAI,IAAI,MAAM;AACzD,aAAK,QAAQ,KAAK;AAAA,UAChB,QAAQ;AAAA,UACR,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF,CAAC,GAAG,KAAK,gBAAgB,IAAI,MAAM,CAAC,GAAG;AAAA,MACrC,KAAK,CAAC,GAAG,MAAM,KAAK,SAAS,KAAK,OAAO,CAAC,IAAI,MAAM,OAAO,KAAK,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,OAAO,KAAK,YAAY,KAAK;AAAA,QACrJ,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS,MAAM;AAAA,QACf;AAAA,MACF,CAAC,GAAG,KAAK,UAAU,CAAC,EAAE,GAAG,CAAC,KAAK,IAAI,MAAM,IAAI,QAAQ,CAAC,MAAM;AAC1D,aAAK,YAAY,KAAK;AAAA,UACpB,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,MAAM,cAAc,GAAG;AACrB,SAAK,SAAS;AACd,eAAW,KAAK,KAAK;AACnB,WAAK,OAAO,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;AACpC,eAAW,KAAK,KAAK;AACnB,QAAE,QAAQ,MAAM,KAAK,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC;AAAA,EACpD;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,MAAM,EAAE;AAC7C,MAAI,MAAM,EAAE,yCAAyC,CAAC;AACpD,MAAE,KAAK,IAAI,GAAG,CAAC;AAAA,OACZ;AACH,UAAM,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI;AAC7B,KAAC,EAAE,2BAA2B,EAAE,4BAA4B,CAAC,GAAG,KAAK;AAAA,MACnE,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG,KAAK,EAAE,EAAE,aAAa;AAAA,EAC5B;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,IAAI,QAAQ,CAAC;AACnB,SAAO,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,QAAQ,IAAI,CAAC;AACpD;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,QAAM,IAAI,MAAM,IAAI,CAAC;AACvB;AACA,IAAM,KAAK,CAAC,MAAM;AAChB,QAAM,IAAI;AAAA,IACR,IAAI,EAAE;AAAA,IACN,OAAO,EAAE;AAAA,IACT,UAAU,CAAC;AAAA,IACX,MAAM,CAAC;AAAA,EACT;AACA,IAAE,SAAS,MAAM,EAAE,KAAK,KAAK;AAAA,IAC3B,OAAO,EAAE;AAAA,IACT,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB,CAAC;AACD,QAAM,IAAI,GAAG,CAAC;AACd,SAAO,IAAI,KAAK,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO,GAAG,GAAG,CAAC,CAAC;AAAA,IACf,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX,CAAC,GAAG,EAAE,KAAK,SAAS,OAAO,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAAA,IACpD,OAAO,GAAG,EAAE,SAAS;AAAA,IACrB,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX,CAAC,GAAG,EAAE,KAAK,KAAK;AAAA,IACd,OAAO,IAAI,IAAI,MAAE,EAAE,KAAK,EAAE,aAAa,CAAC;AAAA,IACxC,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX,CAAC,IAAI,EAAE,KAAK,SAAS,QAAQ,MAAM,EAAE,KAAK,KAAK;AAAA,IAC7C,OAAO,GAAG,EAAE,GAAG;AAAA,IACf,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX,CAAC,GAAG,EAAE,KAAK,KAAK;AAAA,IACd,OAAO,MAAM,KAAK,MAAM,EAAE,SAAS,CAAC,CAAC,OAAO,KAAK,MAAM,EAAE,SAAS,CAAC,CAAC,OAAO,KAAK,MAAM,EAAE,SAAS,CAAC,CAAC;AAAA,IACnG,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACX,CAAC,IAAI;AACP;AACA,SAAS,GAAG,GAAG,GAAG,IAAI,IAAI;AACxB,IAAE,SAAS,QAAQ,CAAC,MAAM;AACxB,QAAI,EAAE,SAAS,oBAAoB,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC;AAC/E;AACF,UAAM,IAAI,GAAG,CAAC;AACd,MAAE,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAChC,CAAC;AACH;AACA,IAAM,KAAK,CAAC;AAAZ,IAAe,IAAI;AAAnB,IAAqC,KAAK,SAAG;AAAA,EAC3C,YAAY;AACd,CAAC;AACD,SAAS,GAAG,GAAG,GAAG;AAChB;AAAA,IACE;AAAA,MACE,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,IACA,CAAC,MAAM;AACL,aAAO,EAAE,OAAO,cAAc;AAAA,QAC5B;AAAA,MACF,GAAG,EAAE,aAAa;AAAA,QAChB,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,MACzB,CAAC,GAAG,YAAY,MAAM;AACpB,UAAE,kBAAkB,CAAC;AAAA,MACvB,GAAG,GAAG,GAAG,YAAY,MAAM;AACzB,UAAE,sBAAsB;AAAA,MAC1B,GAAG,GAAG,GAAG,EAAE,GAAG,iBAAiB,CAAC,MAAM;AACpC,YAAI,EAAE,gBAAgB,GAAG;AACvB,gBAAM,IAAI,GAAG,EAAE,MAAM,KAAK;AAC1B,aAAG,EAAE,MAAM,OAAO,GAAG,EAAE,MAAM,GAAG,GAAG,aAAa,GAAG,EAAE,YAAY,CAAC,CAAC;AAAA,QACrE;AAAA,MACF,CAAC;AACD,UAAI,IAAI,MAAM,IAAI;AAClB,QAAE,GAAG,kBAAkB,CAAC,MAAM;AAC5B,YAAI;AACJ,YAAI,EAAE,gBAAgB,GAAG;AACvB,gBAAM,CAAC,CAAC,IAAI,EAAE,MAAM,MAAM,qBAAqB,QAAQ,EAAE,MAAM;AAC/D,cAAI,CAAC;AACH;AACF,cAAI,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,EAAE,QAAQ;AAC/C,kBAAM,IAAI,GAAG,CAAC;AACd,cAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;AAAA,UACvB;AACA,YAAE,QAAQ;AAAA,YACR,QAAQ,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,MAAM,aAAa,EAAE,KAAK,GAAG,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,gBAAgB,EAAE,IAAI,EAAE,KAAK,GAAG,OAAO,GAAG,UAAU,KAAG,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,MAAM,MAAM,QAAQ;AAAA,UAC1M,GAAG,EAAE,YAAY,EAAE,QAAQ;AAAA,YACzB,GAAG,EAAE;AAAA,YACL,OAAO;AAAA,cACL;AAAA,gBACE,KAAK;AAAA,gBACL,OAAO;AAAA,kBACL,SAAS,EAAE,SAAS;AAAA,kBACpB,QAAQ,GAAG,CAAC;AAAA,kBACZ,OAAO,EAAE,SAAS,MAAM,KAAK,OAAO;AAAA,kBACpC,WAAW,EAAE,SAAS,MAAM,KAAK,OAAO;AAAA,kBACxC,QAAQ,EAAE,SAAS,MAAM,KAAK,OAAO;AAAA,kBACrC,OAAO,EAAE,SAAS,MAAM,KAAK,OAAO;AAAA,gBACtC;AAAA,cACF;AAAA,cACA;AAAA,gBACE,KAAK;AAAA,gBACL,SAAS,IAAI,EAAE,SAAS,MAAM,KAAK,aAAa,OAAO,SAAS,EAAE,IAAI,CAAC,OAAO;AAAA,kBAC5E,GAAG;AAAA,kBACH,aAAa,EAAE;AAAA,gBACjB,EAAE,MAAM,CAAC;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC,GAAG,EAAE,GAAG,mBAAmB,CAAC,MAAM;AACjC,UAAE,gBAAgB,KAAK,GAAG,EAAE,MAAM,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,KAAK;AAAA,MAC1E,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,IAAM,KAAK,CAAC,cAAc,WAAW;AAArC,IAAwC,KAAqB,gBAAG;AAAA,EAC9D,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,SAAS,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IAC1C,YAAY,CAAC;AAAA,IACb,aAAa,CAAC;AAAA,IACd,eAAe,CAAC;AAAA,IAChB,iBAAiB,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IAClD,kBAAkB,CAAC;AAAA,IACnB,qBAAqB,CAAC;AAAA,IACtB,YAAY,EAAE,SAAS,SAAS;AAAA,IAChC,KAAK,CAAC;AAAA,IACN,QAAQ,CAAC;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,YAAY,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IAC7C,qBAAqB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IAClD,SAAS,CAAC;AAAA,IACV,OAAO,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IACxC,oBAAoB,EAAE,MAAM,QAAQ;AAAA,IACpC,WAAW,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IAC5C,SAAS,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IAC1C,uBAAuB,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IACxD,iBAAiB,CAAC;AAAA,IAClB,OAAO,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IACxC,8BAA8B,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IAC/D,WAAW,CAAC;AAAA,IACZ,wBAAwB,EAAE,MAAM,SAAS,SAAS,OAAO;AAAA,IACzD,oBAAoB,EAAE,MAAM,QAAQ;AAAA,EACtC;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,IAAI,GAAG,IAAI,GAAG,IAAI,SAAG,GAAG,IAAI,IAAE,GAAG,IAAI,WAAE,IAAI,MAAG,CAAC,GAAG,IAAI,mBAAG;AAC/D,OAAG,oBAAE;AACL,UAAM,IAAI,CAAC,GAAG,IAAI,UAAO,gBAAG;AAAA,MAC1B,QAAQ;AACN,YAAI;AACJ,cAAM,KAAK,IAAI,mBAAG,MAAM,OAAO,SAAS,EAAE;AAC1C,cAAM,EAAE,MAAM,KAAK,OAAO,SAAS,EAAE,WAAW;AAChD,cAAMA,KAAI,CAAC;AACX,iBAAS,EAAE,GAAG;AACZ,gBAAM,EAAE,UAAU,EAAE,EAAE,MAAM,GAAG,EAAE,YAAY,OAAO,OAAOA,IAAG,EAAE,QAAQ;AAAA,QAC1E;AACA,eAAO,KAAK,QAAQ,EAAE,UAAU,EAAE,wBAAwB,EAAE,EAAE,MAAM,GAAG,QAAQ,QAAQA,EAAC,EAAE,QAAQ,CAAC,MAAM;AACvG,kBAAG,GAAGA,GAAE,CAAC,CAAC;AAAA,QACZ,CAAC,IAAI,QAAG,WAAW,CAAC,GAAG,QAAG,UAAU,EAAE,GAAG,OAAO,SAAS,OAAO,GAAG,KAAK,OAAO,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,EAAG,UAAI,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AAAA,MAC5I;AAAA,IACF,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,UAAO;AACrB,YAAM,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,QAAQA,GAAE,IAAI,eAAG,GAAG,CAAC,CAAC;AAC3C,MAAAA,GAAE,EAAG,CAAC,GAAG,EAAE,KAAK;AAAA,IAClB,GAAG,IAAI,CAAC,GAAG,IAAI,UAAO;AACpB,SAAG,EAAE,MAAM,KAAK,GAAG,MAAM,EAAE,SAAS,MAAM,QAAQ,GAAG,EAAE,SAAS,MAAM,YAAY,QAAQ,GAAG,EAAE,SAAS,MAAM,iBAAiB,IAAI,EAAE,MAAM,SAAS;AAAA,QAClJ,MAAM;AAAA,MACR;AAAA,IACF,GAAG,IAAI,WAAE,IAAI;AACb,MAAE,EAAE,SAAS,GAAG,SAAS,MAAM,EAAE,EAAE,OAAO,IAAE,EAAE,CAAC;AAC/C,UAAM,IAAI,MAAM;AACd,QAAE,EAAE,KAAK,GAAG,EAAE,EAAE,OAAO,IAAE;AAAA,IAC3B;AACA,WAAO,UAAG,MAAM;AACd,YAAM,IAAI;AACV,QAAE,QAAQ,GAAG;AAAA,QACX,OAAO,EAAE;AAAA,QACT,QAAQ;AAAA,QACR,YAAY,EAAE,cAAc;AAAA,QAC5B,iBAAiB;AAAA,QACjB,MAAM;AAAA,MACR,CAAC;AACD,YAAM,EAAE,gBAAgB,GAAG,QAAQ,GAAG,SAASA,IAAG,kBAAkB,EAAE,IAAI,EAAE;AAC5E,QAAE,EAAE,KAAK;AACT,YAAM,IAAI,MAAM;AACd,cAAM,IAAI,IAAI;AAAA,UACZ;AAAA,UACA,OAAO,aAAa,OAAO;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AACA,UAAE,SAAS,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;AAC/C,cAAM,IAAI,YAAG,MAAM;AACjB,UAAAA,GAAE,MAAM,UAAU,MAAM,EAAE,iBAAiB,GAAG,EAAE,CAAC,GAAG,KAAK,QAAQ,EAAE;AAAA,QACrE,CAAC;AAAA,MACH;AACA;AAAA,QACE,MAAM,EAAE;AAAA,QACR,CAAC,GAAG,MAAM;AACR,eAAK,EAAE,CAAC,GAAG,MAAM,EAAE,iBAAiB,GAAG,EAAE,CAAC;AAAA,QAC5C;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF,GAAG,EAAE,SAAS,EAAE;AAAA,IAClB,CAAC,GAAG,YAAE,CAAC,GAAG,CAAC,GAAG,OAAO,UAAG,GAAG,mBAAG,UAAU;AAAA,MACtC,SAAS;AAAA,MACT,KAAK;AAAA,MACL,cAAc,EAAE,MAAM;AAAA,MACtB,OAAO,eAAG,EAAE,OAAO,KAAK;AAAA,MACxB,aAAa,UAAU,MAAE,EAAE,EAAE,OAAO;AAAA,MACpC,OAAO,eAAG;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU,EAAE,aAAa,UAAU;AAAA,QACnC,KAAK;AAAA,QACL,MAAM;AAAA,QACN,eAAe;AAAA,QACf,aAAa;AAAA,QACb,GAAG,EAAE,OAAO;AAAA,MACd,CAAC;AAAA,IACH,GAAG,MAAM,IAAI,EAAE;AAAA,EACjB;AACF,CAAC;AA5HD,IA4HI,KAAK;AAAA,EACP;AAAA,EACA;AAAA,EACA;AACF;AAhIA,IAgIG,KAAK;AAAA,EACN,UAAU;AAAA,IACR,iBAAiB;AAAA,MACf,iBAAiB,CAAC,MAAM,EAAE,WAAW,MAAM,KAAK,CAAC,GAAG,SAAS,CAAC,KAAK,MAAM;AAAA,IAC3E;AAAA,EACF;AACF;AAtIA,IAsIG,EAAE,YAAY,GAAG,IAAI,EAAE;AAC1B,IAAI,IAAI;AACR,IAAM,KAAK;AAAA,EACT,SAAS,CAAC,GAAG,MAAM;AACjB,QAAI;AACJ,UAAM,IAAI,GAAG,CAAC;AACd,QAAI,CAAC,GAAG;AACN,SAAG,8CAA8C,EAAE,KAAK,EAAE;AAC1D;AAAA,IACF;AACA,UAAM,EAAE,QAAQ,GAAG,EAAE,OAAO,OAAO,CAAC;AACpC,UAAM,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ;AAClC,MAAE,UAAU,GAAG,IAAI,IAAI,YAAG,GAAG,EAAE,UAAU,EAAE,SAAS,WAAW,CAAC,GAAG,QAAQ,GAAG,EAAE,OAAO,IAAI,CAAC,GAAG,QAAQ;AAAA,MACrG;AAAA,QACE,CAAC,aAAa,EAAE,SAAS,WAAW,CAAC,CAAC;AAAA,QACtC,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,IAAI,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,IAAI,EAAE,aAAa,OAAO,SAAS,EAAE,CAAC,EAAE;AAAA,QACpH,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,IAAI,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,OAAO,KAAK,OAAO,SAAS,EAAE,CAAC,EAAE;AAAA,MACtF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,CAAC,MAAM;AAChB,SAAK,QAAQ,EAAE,QAAQ,GAAG,EAAE,UAAU,EAAE,OAAO,OAAO,CAAC;AAAA,EACzD;AACF;AACA,IAAM,KAAN,cAAiB,KAAG;AAAA,EAClB,YAAY,GAAG,GAAG;AAChB,UAAM,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,IAAI,eAAG;AACxE,MAAE,aAAa,YAAY,IAAI,uBAAG,GAAG,CAAC,CAAC,GAAG,EAAE,sBAAsB;AAClE,UAAM,IAAI,IAAI,kBAAG,EAAE,KAAK,MAAG,CAAC;AAC5B,UAAM,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,OAAO;AACzD,UAAM,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,eAAG;AACnF,MAAE,aAAa,YAAY,IAAI,uBAAG,GAAG,CAAC,CAAC,GAAG,EAAE,sBAAsB,GAAG,KAAK,IAAI,IAAI,KAAG,GAAG,IAAI,kBAAG,EAAE,MAAM,UAAI,KAAK,MAAG,CAAC,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQ,CAAC,GAAG,KAAK,UAAU;AACrF,WAAK,SAAS,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,CAAC,EAAE,SAAS,MAAM,IAAI,KAAK,KAAK;AAAA,SAChF;AACH,WAAK,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,eAAe,KAAK,MAAM,SAAS;AAC9E,YAAM,IAAI,KAAK,SAAS,OAAO,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzD,UAAI,KAAK,EAAE,eAAe,IAAI,CAAC,GAAG,KAAK,SAAS,CAAC,EAAE,SAAS,MAAM,KAAK,KAAK,SAAS,KAAK;AAAA,IAC5F;AACA,SAAK,YAAY,gBAAgB,KAAK,MAAM,WAAW,EAAE,MAAM,KAAK,KAAK,EAAE,aAAa,KAAK,MAAM,WAAW,GAAG,KAAK,SAAS,CAAC,EAAE,YAAY,KAAK,KAAK,WAAW;AAAA,EACrK;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK,SAAS,CAAC,EAAE,SAAS,QAAQ,GAAG,KAAK,SAAS,CAAC,EAAE,SAAS,QAAQ;AAAA,EAC3H;AACF;AACA,IAAM,EAAE,YAAY,GAAG,IAAI,EAAE;AAC7B,IAAI;AAAJ,IAAQ;AACR,IAAM,KAAK;AAAA,EACT,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,eAAe;AACjB;AANA,IAMG,KAAK;AAAA,EACN,SAAS,CAAC,MAAM;AACd,QAAI,CAAC,EAAE,SAAS;AACd,SAAG,GAAG,EAAE,IAAI,iBAAiB;AAC7B;AAAA,IACF;AACA,SAAK,GAAG,EAAE,IAAI,GAAG,EAAE,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,MAAM,OAAO,CAAC,CAAC;AAAA,EAC9D;AAAA,EACA,SAAS,CAAC,MAAM;AACd,QAAI,EAAE,OAAO,SAAS,KAAK,CAAC,MAAM,aAAa,EAAE,GAAG,EAAE,aAAa,OAAO,EAAE,OAAO;AAAA,EACrF;AAAA,EACA,WAAW,CAAC,MAAM;AAChB,QAAI,CAAC,EAAE,SAAS;AACd,SAAG,GAAG,EAAE,IAAI,iBAAiB;AAC7B;AAAA,IACF;AACA,QAAI,EAAE,OAAO,SAAS,KAAK,CAAC,MAAM,aAAa,EAAE,GAAG,KAAK,EAAE,WAAW,EAAE,QAAQ,GAAG,EAAE,UAAU,EAAE,OAAO,OAAO,CAAC;AAAA,EAClH;AACF;AAxBA,IAwBG,KAAK;AAAA,EACN,SAAS,CAAC,GAAG,MAAM;AACjB,QAAI,EAAE,KAAK;AACT,cAAQ,IAAI,SAAS,EAAE,GAAG,IAAI,EAAE,EAAE,GAAG,CAAC;AACtC;AAAA,IACF;AACA,YAAQ,IAAI,SAAS,CAAC;AAAA,EACxB;AACF;AAhCA,IAgCG,KAAK;AAAA,EACN,QAAQ,GAAG;AACT,MAAE,UAAU,cAAc,EAAE;AAAA,EAC9B;AACF;", "names": ["fn", "on", "fn", "fn", "keys", "fn", "toRefs", "toValue", "fn", "fn", "window", "fn", "window", "timestamp", "window", "window", "window", "window", "toRefs", "window", "h", "toValue"]}