from src.job.manager import manager
from src.http.api import httpapi

def __job_name_validation(job_name):
    if job_name is None:
        return False, {"message": "job name is required"}
    if not isinstance(job_name, str):
        return False, {"message": "job name must be a string"}
    if len(job_name) == 0:
        return False, {"message": "job name cannot be empty"}
    if len(job_name) > 64:
        return False, {"message": "job name is too long, must be less than 64 characters"}
    return True, None

def getJobList(ctx: httpapi, search, post_data):
    _manager = ctx.service.get_service(manager)
    _jobs = _manager.get_job_list()
    return 0, _jobs

def createNewJob(ctx: httpapi, search, post_data):
    _job_name = search.get("name", None)
    _validation = __job_name_validation(_job_name)
    if _validation[0] is False:
        return 1, _validation[1]
    
    _manager: manager = ctx.service.get_service(manager)
    return 0, _manager.create_new_job(_job_name) or False

def openJob(ctx: httpapi, search, post_data):
    _job_name = search.get("name", None)
    _validation = __job_name_validation(_job_name)
    if _validation[0] is False:
        return 1, _validation[1]

    _manager = ctx.service.get_service(manager)
    return 0, _manager.open_job(_job_name)

def closeJob(ctx: httpapi, search, post_data):
    _manager = ctx.service.get_service(manager)
    return 0, _manager.close_job() or False

def saveJob(ctx: httpapi, search, post_data):
    _manager = ctx.service.get_service(manager)
    return 0, _manager.save_job() or False

def getCurrentJob(ctx: httpapi, search, post_data):
    _manager = ctx.service.get_service(manager)
    _current_job = _manager.get_job_runner().get_job_name()
    return 0, _current_job

def setJobPrefrences(ctx: httpapi, search, post_data):
    if(search.get("category") is None):
        return 1, {}
    
    _manager = ctx.service.get_service(manager)
    _runner = _manager.get_job_runner()
    if _runner is None:
        raise ValueError("no job is currently loaded")

    if(search["category"] == "filmArgs"):
        _runner.set_job_repeat(post_data.get("repeat"))
        _runner.set_repeat(post_data.get("filmCount"))
        _runner.set_film_post_delay(post_data.get("beforeFilmDelay"))
        _runner.set_film_after_delay(post_data.get("afterFilmDelay"))
        _runner.set_film_duration(post_data.get("filmDuration"))

    if(search["category"] == "plannerArgs"):
        _runner.set_scan_mode(post_data.get("scanMode"))
        _runner.set_first_axis(post_data.get("preferredAxis"))
        _runner.set_planner_path(post_data.get("xPlannerPath"),post_data.get("yPlannerPath"),post_data.get("zPlannerPath"),)
        _runner.set_scan_step(post_data.get("scanXStep"),post_data.get("scanYStep"),post_data.get("scanZStep"))

    _runner.save_config()
    return 0, True

def getJobPrefrences(ctx: httpapi, search, post_data):
    if(search.get("category") is None):
        return 1, {}

    _manager = ctx.service.get_service(manager)
    _runner = _manager.get_job_runner()
    if _runner is None:
        raise ValueError("no job is currently loaded")
    
    if(search["category"] == "plannerArgs"):
        return 0, {
            "scanMode": _runner.get_scan_mode(),
            "preferredAxis": _runner.get_first_axis(),
            "xPlannerPath": _runner.get_planner_path("x"),
            "yPlannerPath": _runner.get_planner_path("y"),
            "zPlannerPath": _runner.get_planner_path("z"),
            "scanXStep": _runner.get_scan_step("x"),
            "scanYStep": _runner.get_scan_step("y"),
            "scanZStep": _runner.get_scan_step("z"),
        }
    if(search["category"] == "filmArgs"):
        return 0, {
            "repeat": _runner.get_job_repeat(),
            "filmCount": _runner.get_repeat(),
            "beforeFilmDelay": _runner.get_film_post_delay(),
            "afterFilmDelay": _runner.get_film_after_delay(),
            "filmDuration": _runner.get_film_duration()
        }

def getJobStatus(ctx: httpapi, search, post_data):
    _manager = ctx.service.get_service(manager)
    _runner = _manager.get_job_runner()
    if _runner is None:
        raise ValueError("no job is currently loaded")
    
    return 0, {
        "status": _runner.get_job_status(),
        "progress": _runner.get_job_progress(),
        "message": _runner.get_job_message(),
    }

def jobControl(ctx: httpapi, search, post_data):
    mgrsrv = ctx.service.get_service(manager)
    runner = mgrsrv.get_job_runner()
    if runner is None:
        raise ValueError("no job is currently loaded")

    match search.get("action"):
        case "start":
            runner.start()
        case "stop":
            runner.stop()
        case "pause":
            runner.pause()
        case "resume":
            runner.start(from_resume=True)
        case _:
            raise ValueError("invalid action specified")
    return 0, True

def jobMachineControl(ctx: httpapi, search, post_data):
    mgrsrv = ctx.service.get_service(manager)
    runner = mgrsrv.get_job_runner()
    if runner is None:
        raise ValueError("no job is currently loaded")

    match search.get("action"):
        case "homming":
            runner.home()
            pass
        case "go_scan_start":
            runner.go_scan_start()
            pass
        case "go_scan_end":
            runner.go_scan_end()
            pass
        case "set_scan_start":
            runner.set_scan_start()
            pass
        case "set_scan_end":
            runner.set_scan_end()
            pass
        case "swap_scan":
            runner.swap_working_area()
            pass
        case _:
            raise ValueError("invalid action specified")
    return 0, True
