var t=(n,o,i)=>new Promise((c,a)=>{var u=s=>{try{g(i.next(s))}catch(r){a(r)}},l=s=>{try{g(i.throw(s))}catch(r){a(r)}},g=s=>s.done?c(s.value):Promise.resolve(s.value).then(u,l);g((i=i.apply(n,o)).next())});import{r as e}from"./bootstrap-MyT3sENS.js";function p(){return t(this,null,function*(){return e.get("/settings/general/getLocalSerialDevices")})}function S(){return t(this,null,function*(){return e.get("/settings/general/getSerialConfig")})}function d(n){return t(this,null,function*(){return e.post("/settings/general/setSerialConfig",n)})}function I(){return t(this,null,function*(){return e.get("/settings/general/getJoystickKeyBindings")})}function C(n){return t(this,null,function*(){return e.post("/settings/general/setJoystickKeyBindings",n)})}function B(){return t(this,null,function*(){return e.get("/settings/general/getJoystickKeyFunctionList")})}function G(){return t(this,null,function*(){return e.get("/settings/general/getInputDevices")})}function k(){return t(this,null,function*(){return e.get("/settings/general/getInitialGCode")})}function J(n){return t(this,null,function*(){return e.post("/settings/general/setInitialGCode",n)})}function K(){return t(this,null,function*(){return e.get("/settings/general/getStepperArgs")})}function v(n){return t(this,null,function*(){return e.post("/settings/general/setStepperArgs",n)})}function A(){return t(this,null,function*(){return e.get("/settings/general/getGPIOBinding")})}function D(n){return t(this,null,function*(){return e.post("/settings/general/setGPIOBinding",n)})}function L(){return t(this,null,function*(){return e.get("/settings/general/getNotificationSettings")})}function N(n){return t(this,null,function*(){return e.post("/settings/general/setNotificationSettings",n)})}export{D as a,A as b,G as c,C as d,B as e,I as f,L as g,p as h,d as i,v as j,k,K as l,S as m,J as n,N as s};
