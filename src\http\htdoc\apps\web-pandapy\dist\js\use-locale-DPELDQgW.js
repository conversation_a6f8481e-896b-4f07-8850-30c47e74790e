import{e as c,d,c as i}from"./bootstrap-MyT3sENS.js";import{i as u,c as r}from"../jse/index-index-Y3_OtjO-.js";function m(n){const{mergedLocaleRef:o,mergedDateLocaleRef:a}=u(i,null)||{},t=r(()=>{var e,l;return(l=(e=o==null?void 0:o.value)===null||e===void 0?void 0:e[n])!==null&&l!==void 0?l:c[n]});return{dateLocaleRef:r(()=>{var e;return(e=a==null?void 0:a.value)!==null&&e!==void 0?e:d}),localeRef:t}}export{m as u};
