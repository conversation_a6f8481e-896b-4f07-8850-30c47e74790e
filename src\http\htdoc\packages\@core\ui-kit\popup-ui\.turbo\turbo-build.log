

> @vben-core/popup-ui@5.2.1 build /mnt/c/Users/<USER>/Downloads/pandapy/src/http/htdoc/packages/@core/ui-kit/popup-ui
> pnpm unbuild

[36mℹ[39m [36mBuilding popup-ui[39m                                                                                                                                                                                                                 [90m8:44:47 PM[39m
[36mℹ[39m Cleaning dist directory: [36m./dist[39m                                                                                                                                                                                                   [90m8:44:47 PM[39m
[mkdist] vue-sfc-transformer is not installed. mkdist will not transform typescript syntax in Vue SFCs.
[32m✔[39m [32mBuild succeeded for popup-ui[39m                                                                                                                                                                                                      [90m8:44:52 PM[39m
  [1mdist[22m (total size: [36m22.6 kB[39m)                                                                                                                                                                                                         [90m8:44:52 PM[39m
[90m  └─ dist/drawer/drawer.vue[1m (8.28 kB)[22m[39m
[90m  └─ dist/modal/modal.vue[1m (8.77 kB)[22m[39m
[90m  └─ dist/alert/alert.vue[1m (5.54 kB)[22m[39m
  [1mdist[22m (total size: [36m43.6 kB[39m)                                                                                                                                                                                                         [90m8:44:52 PM[39m
[90m  └─ dist/drawer/drawer.mjs[39m
[90m  └─ dist/alert/AlertBuilder.d.ts[1m (770 B)[22m[39m
[90m  └─ dist/index.mjs[1m (106 B)[22m[39m
[90m  └─ dist/drawer/__tests__/drawer-api.test.d.ts[1m (11 B)[22m[39m
[90m  └─ dist/index.d.ts[1m (76 B)[22m[39m
[90m  └─ dist/drawer/drawer-api.d.ts[1m (1.5 kB)[22m[39m
[90m  └─ dist/alert/index.mjs[1m (222 B)[22m[39m
[90m  └─ dist/alert/index.d.ts[1m (292 B)[22m[39m
[90m  └─ dist/modal/modal.mjs[39m
[90m  └─ dist/drawer/drawer-api.mjs[1m (3.43 kB)[22m[39m
[90m  └─ dist/modal/__tests__/modal-api.test.d.ts[1m (11 B)[22m[39m
[90m  └─ dist/drawer/drawer.d.ts[1m (3.54 kB)[22m[39m
[90m  └─ dist/alert/AlertBuilder.mjs[1m (4.64 kB)[22m[39m
[90m  └─ dist/alert/alert.d.ts[1m (2.67 kB)[22m[39m
[90m  └─ dist/alert/alert.mjs[1m (341 B)[22m[39m
[90m  └─ dist/drawer/use-drawer.mjs[1m (3.14 kB)[22m[39m
[90m  └─ dist/drawer/index.d.ts[1m (154 B)[22m[39m
[90m  └─ dist/modal/index.mjs[1m (122 B)[22m[39m
[90m  └─ dist/modal/index.d.ts[1m (148 B)[22m[39m
[90m  └─ dist/modal/modal-api.mjs[1m (3.55 kB)[22m[39m
[90m  └─ dist/modal/modal-api.d.ts[1m (1.5 kB)[22m[39m
[90m  └─ dist/modal/use-modal-draggable.d.ts[1m (479 B)[22m[39m
[90m  └─ dist/drawer/index.mjs[1m (127 B)[22m[39m
[90m  └─ dist/drawer/use-drawer.d.ts[1m (419 B)[22m[39m
[90m  └─ dist/modal/modal.d.ts[1m (3.66 kB)[22m[39m
[90m  └─ dist/modal/use-modal-draggable.mjs[1m (2.67 kB)[22m[39m
[90m  └─ dist/drawer/__tests__/drawer-api.test.mjs[1m (3.22 kB)[22m[39m
[90m  └─ dist/modal/use-modal.mjs[1m (3.18 kB)[22m[39m
[90m  └─ dist/modal/use-modal.d.ts[1m (405 B)[22m[39m
[90m  └─ dist/modal/__tests__/modal-api.test.mjs[1m (3.19 kB)[22m[39m
Σ Total dist size (byte size): [36m66.1 kB[39m
                                                                                                                                                                                                                                     [90m8:44:52 PM[39m
