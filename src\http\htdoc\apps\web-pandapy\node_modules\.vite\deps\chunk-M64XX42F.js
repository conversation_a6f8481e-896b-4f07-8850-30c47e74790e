import {
  dark_default as dark_default5,
  light_default as light_default5
} from "./chunk-Q6KFYHZU.js";
import {
  dark_default as dark_default2,
  dark_default2 as dark_default3,
  dark_default6 as dark_default4,
  light_default as light_default2,
  light_default2 as light_default3,
  light_default6 as light_default4
} from "./chunk-T4COAYJY.js";
import {
  dark_default,
  light_default
} from "./chunk-2AZKBT6I.js";
import {
  c,
  cB,
  cE,
  cM,
  changeColor,
  common_default,
  createTheme
} from "./chunk-HXOHBLE5.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/tree/styles/light.mjs
function self(vars) {
  const {
    borderRadiusSmall,
    dividerColor,
    hoverColor,
    pressedColor,
    primaryColor,
    textColor3,
    textColor2,
    textColorDisabled,
    fontSize
  } = vars;
  return {
    fontSize,
    lineHeight: "1.5",
    nodeHeight: "30px",
    nodeWrapperPadding: "3px 0",
    nodeBorderRadius: borderRadiusSmall,
    nodeColorHover: hoverColor,
    nodeColorPressed: pressedColor,
    nodeColorActive: changeColor(primaryColor, {
      alpha: 0.1
    }),
    arrowColor: textColor3,
    nodeTextColor: textColor2,
    nodeTextColorDisabled: textColorDisabled,
    loadingColor: primaryColor,
    dropMarkColor: primaryColor,
    lineColor: dividerColor
  };
}
var treeLight = createTheme({
  name: "Tree",
  common: light_default,
  peers: {
    Checkbox: light_default5,
    Scrollbar: light_default2,
    Empty: light_default3
  },
  self
});
var light_default6 = treeLight;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/tree/styles/dark.mjs
var treeDark = {
  name: "Tree",
  common: dark_default,
  peers: {
    Checkbox: dark_default5,
    Scrollbar: dark_default2,
    Empty: dark_default3
  },
  self(vars) {
    const {
      primaryColor
    } = vars;
    const commonSelf = self(vars);
    commonSelf.nodeColorActive = changeColor(primaryColor, {
      alpha: 0.15
    });
    return commonSelf;
  }
};
var dark_default6 = treeDark;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/tree/src/styles/rtl.cssr.mjs
var rtl_cssr_default = cB("tree", [cM("rtl", `
 direction: rtl;
 text-align: right;
 `, [cB("tree-node-switcher", `
 transform: rotate(180deg);
 `, [cM("expanded", `
 transform: rotate(90deg);
 `)]), cB("tree-node-checkbox", `
 margin-right: 0;
 margin-left: 4px;
 `), cB("tree-node-content", [cE("prefix", `
 margin-right: 0;
 margin-left: 8px;
 `)]), cB("tree-node-checkbox", [cM("right", `
 margin-right: 4px;
 `)])])]);

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/tree/styles/rtl.mjs
var treeRtl = {
  name: "Tree",
  style: rtl_cssr_default
};

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/tree-select/styles/dark.mjs
var treeSelectDark = {
  name: "TreeSelect",
  common: dark_default,
  peers: {
    Tree: dark_default6,
    Empty: dark_default3,
    InternalSelection: dark_default4
  }
};
var dark_default7 = treeSelectDark;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/tree-select/styles/light.mjs
function self2(vars) {
  const {
    popoverColor,
    boxShadow2,
    borderRadius,
    heightMedium,
    dividerColor,
    textColor2
  } = vars;
  return {
    menuPadding: "4px",
    menuColor: popoverColor,
    menuBoxShadow: boxShadow2,
    menuBorderRadius: borderRadius,
    menuHeight: `calc(${heightMedium} * 7.6)`,
    actionDividerColor: dividerColor,
    actionTextColor: textColor2,
    actionPadding: "8px 12px",
    headerDividerColor: dividerColor,
    headerTextColor: textColor2,
    headerPadding: "8px 12px"
  };
}
var treeSelectLight = createTheme({
  name: "TreeSelect",
  common: light_default,
  peers: {
    Tree: light_default6,
    Empty: light_default3,
    InternalSelection: light_default4
  },
  self: self2
});
var light_default7 = treeSelectLight;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/_styles/transitions/fade-in-height-expand.cssr.mjs
var {
  cubicBezierEaseInOut,
  cubicBezierEaseOut,
  cubicBezierEaseIn
} = common_default;
function fadeInHeightExpandTransition({
  overflow = "hidden",
  duration = ".3s",
  originalTransition = "",
  leavingDelay = "0s",
  foldPadding = false,
  enterToProps = void 0,
  leaveToProps = void 0,
  reverse = false
} = {}) {
  const enterClass = reverse ? "leave" : "enter";
  const leaveClass = reverse ? "enter" : "leave";
  return [c(`&.fade-in-height-expand-transition-${leaveClass}-from,
 &.fade-in-height-expand-transition-${enterClass}-to`, Object.assign(Object.assign({}, enterToProps), {
    opacity: 1
  })), c(`&.fade-in-height-expand-transition-${leaveClass}-to,
 &.fade-in-height-expand-transition-${enterClass}-from`, Object.assign(Object.assign({}, leaveToProps), {
    opacity: 0,
    marginTop: "0 !important",
    marginBottom: "0 !important",
    paddingTop: foldPadding ? "0 !important" : void 0,
    paddingBottom: foldPadding ? "0 !important" : void 0
  })), c(`&.fade-in-height-expand-transition-${leaveClass}-active`, `
 overflow: ${overflow};
 transition:
 max-height ${duration} ${cubicBezierEaseInOut} ${leavingDelay},
 opacity ${duration} ${cubicBezierEaseOut} ${leavingDelay},
 margin-top ${duration} ${cubicBezierEaseInOut} ${leavingDelay},
 margin-bottom ${duration} ${cubicBezierEaseInOut} ${leavingDelay},
 padding-top ${duration} ${cubicBezierEaseInOut} ${leavingDelay},
 padding-bottom ${duration} ${cubicBezierEaseInOut} ${leavingDelay}
 ${originalTransition ? `,${originalTransition}` : ""}
 `), c(`&.fade-in-height-expand-transition-${enterClass}-active`, `
 overflow: ${overflow};
 transition:
 max-height ${duration} ${cubicBezierEaseInOut},
 opacity ${duration} ${cubicBezierEaseIn},
 margin-top ${duration} ${cubicBezierEaseInOut},
 margin-bottom ${duration} ${cubicBezierEaseInOut},
 padding-top ${duration} ${cubicBezierEaseInOut},
 padding-bottom ${duration} ${cubicBezierEaseInOut}
 ${originalTransition ? `,${originalTransition}` : ""}
 `)];
}

export {
  fadeInHeightExpandTransition,
  light_default6 as light_default,
  dark_default6 as dark_default,
  treeRtl,
  dark_default7 as dark_default2,
  light_default7 as light_default2
};
//# sourceMappingURL=chunk-M64XX42F.js.map
