<template>
  <Page
    description="本页面包含系统基本设定"
    title="通用"
  >
    <NCard title="通知"><FormMachine /></NCard><br/>

  </Page>
</template>

<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { NButton, NCard, useMessage } from 'naive-ui';
import { useVbenForm } from '#/adapter/form';
import { ref, onMounted } from 'vue';
import { getNotificationSettings, setNotificationSettings } from '#/api/index';

const message = useMessage();
const [FormMachine, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    // componentProps: {
    //   class: 'w-full',
    // },
  },
  layout: 'horizontal',
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    setNotificationSettings(values).then(() => {
      message.success('设置已保存');
    }).catch((error) => {
      console.log(`保存失败: ${error.message}`);
    });
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'RadioGroup',
      fieldName: 'finishSound',
      label: '完成提示音',
      componentProps: {
        isButton: true,
        class: 'flex flex-wrap', // 如果选项过多，可以添加class来自动折叠
        options: [
          { value: 'disabled', label: '关闭' },
          { value: 'dingdong', label: '叮咚' },
          { value: 'bubble', label: '冒泡' },
        ],
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'filmSound',
      label: '快门提示音',
      componentProps: {
        isButton: true,
        class: 'flex flex-wrap', // 如果选项过多，可以添加class来自动折叠
        options: [
          { value: 'disabled', label: '关闭' },
          { value: 'sound1', label: '音效一' },
          { value: 'sound2', label: '音效二' },
        ],
      },
    }
  ],
});

onMounted(() => {
  getNotificationSettings().then(res => {
    formApi.setValues({
      finishSound: res.finishSound,
      filmSound: res.filmSound,
    });
  }).catch(error => {
    console.log(`获取设置失败: ${error.message}`);
  });
});

</script>
