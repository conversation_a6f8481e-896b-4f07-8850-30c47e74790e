import{i as G,f as R,u as j,b as C,g as E,s as I,h as M,j as P,k as w}from"./bootstrap-MyT3sENS.js";import{g as T}from"./get-slot-Bk_rJcZu.js";import{d as A,h as S,C as L,c as O}from"../jse/index-index-Y3_OtjO-.js";let h;function W(){if(!G)return!0;if(h===void 0){const e=document.createElement("div");e.style.display="flex",e.style.flexDirection="column",e.style.rowGap="1px",e.appendChild(document.createElement("div")),e.appendChild(document.createElement("div")),document.body.appendChild(e);const r=e.scrollHeight===1;return document.body.removeChild(e),h=r}return h}const D=Object.assign(Object.assign({},C.props),{align:String,justify:{type:String,default:"start"},inline:Boolean,vertical:Boolean,reverse:Boolean,size:{type:[String,Number,Array],default:"medium"},wrapItem:{type:Boolean,default:!0},itemClass:String,itemStyle:[String,Object],wrap:{type:Boolean,default:!0},internalUseGap:{type:Boolean,default:void 0}}),U=A({name:"Space",props:D,setup(e){const{mergedClsPrefixRef:r,mergedRtlRef:u}=j(e),d=C("Space","-space",void 0,I,e,r),t=E("Space",u,r);return{useGap:W(),rtlEnabled:t,mergedClsPrefix:r,margin:O(()=>{const{size:n}=e;if(Array.isArray(n))return{horizontal:n[0],vertical:n[1]};if(typeof n=="number")return{horizontal:n,vertical:n};const{self:{[M("gap",n)]:f}}=d.value,{row:a,col:g}=P(f);return{horizontal:w(g),vertical:w(a)}})}},render(){const{vertical:e,reverse:r,align:u,inline:d,justify:t,itemClass:n,itemStyle:f,margin:a,wrap:g,mergedClsPrefix:v,rtlEnabled:x,useGap:o,wrapItem:B,internalUseGap:$}=this,p=R(T(this),!1);if(!p.length)return null;const b=`${a.horizontal}px`,c=`${a.horizontal/2}px`,z=`${a.vertical}px`,s=`${a.vertical/2}px`,l=p.length-1,m=t.startsWith("space-");return S("div",{role:"none",class:[`${v}-space`,x&&`${v}-space--rtl`],style:{display:d?"inline-flex":"flex",flexDirection:e&&!r?"column":e&&r?"column-reverse":!e&&r?"row-reverse":"row",justifyContent:["start","end"].includes(t)?`flex-${t}`:t,flexWrap:!g||e?"nowrap":"wrap",marginTop:o||e?"":`-${s}`,marginBottom:o||e?"":`-${s}`,alignItems:u,gap:o?`${a.vertical}px ${a.horizontal}px`:""}},!B&&(o||$)?p:p.map((y,i)=>y.type===L?y:S("div",{role:"none",class:n,style:[f,{maxWidth:"100%"},o?"":e?{marginBottom:i!==l?z:""}:x?{marginLeft:m?t==="space-between"&&i===l?"":c:i!==l?b:"",marginRight:m?t==="space-between"&&i===0?"":c:"",paddingTop:s,paddingBottom:s}:{marginRight:m?t==="space-between"&&i===l?"":c:i!==l?b:"",marginLeft:m?t==="space-between"&&i===0?"":c:"",paddingTop:s,paddingBottom:s}]},y)))}});export{U as NSpace,D as spaceProps};
