var q=Object.defineProperty,H=Object.defineProperties;var E=Object.getOwnPropertyDescriptors;var V=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var P=(a,s,e)=>s in a?q(a,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[s]=e,_=(a,s)=>{for(var e in s||(s={}))B.call(s,e)&&P(a,e,s[e]);if(V)for(var e of V(s))z.call(s,e)&&P(a,e,s[e]);return a},M=(a,s)=>H(a,E(s));var m=(a,s)=>{var e={};for(var t in a)B.call(a,t)&&s.indexOf(t)<0&&(e[t]=a[t]);if(a!=null&&V)for(var t of V(a))s.indexOf(t)<0&&z.call(a,t)&&(e[t]=a[t]);return e};import{a as w,ar as R,b2 as K,aw as $,b3 as J,b4 as Q,b5 as Y,b6 as X,b7 as Z,b8 as A,b9 as j,ba as ee,bb as te,bc as se,bd as ae,be as ne}from"./bootstrap-MyT3sENS.js";import{d as u,j as p,o as f,u as i,a3 as T,a4 as W,s as l,n as d,c as S,L as b,a5 as h,v as c,D as I,g as oe,aa as re,a9 as ie,ab as le,h as F,r as ce,w as de,W as ue,ac as pe}from"../jse/index-index-Y3_OtjO-.js";const fe=w("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);const N=w("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);const he=w("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);const ge=w("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);const Ce=w("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),$e=u({__name:"Select",props:{open:{type:Boolean},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{},dir:{},name:{},autocomplete:{},disabled:{type:Boolean},required:{type:Boolean}},emits:["update:modelValue","update:open"],setup(a,{emit:s}){const n=R(a,s);return(o,r)=>(f(),p(i(K),T(W(i(n))),{default:l(()=>[d(o.$slots,"default")]),_:3},16))}}),_e=u({__name:"SelectScrollDownButton",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const s=a,e=S(()=>{const r=s,{class:n}=r;return m(r,["class"])}),t=$(e);return(n,o)=>(f(),p(i(J),b(i(t),{class:i(h)("flex cursor-default items-center justify-center py-1",s.class)}),{default:l(()=>[d(n.$slots,"default",{},()=>[c(i(N),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),me=u({__name:"SelectScrollUpButton",props:{asChild:{type:Boolean},as:{},class:{}},setup(a){const s=a,e=S(()=>{const r=s,{class:n}=r;return m(r,["class"])}),t=$(e);return(n,o)=>(f(),p(i(Q),b(i(t),{class:i(h)("flex cursor-default items-center justify-center py-1",s.class)}),{default:l(()=>[d(n.$slots,"default",{},()=>[c(i(he),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),xe=u({inheritAttrs:!1,__name:"SelectContent",props:{forceMount:{type:Boolean},position:{default:"popper"},bodyLock:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(a,{emit:s}){const e=a,t=s,n=S(()=>{const O=e,{class:r}=O;return m(O,["class"])}),o=R(n,t);return(r,g)=>(f(),p(i(Y),null,{default:l(()=>[c(i(X),b(_(_({},i(o)),r.$attrs),{class:i(h)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup relative max-h-96 min-w-32 overflow-hidden rounded-md border shadow-md",r.position==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e.class)}),{default:l(()=>[c(me),c(i(Z),{class:I(i(h)("p-1",r.position==="popper"&&"h-[--radix-select-trigger-height] w-full min-w-[--radix-select-trigger-width]"))},{default:l(()=>[d(r.$slots,"default")]),_:3},8,["class"]),c(_e)]),_:3},16,["class"])]),_:3}))}}),ye={class:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center"},Oe=u({__name:"SelectItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},setup(a){const s=a,e=S(()=>{const r=s,{class:n}=r;return m(r,["class"])}),t=$(e);return(n,o)=>(f(),p(i(A),b(i(t),{class:i(h)("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s.class)}),{default:l(()=>[oe("span",ye,[c(i(j),null,{default:l(()=>[c(i(fe),{class:"h-4 w-4"})]),_:1})]),c(i(ee),null,{default:l(()=>[d(n.$slots,"default")]),_:3})]),_:3},16,["class"]))}}),Pe=u({__name:"SelectTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(a){const s=a,e=S(()=>{const r=s,{class:n}=r;return m(r,["class"])}),t=$(e);return(n,o)=>(f(),p(i(te),b(i(t),{class:i(h)("border-input ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full items-center justify-between whitespace-nowrap rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s.class)}),{default:l(()=>[d(n.$slots,"default"),c(i(se),{"as-child":""},{default:l(()=>[c(i(N),{class:"h-4 w-4 opacity-50"})]),_:1})]),_:3},16,["class"]))}}),Be=u({__name:"SelectValue",props:{placeholder:{},asChild:{type:Boolean},as:{}},setup(a){const s=a;return(e,t)=>(f(),p(i(ae),T(W(s)),{default:l(()=>[d(e.$slots,"default")]),_:3},16))}}),ze=u({name:"RenderContent",props:{content:{default:void 0,type:[Object,String,Function]},renderBr:{default:!1,type:Boolean}},setup(a,{attrs:s,slots:e}){return()=>{if(!a.content)return null;if(!((re(a.content)||ie(a.content))&&a.content!==null))if(a.renderBr&&le(a.content)){const n=a.content.split(`
`),o=[];for(const[r,g]of n.entries())o.push(F("p",{key:r},g));return o}else return a.content;return F(a.content,M(_({},s),{props:_(_({},a),s),slots:e}))}}}),Me=u({inheritAttrs:!1,__name:"help-tooltip",props:{triggerClass:{}},setup(a){return(s,e)=>(f(),p(ne,{"delay-duration":300,side:"right"},{trigger:l(()=>[d(s.$slots,"trigger",{},()=>[c(i(ge),{class:I(i(h)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer",s.triggerClass))},null,8,["class"])])]),default:l(()=>[d(s.$slots,"default")]),_:3}))}}),y=new WeakMap,D=new WeakMap,C={current:[]};let x=!1;const k=new Set,L=new Map;function U(a){const s=Array.from(a).sort((e,t)=>e instanceof v&&e.options.deps.includes(t)?1:t instanceof v&&t.options.deps.includes(e)?-1:0);for(const e of s){if(C.current.includes(e))continue;C.current.push(e),e.recompute();const t=D.get(e);if(t)for(const n of t){const o=y.get(n);o&&U(o)}}}function ve(a){a.listeners.forEach(s=>s({prevVal:a.prevState,currentVal:a.state}))}function we(a){a.listeners.forEach(s=>s({prevVal:a.prevState,currentVal:a.state}))}function Se(a){var s;if(k.add(a),!x)try{for(x=!0;k.size>0;){const e=Array.from(k);k.clear();for(const t of e){const n=(s=L.get(t))!=null?s:t.prevState;t.prevState=n,ve(t)}for(const t of e){const n=y.get(t);n&&(C.current.push(t),U(n))}for(const t of e){const n=y.get(t);if(n)for(const o of n)we(o)}}}finally{x=!1,C.current=[],L.clear()}}class G{constructor(s,e){this.listeners=new Set,this.subscribe=t=>{var n,o;this.listeners.add(t);const r=(o=(n=this.options)==null?void 0:n.onSubscribe)==null?void 0:o.call(n,t,this);return()=>{this.listeners.delete(t),r==null||r()}},this.setState=t=>{var n,o,r;this.prevState=this.state,this.state=(n=this.options)!=null&&n.updateFn?this.options.updateFn(this.prevState)(t):t(this.prevState),(r=(o=this.options)==null?void 0:o.onUpdate)==null||r.call(o),Se(this)},this.prevState=s,this.state=s,this.options=e}}class v{constructor(s){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{var n;const e=[],t=[];for(const o of this.options.deps)e.push(o.prevState),t.push(o.state);return this.lastSeenDepValues=t,{prevDepVals:e,currDepVals:t,prevVal:(n=this.prevState)!=null?n:void 0}},this.recompute=()=>{var e,t;this.prevState=this.state;const{prevDepVals:n,currDepVals:o,prevVal:r}=this.getDepVals();this.state=this.options.fn({prevDepVals:n,currDepVals:o,prevVal:r}),(t=(e=this.options).onUpdate)==null||t.call(e)},this.checkIfRecalculationNeededDeeply=()=>{for(const o of this.options.deps)o instanceof v&&o.checkIfRecalculationNeededDeeply();let e=!1;const t=this.lastSeenDepValues,{currDepVals:n}=this.getDepVals();for(let o=0;o<n.length;o++)if(n[o]!==t[o]){e=!0;break}e&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const e of this._subscriptions)e()}),this.subscribe=e=>{var t,n;this.listeners.add(e);const o=(n=(t=this.options).onSubscribe)==null?void 0:n.call(t,e,this);return()=>{this.listeners.delete(e),o==null||o()}},this.options=s,this.state=s.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(s=this.options.deps){for(const e of s)if(e instanceof v)e.registerOnGraph(),this.registerOnGraph(e.options.deps);else if(e instanceof G){let t=y.get(e);t||(t=new Set,y.set(e,t)),t.add(this);let n=D.get(this);n||(n=new Set,D.set(this,n)),n.add(e)}}unregisterFromGraph(s=this.options.deps){for(const e of s)if(e instanceof v)this.unregisterFromGraph(e.options.deps);else if(e instanceof G){const t=y.get(e);t&&t.delete(this);const n=D.get(this);n&&n.delete(e)}}}function Fe(a,s=e=>e){const e=ce(s(a.state));return de(()=>a,(t,n,o)=>{const r=t.subscribe(()=>{const g=s(t.state);be(pe(e.value),g)||(e.value=g)});o(()=>{r()})},{immediate:!0}),ue(e)}function be(a,s){if(Object.is(a,s))return!0;if(typeof a!="object"||a===null||typeof s!="object"||s===null)return!1;if(a instanceof Map&&s instanceof Map){if(a.size!==s.size)return!1;for(const[t,n]of a)if(!s.has(t)||!Object.is(n,s.get(t)))return!1;return!0}if(a instanceof Set&&s instanceof Set){if(a.size!==s.size)return!1;for(const t of a)if(!s.has(t))return!1;return!0}const e=Object.keys(a);if(e.length!==Object.keys(s).length)return!1;for(let t=0;t<e.length;t++)if(!Object.prototype.hasOwnProperty.call(s,e[t])||!Object.is(a[e[t]],s[e[t]]))return!1;return!0}export{fe as C,Ce as M,G as S,Me as _,N as a,$e as b,Be as c,Pe as d,Oe as e,xe as f,ze as g,ge as h,Fe as u};
