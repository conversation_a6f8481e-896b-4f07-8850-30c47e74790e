import{Z as _t,a8 as mt,N as en,y as tn,Q as nn,i as ln,L as J,aR as an,aS as on,B as Ut,l as I,n as se,o as V,p as ze,m as xe,aT as Pt,q as rn,a5 as kt,aU as Ot,O as wt,k as Qe,u as $t,g as dn,b as rt,x as Mt,aV as sn,A as j,aa as cn,a9 as un,ac as fn,v as At,w as hn,ad as gn,ae as vn,af as yn,aW as bn}from"./bootstrap-MyT3sENS.js";import{d as Fe,h as g,i as He,r as L,c as O,y as mn,K as Ze,t as D,w as ot,I as jt,H as it,z as kn}from"../jse/index-index-Y3_OtjO-.js";import{NCheckbox as xn}from"./index-C30meZ0q.js";import{h as Ve,F as wn}from"./FocusDetector-CwuXLSZQ.js";import{V as pn}from"./VirtualList-CqdUtx-w.js";import{b as pt,c as Sn,N as St,f as Lt,a as Rn,u as Cn}from"./Selection-Cono1GyR.js";import{u as Le}from"./use-merged-state-DFvgmEt8.js";import{B as Nn,V as Kn,e as Tn,u as Rt}from"./Follower-B0-DuUBY.js";import{u as Fn}from"./use-locale-DPELDQgW.js";import"./Suffix-CqN684CL.js";import"./Popover-BJHGarS6.js";import"./format-length-B-p6aW7q.js";function zt(e){return typeof e=="string"?`s-${e}`:`n-${e}`}const Dn=Fe({name:"Switcher",render(){return g("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},g("path",{d:"M12 8l10 8l-10 8z"}))}}),Nt=_t("n-tree-select");function Et({position:e,offsetLevel:l,indent:r,el:c}){const i={position:"absolute",boxSizing:"border-box",right:0};if(e==="inside")i.left=0,i.top=0,i.bottom=0,i.borderRadius="inherit",i.boxShadow="inset 0 0 0 2px var(--n-drop-mark-color)";else{const s=e==="before"?"top":"bottom";i[s]=0,i.left=`${c.offsetLeft+6-l*r}px`,i.height="2px",i.backgroundColor="var(--n-drop-mark-color)",i.transformOrigin=s,i.borderRadius="1px",i.transform=e==="before"?"translateY(-4px)":"translateY(4px)"}return g("div",{style:i})}function Pn({dropPosition:e,node:l}){return l.isLeaf===!1||l.children?!0:e!=="inside"}const Je=_t("n-tree");function On({props:e,fNodesRef:l,mergedExpandedKeysRef:r,mergedSelectedKeysRef:c,mergedCheckedKeysRef:i,handleCheck:s,handleSelect:p,handleSwitcherClick:y}){const{value:w}=c,k=He(Nt,null),x=k?k.pendingNodeKeyRef:L(w.length?w[w.length-1]:null);function U(C){var _;if(!e.keyboard)return{enterBehavior:null};const{value:T}=x;let K=null;if(T===null){if((C.key==="ArrowDown"||C.key==="ArrowUp")&&C.preventDefault(),["ArrowDown","ArrowUp","ArrowLeft","ArrowRight"].includes(C.key)&&T===null){const{value:b}=l;let v=0;for(;v<b.length;){if(!b[v].disabled){x.value=b[v].key;break}v+=1}}}else{const{value:b}=l;let v=b.findIndex(N=>N.key===T);if(!~v)return{enterBehavior:null};if(C.key==="Enter"){const N=b[v];switch(K=((_=e.overrideDefaultNodeClickBehavior)===null||_===void 0?void 0:_.call(e,{option:N.rawNode}))||null,K){case"toggleCheck":s(N,!i.value.includes(N.key));break;case"toggleSelect":p(N);break;case"toggleExpand":y(N);break;case"none":break;case"default":default:K="default",p(N)}}else if(C.key==="ArrowDown")for(C.preventDefault(),v+=1;v<b.length;){if(!b[v].disabled){x.value=b[v].key;break}v+=1}else if(C.key==="ArrowUp")for(C.preventDefault(),v-=1;v>=0;){if(!b[v].disabled){x.value=b[v].key;break}v-=1}else if(C.key==="ArrowLeft"){const N=b[v];if(N.isLeaf||!r.value.includes(T)){const P=N.getParent();P&&(x.value=P.key)}else y(N)}else if(C.key==="ArrowRight"){const N=b[v];if(N.isLeaf)return{enterBehavior:null};if(!r.value.includes(T))y(N);else for(v+=1;v<b.length;){if(!b[v].disabled){x.value=b[v].key;break}v+=1}}}return{enterBehavior:K}}return{pendingNodeKeyRef:x,handleKeydown:U}}const An=Fe({name:"NTreeNodeCheckbox",props:{clsPrefix:{type:String,required:!0},indent:{type:Number,required:!0},right:Boolean,focusable:Boolean,disabled:Boolean,checked:Boolean,indeterminate:Boolean,onCheck:Function},setup(e){const l=He(Je);function r(i){const{onCheck:s}=e;s&&s(i)}function c(i){r(i)}return{handleUpdateValue:c,mergedTheme:l.mergedThemeRef}},render(){const{clsPrefix:e,mergedTheme:l,checked:r,indeterminate:c,disabled:i,focusable:s,indent:p,handleUpdateValue:y}=this;return g("span",{class:[`${e}-tree-node-checkbox`,this.right&&`${e}-tree-node-checkbox--right`],style:{width:`${p}px`},"data-checkbox":!0},g(xn,{focusable:s,disabled:i,theme:l.peers.Checkbox,themeOverrides:l.peerOverrides.Checkbox,checked:r,indeterminate:c,onUpdateChecked:y}))}}),Ln=Fe({name:"TreeNodeContent",props:{clsPrefix:{type:String,required:!0},disabled:Boolean,checked:Boolean,selected:Boolean,onClick:Function,onDragstart:Function,tmNode:{type:Object,required:!0},nodeProps:Object},setup(e){const{renderLabelRef:l,renderPrefixRef:r,renderSuffixRef:c,labelFieldRef:i}=He(Je),s=L(null);function p(w){const{onClick:k}=e;k&&k(w)}function y(w){p(w)}return{selfRef:s,renderLabel:l,renderPrefix:r,renderSuffix:c,labelField:i,handleClick:y}},render(){const{clsPrefix:e,labelField:l,nodeProps:r,checked:c=!1,selected:i=!1,renderLabel:s,renderPrefix:p,renderSuffix:y,handleClick:w,onDragstart:k,tmNode:{rawNode:x,rawNode:{prefix:U,suffix:C,[l]:_}}}=this;return g("span",Object.assign({},r,{ref:"selfRef",class:[`${e}-tree-node-content`,r==null?void 0:r.class],onClick:w,draggable:k===void 0?void 0:!0,onDragstart:k}),p||U?g("div",{class:`${e}-tree-node-content__prefix`},p?p({option:x,selected:i,checked:c}):mt(U)):null,g("div",{class:`${e}-tree-node-content__text`},s?s({option:x,selected:i,checked:c}):mt(_)),y||C?g("div",{class:`${e}-tree-node-content__suffix`},y?y({option:x,selected:i,checked:c}):mt(C)):null)}}),En=Fe({name:"NTreeSwitcher",props:{clsPrefix:{type:String,required:!0},indent:{type:Number,required:!0},expanded:Boolean,selected:Boolean,hide:Boolean,loading:Boolean,onClick:Function,tmNode:{type:Object,required:!0}},setup(e){const{renderSwitcherIconRef:l}=He(Je,null);return()=>{const{clsPrefix:r,expanded:c,hide:i,indent:s,onClick:p}=e;return g("span",{"data-switcher":!0,class:[`${r}-tree-node-switcher`,c&&`${r}-tree-node-switcher--expanded`,i&&`${r}-tree-node-switcher--hide`],style:{width:`${s}px`},onClick:p},g("div",{class:`${r}-tree-node-switcher__icon`},g(en,null,{default:()=>{if(e.loading)return g(tn,{clsPrefix:r,key:"loading",radius:85,strokeWidth:20});const{value:y}=l;return y?y({expanded:e.expanded,selected:e.selected,option:e.tmNode.rawNode}):g(nn,{clsPrefix:r,key:"switcher"},{default:()=>g(Dn,null)})}})))}}});function Vt(e){return O(()=>e.leafOnly?"child":e.checkStrategy)}function Te(e,l){return!!e.rawNode[l]}function Ht(e,l,r,c){e==null||e.forEach(i=>{r(i),Ht(i[l],l,r,c),c(i)})}function Bn(e,l,r,c,i){const s=new Set,p=new Set,y=[];return Ht(e,c,w=>{if(y.push(w),i(l,w)){p.add(w[r]);for(let k=y.length-2;k>=0;--k)if(!s.has(y[k][r]))s.add(y[k][r]);else return}},()=>{y.pop()}),{expandedKeys:Array.from(s),highlightKeySet:p}}if(ln&&Image){const e=new Image;e.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="}function In(e,l,r,c,i){const s=new Set,p=new Set,y=new Set,w=[],k=[],x=[];function U(_){_.forEach(T=>{if(x.push(T),l(r,T)){s.add(T[c]),y.add(T[c]);for(let b=x.length-2;b>=0;--b){const v=x[b][c];if(!p.has(v))p.add(v),s.has(v)&&s.delete(v);else break}}const K=T[i];K&&U(K),x.pop()})}U(e);function C(_,T){_.forEach(K=>{const b=K[c],v=s.has(b),N=p.has(b);if(!v&&!N)return;const P=K[i];if(P)if(v)T.push(K);else{w.push(b);const H=Object.assign(Object.assign({},K),{[i]:[]});T.push(H),C(P,H[i])}else T.push(K)})}return C(e,k),{filteredTree:k,highlightKeySet:y,expandedKeys:w}}const qt=Fe({name:"TreeNode",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(e){const l=He(Je),{droppingNodeParentRef:r,droppingMouseNodeRef:c,draggingNodeRef:i,droppingPositionRef:s,droppingOffsetLevelRef:p,nodePropsRef:y,indentRef:w,blockLineRef:k,checkboxPlacementRef:x,checkOnClickRef:U,disabledFieldRef:C,showLineRef:_,renderSwitcherIconRef:T,overrideDefaultNodeClickBehaviorRef:K}=l,b=J(()=>!!e.tmNode.rawNode.checkboxDisabled),v=J(()=>Te(e.tmNode,C.value)),N=J(()=>l.disabledRef.value||v.value),P=O(()=>{const{value:u}=y;if(u)return u({option:e.tmNode.rawNode})}),H=L(null),q={value:null};mn(()=>{q.value=H.value.$el});function X(){const u=()=>{const{tmNode:R}=e;if(!R.isLeaf&&!R.shallowLoaded){if(!l.loadingKeysRef.value.has(R.key))l.loadingKeysRef.value.add(R.key);else return;const{onLoadRef:{value:B}}=l;B&&B(R.rawNode).then(M=>{M!==!1&&l.handleSwitcherClick(R)}).finally(()=>{l.loadingKeysRef.value.delete(R.key)})}else l.handleSwitcherClick(R)};T.value?setTimeout(u,0):u()}const we=J(()=>!v.value&&l.selectableRef.value&&(l.internalTreeSelect?l.mergedCheckStrategyRef.value!=="child"||l.multipleRef.value&&l.cascadeRef.value||e.tmNode.isLeaf:!0)),Q=J(()=>l.checkableRef.value&&(l.cascadeRef.value||l.mergedCheckStrategyRef.value!=="child"||e.tmNode.isLeaf)),fe=J(()=>l.displayedCheckedKeysRef.value.includes(e.tmNode.key)),he=J(()=>{const{value:u}=Q;if(!u)return!1;const{value:R}=U,{tmNode:B}=e;return typeof R=="boolean"?!B.disabled&&R:R(e.tmNode.rawNode)});function ge(u){const{value:R}=l.expandOnClickRef,{value:B}=we,{value:M}=he;if(!B&&!R&&!M||Ve(u,"checkbox")||Ve(u,"switcher"))return;const{tmNode:W}=e;B&&l.handleSelect(W),R&&!W.isLeaf&&X(),M&&Y(!fe.value)}function pe(u){var R,B;if(!(Ve(u,"checkbox")||Ve(u,"switcher"))){if(!N.value){const M=K.value;let W=!1;if(M)switch(M({option:e.tmNode.rawNode})){case"toggleCheck":W=!0,Y(!fe.value);break;case"toggleSelect":W=!0,l.handleSelect(e.tmNode);break;case"toggleExpand":W=!0,X(),W=!0;break;case"none":W=!0,W=!0;return}W||ge(u)}(B=(R=P.value)===null||R===void 0?void 0:R.onClick)===null||B===void 0||B.call(R,u)}}function qe(u){k.value||pe(u)}function ee(u){k.value&&pe(u)}function Y(u){l.handleCheck(e.tmNode,u)}function ve(u){l.handleDragStart({event:u,node:e.tmNode})}function Ee(u){u.currentTarget===u.target&&l.handleDragEnter({event:u,node:e.tmNode})}function ne(u){u.preventDefault(),l.handleDragOver({event:u,node:e.tmNode})}function ye(u){l.handleDragEnd({event:u,node:e.tmNode})}function De(u){u.currentTarget===u.target&&l.handleDragLeave({event:u,node:e.tmNode})}function Se(u){u.preventDefault(),s.value!==null&&l.handleDrop({event:u,node:e.tmNode,dropPosition:s.value})}const Be=O(()=>{const{clsPrefix:u}=e,{value:R}=w;if(_.value){const B=[];let M=e.tmNode.parent;for(;M;)M.isLastChild?B.push(g("div",{class:`${u}-tree-node-indent`},g("div",{style:{width:`${R}px`}}))):B.push(g("div",{class:[`${u}-tree-node-indent`,`${u}-tree-node-indent--show-line`]},g("div",{style:{width:`${R}px`}}))),M=M.parent;return B.reverse()}else return an(e.tmNode.level,g("div",{class:`${e.clsPrefix}-tree-node-indent`},g("div",{style:{width:`${R}px`}})))});return{showDropMark:J(()=>{const{value:u}=i;if(!u)return;const{value:R}=s;if(!R)return;const{value:B}=c;if(!B)return;const{tmNode:M}=e;return M.key===B.key}),showDropMarkAsParent:J(()=>{const{value:u}=r;if(!u)return!1;const{tmNode:R}=e,{value:B}=s;return B==="before"||B==="after"?u.key===R.key:!1}),pending:J(()=>l.pendingNodeKeyRef.value===e.tmNode.key),loading:J(()=>l.loadingKeysRef.value.has(e.tmNode.key)),highlight:J(()=>{var u;return(u=l.highlightKeySetRef.value)===null||u===void 0?void 0:u.has(e.tmNode.key)}),checked:fe,indeterminate:J(()=>l.displayedIndeterminateKeysRef.value.includes(e.tmNode.key)),selected:J(()=>l.mergedSelectedKeysRef.value.includes(e.tmNode.key)),expanded:J(()=>l.mergedExpandedKeysRef.value.includes(e.tmNode.key)),disabled:N,checkable:Q,mergedCheckOnClick:he,checkboxDisabled:b,selectable:we,expandOnClick:l.expandOnClickRef,internalScrollable:l.internalScrollableRef,draggable:l.draggableRef,blockLine:k,nodeProps:P,checkboxFocusable:l.internalCheckboxFocusableRef,droppingPosition:s,droppingOffsetLevel:p,indent:w,checkboxPlacement:x,showLine:_,contentInstRef:H,contentElRef:q,indentNodes:Be,handleCheck:Y,handleDrop:Se,handleDragStart:ve,handleDragEnter:Ee,handleDragOver:ne,handleDragEnd:ye,handleDragLeave:De,handleLineClick:ee,handleContentClick:qe,handleSwitcherClick:X}},render(){const{tmNode:e,clsPrefix:l,checkable:r,expandOnClick:c,selectable:i,selected:s,checked:p,highlight:y,draggable:w,blockLine:k,indent:x,indentNodes:U,disabled:C,pending:_,internalScrollable:T,nodeProps:K,checkboxPlacement:b}=this,v=w&&!C?{onDragenter:this.handleDragEnter,onDragleave:this.handleDragLeave,onDragend:this.handleDragEnd,onDrop:this.handleDrop,onDragover:this.handleDragOver}:void 0,N=T?zt(e.key):void 0,P=b==="right",H=r?g(An,{indent:x,right:P,focusable:this.checkboxFocusable,disabled:C||this.checkboxDisabled,clsPrefix:l,checked:this.checked,indeterminate:this.indeterminate,onCheck:this.handleCheck}):null;return g("div",Object.assign({class:`${l}-tree-node-wrapper`},v),g("div",Object.assign({},k?K:void 0,{class:[`${l}-tree-node`,{[`${l}-tree-node--selected`]:s,[`${l}-tree-node--checkable`]:r,[`${l}-tree-node--highlight`]:y,[`${l}-tree-node--pending`]:_,[`${l}-tree-node--disabled`]:C,[`${l}-tree-node--selectable`]:i,[`${l}-tree-node--clickable`]:i||c||this.mergedCheckOnClick},K==null?void 0:K.class],"data-key":N,draggable:w&&k,onClick:this.handleLineClick,onDragstart:w&&k&&!C?this.handleDragStart:void 0}),U,e.isLeaf&&this.showLine?g("div",{class:[`${l}-tree-node-indent`,`${l}-tree-node-indent--show-line`,e.isLeaf&&`${l}-tree-node-indent--is-leaf`,e.isLastChild&&`${l}-tree-node-indent--last-child`]},g("div",{style:{width:`${x}px`}})):g(En,{clsPrefix:l,expanded:this.expanded,selected:s,loading:this.loading,hide:e.isLeaf,tmNode:this.tmNode,indent:x,onClick:this.handleSwitcherClick}),P?null:H,g(Ln,{ref:"contentInstRef",clsPrefix:l,checked:p,selected:s,onClick:this.handleContentClick,nodeProps:k?void 0:K,onDragstart:w&&!k&&!C?this.handleDragStart:void 0,tmNode:e}),w?this.showDropMark?Et({el:this.contentElRef.value,position:this.droppingPosition,offsetLevel:this.droppingOffsetLevel,indent:x}):this.showDropMarkAsParent?Et({el:this.contentElRef.value,position:"inside",offsetLevel:this.droppingOffsetLevel,indent:x}):null:null,P?H:null))}}),_n=Fe({name:"TreeMotionWrapper",props:{clsPrefix:{type:String,required:!0},height:Number,nodes:{type:Array,required:!0},mode:{type:String,required:!0},onAfterEnter:{type:Function,required:!0}},render(){const{clsPrefix:e}=this;return g(on,{onAfterEnter:this.onAfterEnter,appear:!0,reverse:this.mode==="collapse"},{default:()=>g("div",{class:[`${e}-tree-motion-wrapper`,`${e}-tree-motion-wrapper--${this.mode}`],style:{height:Ut(this.height)}},this.nodes.map(l=>g(qt,{clsPrefix:e,tmNode:l})))})}}),xt=rn(),Un=I("tree",`
 font-size: var(--n-font-size);
 outline: none;
`,[se("ul, li",`
 margin: 0;
 padding: 0;
 list-style: none;
 `),se(">",[I("tree-node",[se("&:first-child","margin-top: 0;")])]),I("tree-motion-wrapper",[V("expand",[Pt({duration:"0.2s"})]),V("collapse",[Pt({duration:"0.2s",reverse:!0})])]),I("tree-node-wrapper",`
 box-sizing: border-box;
 padding: var(--n-node-wrapper-padding);
 `),I("tree-node",`
 transform: translate3d(0,0,0);
 position: relative;
 display: flex;
 border-radius: var(--n-node-border-radius);
 transition: background-color .3s var(--n-bezier);
 `,[V("highlight",[I("tree-node-content",[xe("text","border-bottom-color: var(--n-node-text-color-disabled);")])]),V("disabled",[I("tree-node-content",`
 color: var(--n-node-text-color-disabled);
 cursor: not-allowed;
 `)]),ze("disabled",[V("clickable",[I("tree-node-content",`
 cursor: pointer;
 `)])])]),V("block-node",[I("tree-node-content",`
 flex: 1;
 min-width: 0;
 `)]),ze("block-line",[I("tree-node",[ze("disabled",[I("tree-node-content",[se("&:hover","background: var(--n-node-color-hover);")]),V("selectable",[I("tree-node-content",[se("&:active","background: var(--n-node-color-pressed);")])]),V("pending",[I("tree-node-content",`
 background: var(--n-node-color-hover);
 `)]),V("selected",[I("tree-node-content","background: var(--n-node-color-active);")])]),V("selected",[I("tree-node-content","background: var(--n-node-color-active);")])])]),V("block-line",[I("tree-node",[ze("disabled",[se("&:hover","background: var(--n-node-color-hover);"),V("pending",`
 background: var(--n-node-color-hover);
 `),V("selectable",[ze("selected",[se("&:active","background: var(--n-node-color-pressed);")])]),V("selected","background: var(--n-node-color-active);")]),V("selected","background: var(--n-node-color-active);"),V("disabled",`
 cursor: not-allowed;
 `)])]),I("tree-node-indent",`
 flex-grow: 0;
 flex-shrink: 0;
 `,[V("show-line","position: relative",[se("&::before",`
 position: absolute;
 left: 50%;
 border-left: 1px solid var(--n-line-color);
 transition: border-color .3s var(--n-bezier);
 transform: translate(-50%);
 content: "";
 top: var(--n-line-offset-top);
 bottom: var(--n-line-offset-bottom);
 `),V("last-child",[se("&::before",`
 bottom: 50%;
 `)]),V("is-leaf",[se("&::after",`
 position: absolute;
 content: "";
 left: calc(50% + 0.5px);
 right: 0;
 bottom: 50%;
 transition: border-color .3s var(--n-bezier);
 border-bottom: 1px solid var(--n-line-color);
 `)])]),ze("show-line","height: 0;")]),I("tree-node-switcher",`
 cursor: pointer;
 display: inline-flex;
 flex-shrink: 0;
 height: var(--n-node-content-height);
 align-items: center;
 justify-content: center;
 transition: transform .15s var(--n-bezier);
 vertical-align: bottom;
 `,[xe("icon",`
 position: relative;
 height: 14px;
 width: 14px;
 display: flex;
 color: var(--n-arrow-color);
 transition: color .3s var(--n-bezier);
 font-size: 14px;
 `,[I("icon",[xt]),I("base-loading",`
 color: var(--n-loading-color);
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 `,[xt]),I("base-icon",[xt])]),V("hide","visibility: hidden;"),V("expanded","transform: rotate(90deg);")]),I("tree-node-checkbox",`
 display: inline-flex;
 height: var(--n-node-content-height);
 vertical-align: bottom;
 align-items: center;
 justify-content: center;
 `),I("tree-node-content",`
 user-select: none;
 position: relative;
 display: inline-flex;
 align-items: center;
 min-height: var(--n-node-content-height);
 box-sizing: border-box;
 line-height: var(--n-line-height);
 vertical-align: bottom;
 padding: 0 6px 0 4px;
 cursor: default;
 border-radius: var(--n-node-border-radius);
 color: var(--n-node-text-color);
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[se("&:last-child","margin-bottom: 0;"),xe("prefix",`
 display: inline-flex;
 margin-right: 8px;
 `),xe("text",`
 border-bottom: 1px solid #0000;
 transition: border-color .3s var(--n-bezier);
 flex-grow: 1;
 max-width: 100%;
 `),xe("suffix",`
 display: inline-flex;
 `)]),xe("empty","margin: auto;")]);var $n=function(e,l,r,c){function i(s){return s instanceof r?s:new r(function(p){p(s)})}return new(r||(r=Promise))(function(s,p){function y(x){try{k(c.next(x))}catch(U){p(U)}}function w(x){try{k(c.throw(x))}catch(U){p(U)}}function k(x){x.done?s(x.value):i(x.value).then(y,w)}k((c=c.apply(e,[])).next())})};function Ct(e,l,r,c){return{getIsGroup(){return!1},getKey(s){return s[e]},getChildren:c||(s=>s[l]),getDisabled(s){return!!(s[r]||s.checkboxDisabled)}}}const Wt={allowCheckingNotLoaded:Boolean,filter:Function,defaultExpandAll:Boolean,expandedKeys:Array,keyField:{type:String,default:"key"},labelField:{type:String,default:"label"},childrenField:{type:String,default:"children"},disabledField:{type:String,default:"disabled"},defaultExpandedKeys:{type:Array,default:()=>[]},indeterminateKeys:Array,renderSwitcherIcon:Function,onUpdateIndeterminateKeys:[Function,Array],"onUpdate:indeterminateKeys":[Function,Array],onUpdateExpandedKeys:[Function,Array],"onUpdate:expandedKeys":[Function,Array],overrideDefaultNodeClickBehavior:Function},Mn=Object.assign(Object.assign(Object.assign(Object.assign({},rt.props),{accordion:Boolean,showIrrelevantNodes:{type:Boolean,default:!0},data:{type:Array,default:()=>[]},expandOnDragenter:{type:Boolean,default:!0},expandOnClick:Boolean,checkOnClick:{type:[Boolean,Function],default:!1},cancelable:{type:Boolean,default:!0},checkable:Boolean,draggable:Boolean,blockNode:Boolean,blockLine:Boolean,showLine:Boolean,disabled:Boolean,checkedKeys:Array,defaultCheckedKeys:{type:Array,default:()=>[]},selectedKeys:Array,defaultSelectedKeys:{type:Array,default:()=>[]},multiple:Boolean,pattern:{type:String,default:""},onLoad:Function,cascade:Boolean,selectable:{type:Boolean,default:!0},scrollbarProps:Object,indent:{type:Number,default:24},allowDrop:{type:Function,default:Pn},animated:{type:Boolean,default:!0},checkboxPlacement:{type:String,default:"left"},virtualScroll:Boolean,watchProps:Array,renderLabel:Function,renderPrefix:Function,renderSuffix:Function,nodeProps:Function,keyboard:{type:Boolean,default:!0},getChildren:Function,onDragenter:[Function,Array],onDragleave:[Function,Array],onDragend:[Function,Array],onDragstart:[Function,Array],onDragover:[Function,Array],onDrop:[Function,Array],onUpdateCheckedKeys:[Function,Array],"onUpdate:checkedKeys":[Function,Array],onUpdateSelectedKeys:[Function,Array],"onUpdate:selectedKeys":[Function,Array]}),Wt),{internalTreeSelect:Boolean,internalScrollable:Boolean,internalScrollablePadding:String,internalRenderEmpty:Function,internalHighlightKeySet:Object,internalUnifySelectCheck:Boolean,internalCheckboxFocusable:{type:Boolean,default:!0},internalFocusable:{type:Boolean,default:!0},checkStrategy:{type:String,default:"all"},leafOnly:Boolean}),jn=Fe({name:"Tree",props:Mn,slots:Object,setup(e){const{mergedClsPrefixRef:l,inlineThemeDisabled:r,mergedRtlRef:c}=$t(e),i=dn("Tree",c,l),s=rt("Tree","-tree",Un,sn,e,l),p=L(null),y=L(null),w=L(null);function k(){var t;return(t=w.value)===null||t===void 0?void 0:t.listElRef}function x(){var t;return(t=w.value)===null||t===void 0?void 0:t.itemsElRef}const U=O(()=>{const{filter:t}=e;if(t)return t;const{labelField:a}=e;return(o,f)=>{if(!o.length)return!0;const h=f[a];return typeof h=="string"?h.toLowerCase().includes(o.toLowerCase()):!1}}),C=O(()=>{const{pattern:t}=e;return t?!t.length||!U.value?{filteredTree:e.data,highlightKeySet:null,expandedKeys:void 0}:In(e.data,U.value,t,e.keyField,e.childrenField):{filteredTree:e.data,highlightKeySet:null,expandedKeys:void 0}}),_=O(()=>pt(e.showIrrelevantNodes?e.data:C.value.filteredTree,Ct(e.keyField,e.childrenField,e.disabledField,e.getChildren))),T=He(Nt,null),K=e.internalTreeSelect?T.dataTreeMate:O(()=>e.showIrrelevantNodes?_.value:pt(e.data,Ct(e.keyField,e.childrenField,e.disabledField,e.getChildren))),{watchProps:b}=e,v=L([]);b!=null&&b.includes("defaultCheckedKeys")?Ze(()=>{v.value=e.defaultCheckedKeys}):v.value=e.defaultCheckedKeys;const N=D(e,"checkedKeys"),P=Le(N,v),H=O(()=>K.value.getCheckedKeys(P.value,{cascade:e.cascade,allowNotLoaded:e.allowCheckingNotLoaded})),q=Vt(e),X=O(()=>H.value.checkedKeys),we=O(()=>{const{indeterminateKeys:t}=e;return t!==void 0?t:H.value.indeterminateKeys}),Q=L([]);b!=null&&b.includes("defaultSelectedKeys")?Ze(()=>{Q.value=e.defaultSelectedKeys}):Q.value=e.defaultSelectedKeys;const fe=D(e,"selectedKeys"),he=Le(fe,Q),ge=L([]),pe=t=>{ge.value=e.defaultExpandAll?K.value.getNonLeafKeys():t===void 0?e.defaultExpandedKeys:t};b!=null&&b.includes("defaultExpandedKeys")?Ze(()=>{pe(void 0)}):Ze(()=>{pe(e.defaultExpandedKeys)});const qe=D(e,"expandedKeys"),ee=Le(qe,ge),Y=O(()=>_.value.getFlattenedNodes(ee.value)),{pendingNodeKeyRef:ve,handleKeydown:Ee}=On({props:e,mergedCheckedKeysRef:P,mergedSelectedKeysRef:he,fNodesRef:Y,mergedExpandedKeysRef:ee,handleCheck:d,handleSelect:F,handleSwitcherClick:m});let ne=null,ye=null;const De=L(new Set),Se=O(()=>e.internalHighlightKeySet||C.value.highlightKeySet),Be=Le(Se,De),u=L(new Set),R=O(()=>ee.value.filter(t=>!u.value.has(t)));let B=0;const M=L(null),W=L(null),Pe=L(null),le=L(null),Re=L(0),dt=O(()=>{const{value:t}=W;return t?t.parent:null});let We=!1;ot(D(e,"data"),()=>{We=!0,it(()=>{We=!1}),u.value.clear(),ve.value=null,Ne()},{deep:!1});let Ge=!1;const ce=()=>{Ge=!0,it(()=>{Ge=!1})};let Oe;ot(D(e,"pattern"),(t,a)=>{if(e.showIrrelevantNodes)if(Oe=void 0,t){const{expandedKeys:o,highlightKeySet:f}=Bn(e.data,e.pattern,e.keyField,e.childrenField,U.value);De.value=f,ce(),me(o,Z(o),{node:null,action:"filter"})}else De.value=new Set;else if(!t.length)Oe!==void 0&&(ce(),me(Oe,Z(Oe),{node:null,action:"filter"}));else{a.length||(Oe=ee.value);const{expandedKeys:o}=C.value;o!==void 0&&(ce(),me(o,Z(o),{node:null,action:"filter"}))}});function et(t){return $n(this,void 0,void 0,function*(){const{onLoad:a}=e;if(!a){yield Promise.resolve();return}const{value:o}=u;if(!o.has(t.key)){o.add(t.key);try{(yield a(t.rawNode))===!1&&n()}catch(f){console.error(f),n()}o.delete(t.key)}})}Ze(()=>{var t;const{value:a}=_;if(!a)return;const{getNode:o}=a;(t=ee.value)===null||t===void 0||t.forEach(f=>{const h=o(f);h&&!h.shallowLoaded&&et(h)})});const Ce=L(!1),be=L([]);ot(R,(t,a)=>{if(!e.animated||Ge){it(Ie);return}if(We)return;const o=Qe(s.value.self.nodeHeight),f=new Set(a);let h=null,$=null;for(const E of t)if(!f.has(E)){if(h!==null)return;h=E}const te=new Set(t);for(const E of a)if(!te.has(E)){if($!==null)return;$=E}if(h===null&&$===null)return;const{virtualScroll:oe}=e,$e=(oe?w.value.listElRef:p.value).offsetHeight,Me=Math.ceil($e/o)+1;let re;if(h!==null&&(re=a),$!==null&&(re===void 0?re=t:re=re.filter(E=>E!==$)),Ce.value=!0,be.value=_.value.getFlattenedNodes(re),h!==null){const E=be.value.findIndex(de=>de.key===h);if(~E){const de=be.value[E].children;if(de){const ue=Lt(de,t);be.value.splice(E+1,0,{__motion:!0,mode:"expand",height:oe?ue.length*o:void 0,nodes:oe?ue.slice(0,Me):ue})}}}if($!==null){const E=be.value.findIndex(de=>de.key===$);if(~E){const de=be.value[E].children;if(!de)return;Ce.value=!0;const ue=Lt(de,t);be.value.splice(E+1,0,{__motion:!0,mode:"collapse",height:oe?ue.length*o:void 0,nodes:oe?ue.slice(0,Me):ue})}}});const st=O(()=>Sn(Y.value)),ct=O(()=>Ce.value?be.value:Y.value);function Ie(){const{value:t}=y;t&&t.sync()}function ut(){Ce.value=!1,e.virtualScroll&&it(Ie)}function Z(t){const{getNode:a}=K.value;return t.map(o=>{var f;return((f=a(o))===null||f===void 0?void 0:f.rawNode)||null})}function me(t,a,o){const{"onUpdate:expandedKeys":f,onUpdateExpandedKeys:h}=e;ge.value=t,f&&j(f,t,a,o),h&&j(h,t,a,o)}function tt(t,a,o){const{"onUpdate:checkedKeys":f,onUpdateCheckedKeys:h}=e;v.value=t,h&&j(h,t,a,o),f&&j(f,t,a,o)}function ft(t,a){const{"onUpdate:indeterminateKeys":o,onUpdateIndeterminateKeys:f}=e;o&&j(o,t,a),f&&j(f,t,a)}function Xe(t,a,o){const{"onUpdate:selectedKeys":f,onUpdateSelectedKeys:h}=e;Q.value=t,h&&j(h,t,a,o),f&&j(f,t,a,o)}function ht(t){const{onDragenter:a}=e;a&&j(a,t)}function nt(t){const{onDragleave:a}=e;a&&j(a,t)}function gt(t){const{onDragend:a}=e;a&&j(a,t)}function lt(t){const{onDragstart:a}=e;a&&j(a,t)}function vt(t){const{onDragover:a}=e;a&&j(a,t)}function Ye(t){const{onDrop:a}=e;a&&j(a,t)}function Ne(){Ke(),ke()}function Ke(){M.value=null}function ke(){Re.value=0,W.value=null,Pe.value=null,le.value=null,n()}function n(){ne&&(window.clearTimeout(ne),ne=null),ye=null}function d(t,a){if(e.disabled||Te(t,e.disabledField))return;if(e.internalUnifySelectCheck&&!e.multiple){F(t);return}const o=a?"check":"uncheck",{checkedKeys:f,indeterminateKeys:h}=K.value[o](t.key,X.value,{cascade:e.cascade,checkStrategy:q.value,allowNotLoaded:e.allowCheckingNotLoaded});tt(f,Z(f),{node:t.rawNode,action:o}),ft(h,Z(h))}function S(t){if(e.disabled)return;const{key:a}=t,{value:o}=ee,f=o.findIndex(h=>h===a);if(~f){const h=Array.from(o);h.splice(f,1),me(h,Z(h),{node:t.rawNode,action:"collapse"})}else{const h=_.value.getNode(a);if(!h||h.isLeaf)return;let $;if(e.accordion){const te=new Set(t.siblings.map(({key:oe})=>oe));$=o.filter(oe=>!te.has(oe)),$.push(a)}else $=o.concat(a);me($,Z($),{node:t.rawNode,action:"expand"})}}function m(t){e.disabled||Ce.value||S(t)}function F(t){if(!(e.disabled||!e.selectable)){if(ve.value=t.key,e.internalUnifySelectCheck){const{value:{checkedKeys:a,indeterminateKeys:o}}=H;e.multiple?d(t,!(a.includes(t.key)||o.includes(t.key))):tt([t.key],Z([t.key]),{node:t.rawNode,action:"check"})}if(e.multiple){const a=Array.from(he.value),o=a.findIndex(f=>f===t.key);~o?e.cancelable&&a.splice(o,1):~o||a.push(t.key),Xe(a,Z(a),{node:t.rawNode,action:~o?"unselect":"select"})}else he.value.includes(t.key)?e.cancelable&&Xe([],[],{node:t.rawNode,action:"unselect"}):Xe([t.key],Z([t.key]),{node:t.rawNode,action:"select"})}}function z(t){if(ne&&(window.clearTimeout(ne),ne=null),t.isLeaf)return;ye=t.key;const a=()=>{if(ye!==t.key)return;const{value:o}=Pe;if(o&&o.key===t.key&&!ee.value.includes(t.key)){const f=ee.value.concat(t.key);me(f,Z(f),{node:t.rawNode,action:"expand"})}ne=null,ye=null};t.shallowLoaded?ne=window.setTimeout(()=>{a()},1e3):ne=window.setTimeout(()=>{et(t).then(()=>{a()})},1e3)}function ie({event:t,node:a}){!e.draggable||e.disabled||Te(a,e.disabledField)||(Kt({event:t,node:a},!1),ht({event:t,node:a.rawNode}))}function ae({event:t,node:a}){!e.draggable||e.disabled||Te(a,e.disabledField)||nt({event:t,node:a.rawNode})}function _e(t){t.target===t.currentTarget&&ke()}function Ae({event:t,node:a}){Ne(),!(!e.draggable||e.disabled||Te(a,e.disabledField))&&gt({event:t,node:a.rawNode})}function yt({event:t,node:a}){!e.draggable||e.disabled||Te(a,e.disabledField)||(B=t.clientX,M.value=a,lt({event:t,node:a.rawNode}))}function Kt({event:t,node:a},o=!0){var f;if(!e.draggable||e.disabled||Te(a,e.disabledField))return;const{value:h}=M;if(!h)return;const{allowDrop:$,indent:te}=e;o&&vt({event:t,node:a.rawNode});const oe=t.currentTarget,{height:$e,top:Me}=oe.getBoundingClientRect(),re=t.clientY-Me;let E;$({node:a.rawNode,dropPosition:"inside",phase:"drag"})?re<=8?E="before":re>=$e-8?E="after":E="inside":re<=$e/2?E="before":E="after";const{value:ue}=st;let A,G;const je=ue(a.key);if(je===null){ke();return}let at=!1;E==="inside"?(A=a,G="inside"):E==="before"?a.isFirstChild?(A=a,G="before"):(A=Y.value[je-1],G="after"):(A=a,G="after"),!A.isLeaf&&ee.value.includes(A.key)&&(at=!0,G==="after"&&(A=Y.value[je+1],A?G="before":(A=a,G="inside")));const Ft=A;if(Pe.value=Ft,!at&&h.isLastChild&&h.key===A.key&&(G="after"),G==="after"){let Dt=B-t.clientX,bt=0;for(;Dt>=te/2&&A.parent!==null&&A.isLastChild&&bt<1;)Dt-=te,bt+=1,A=A.parent;Re.value=bt}else Re.value=0;if((h.contains(A)||G==="inside"&&((f=h.parent)===null||f===void 0?void 0:f.key)===A.key)&&!(h.key===Ft.key&&h.key===A.key)){ke();return}if(!$({node:A.rawNode,dropPosition:G,phase:"drag"})){ke();return}if(h.key===A.key)n();else if(ye!==A.key)if(G==="inside"){if(e.expandOnDragenter){if(z(A),!A.shallowLoaded&&ye!==A.key){Ne();return}}else if(!A.shallowLoaded){Ne();return}}else n();else G!=="inside"&&n();le.value=G,W.value=A}function Gt({event:t,node:a,dropPosition:o}){if(!e.draggable||e.disabled||Te(a,e.disabledField))return;const{value:f}=M,{value:h}=W,{value:$}=le;if(!(!f||!h||!$)&&e.allowDrop({node:h.rawNode,dropPosition:$,phase:"drag"})&&f.key!==h.key){if($==="before"){const te=f.getNext({includeDisabled:!0});if(te&&te.key===h.key){ke();return}}if($==="after"){const te=f.getPrev({includeDisabled:!0});if(te&&te.key===h.key){ke();return}}Ye({event:t,node:h.rawNode,dragNode:f.rawNode,dropPosition:o}),Ne()}}function Xt(){Ie()}function Yt(){Ie()}function Qt(t){var a;if(e.virtualScroll||e.internalScrollable){const{value:o}=y;if(!((a=o==null?void 0:o.containerRef)===null||a===void 0)&&a.contains(t.relatedTarget))return;ve.value=null}else{const{value:o}=p;if(o!=null&&o.contains(t.relatedTarget))return;ve.value=null}}ot(ve,t=>{var a,o;if(t!==null){if(e.virtualScroll)(a=w.value)===null||a===void 0||a.scrollTo({key:t});else if(e.internalScrollable){const{value:f}=y;if(f===null)return;const h=(o=f.contentRef)===null||o===void 0?void 0:o.querySelector(`[data-key="${zt(t)}"]`);if(!h)return;f.scrollTo({el:h})}}}),jt(Je,{loadingKeysRef:u,highlightKeySetRef:Be,displayedCheckedKeysRef:X,displayedIndeterminateKeysRef:we,mergedSelectedKeysRef:he,mergedExpandedKeysRef:ee,mergedThemeRef:s,mergedCheckStrategyRef:q,nodePropsRef:D(e,"nodeProps"),disabledRef:D(e,"disabled"),checkableRef:D(e,"checkable"),selectableRef:D(e,"selectable"),expandOnClickRef:D(e,"expandOnClick"),onLoadRef:D(e,"onLoad"),draggableRef:D(e,"draggable"),blockLineRef:D(e,"blockLine"),indentRef:D(e,"indent"),cascadeRef:D(e,"cascade"),checkOnClickRef:D(e,"checkOnClick"),checkboxPlacementRef:e.checkboxPlacement,droppingMouseNodeRef:Pe,droppingNodeParentRef:dt,draggingNodeRef:M,droppingPositionRef:le,droppingOffsetLevelRef:Re,fNodesRef:Y,pendingNodeKeyRef:ve,showLineRef:D(e,"showLine"),disabledFieldRef:D(e,"disabledField"),internalScrollableRef:D(e,"internalScrollable"),internalCheckboxFocusableRef:D(e,"internalCheckboxFocusable"),internalTreeSelect:e.internalTreeSelect,renderLabelRef:D(e,"renderLabel"),renderPrefixRef:D(e,"renderPrefix"),renderSuffixRef:D(e,"renderSuffix"),renderSwitcherIconRef:D(e,"renderSwitcherIcon"),labelFieldRef:D(e,"labelField"),multipleRef:D(e,"multiple"),overrideDefaultNodeClickBehaviorRef:D(e,"overrideDefaultNodeClickBehavior"),handleSwitcherClick:m,handleDragEnd:Ae,handleDragEnter:ie,handleDragLeave:ae,handleDragStart:yt,handleDrop:Gt,handleDragOver:Kt,handleSelect:F,handleCheck:d});function Zt(t,a){var o,f;typeof t=="number"?(o=w.value)===null||o===void 0||o.scrollTo(t,a||0):(f=w.value)===null||f===void 0||f.scrollTo(t)}const Jt={handleKeydown:Ee,scrollTo:Zt,getCheckedData:()=>{if(!e.checkable)return{keys:[],options:[]};const{checkedKeys:t}=H.value;return{keys:t,options:Z(t)}},getIndeterminateData:()=>{if(!e.checkable)return{keys:[],options:[]};const{indeterminateKeys:t}=H.value;return{keys:t,options:Z(t)}}},Tt=O(()=>{const{common:{cubicBezierEaseInOut:t},self:{fontSize:a,nodeBorderRadius:o,nodeColorHover:f,nodeColorPressed:h,nodeColorActive:$,arrowColor:te,loadingColor:oe,nodeTextColor:$e,nodeTextColorDisabled:Me,dropMarkColor:re,nodeWrapperPadding:E,nodeHeight:de,lineHeight:ue,lineColor:A}}=s.value,G=kt(E,"top"),je=kt(E,"bottom"),at=Ut(Qe(de)-Qe(G)-Qe(je));return{"--n-arrow-color":te,"--n-loading-color":oe,"--n-bezier":t,"--n-font-size":a,"--n-node-border-radius":o,"--n-node-color-active":$,"--n-node-color-hover":f,"--n-node-color-pressed":h,"--n-node-text-color":$e,"--n-node-text-color-disabled":Me,"--n-drop-mark-color":re,"--n-node-wrapper-padding":E,"--n-line-offset-top":`-${G}`,"--n-line-offset-bottom":`-${je}`,"--n-node-content-height":at,"--n-line-height":ue,"--n-line-color":A}}),Ue=r?Mt("tree",void 0,Tt,e):void 0;return Object.assign(Object.assign({},Jt),{mergedClsPrefix:l,mergedTheme:s,rtlEnabled:i,fNodes:ct,aip:Ce,selfElRef:p,virtualListInstRef:w,scrollbarInstRef:y,handleFocusout:Qt,handleDragLeaveTree:_e,handleScroll:Xt,getScrollContainer:k,getScrollContent:x,handleAfterEnter:ut,handleResize:Yt,cssVars:r?void 0:Tt,themeClass:Ue==null?void 0:Ue.themeClass,onRender:Ue==null?void 0:Ue.onRender})},render(){var e;const{fNodes:l,internalRenderEmpty:r}=this;if(!l.length&&r)return r();const{mergedClsPrefix:c,blockNode:i,blockLine:s,draggable:p,disabled:y,internalFocusable:w,checkable:k,handleKeydown:x,rtlEnabled:U,handleFocusout:C,scrollbarProps:_}=this,T=w&&!y,K=T?"0":void 0,b=[`${c}-tree`,U&&`${c}-tree--rtl`,k&&`${c}-tree--checkable`,(s||i)&&`${c}-tree--block-node`,s&&`${c}-tree--block-line`],v=P=>"__motion"in P?g(_n,{height:P.height,nodes:P.nodes,clsPrefix:c,mode:P.mode,onAfterEnter:this.handleAfterEnter}):g(qt,{key:P.key,tmNode:P,clsPrefix:c});if(this.virtualScroll){const{mergedTheme:P,internalScrollablePadding:H}=this,q=kt(H||"0");return g(Ot,Object.assign({},_,{ref:"scrollbarInstRef",onDragleave:p?this.handleDragLeaveTree:void 0,container:this.getScrollContainer,content:this.getScrollContent,class:b,theme:P.peers.Scrollbar,themeOverrides:P.peerOverrides.Scrollbar,tabindex:K,onKeydown:T?x:void 0,onFocusout:T?C:void 0}),{default:()=>{var X;return(X=this.onRender)===null||X===void 0||X.call(this),l.length?g(pn,{ref:"virtualListInstRef",items:this.fNodes,itemSize:Qe(P.self.nodeHeight),ignoreItemResize:this.aip,paddingTop:q.top,paddingBottom:q.bottom,class:this.themeClass,style:[this.cssVars,{paddingLeft:q.left,paddingRight:q.right}],onScroll:this.handleScroll,onResize:this.handleResize,showScrollbar:!1,itemResizable:!0},{default:({item:we})=>v(we)}):wt(this.$slots.empty,()=>[g(St,{class:`${c}-tree__empty`,theme:this.mergedTheme.peers.Empty,themeOverrides:this.mergedTheme.peerOverrides.Empty})])}})}const{internalScrollable:N}=this;return b.push(this.themeClass),(e=this.onRender)===null||e===void 0||e.call(this),N?g(Ot,Object.assign({},_,{class:b,tabindex:K,onKeydown:T?x:void 0,onFocusout:T?C:void 0,style:this.cssVars,contentStyle:{padding:this.internalScrollablePadding}}),{default:()=>g("div",{onDragleave:p?this.handleDragLeaveTree:void 0,ref:"selfElRef"},this.fNodes.map(v))}):g("div",{class:b,tabindex:K,ref:"selfElRef",style:this.cssVars,onKeydown:T?x:void 0,onFocusout:T?C:void 0,onDragleave:p?this.handleDragLeaveTree:void 0},l.length?l.map(v):wt(this.$slots.empty,()=>[g(St,{class:`${c}-tree__empty`,theme:this.mergedTheme.peers.Empty,themeOverrides:this.mergedTheme.peerOverrides.Empty})]))}}),zn=se([I("tree-select",`
 z-index: auto;
 outline: none;
 width: 100%;
 position: relative;
 `),I("tree-select-menu",`
 position: relative;
 overflow: hidden;
 margin: 4px 0;
 transition: box-shadow .3s var(--n-bezier), background-color .3s var(--n-bezier);
 border-radius: var(--n-menu-border-radius);
 box-shadow: var(--n-menu-box-shadow);
 background-color: var(--n-menu-color);
 outline: none;
 `,[I("tree","max-height: var(--n-menu-height);"),xe("empty",`
 display: flex;
 padding: 12px 32px;
 flex: 1;
 justify-content: center;
 `),xe("header",`
 padding: var(--n-header-padding);
 transition: 
 color .3s var(--n-bezier);
 border-color .3s var(--n-bezier);
 border-bottom: 1px solid var(--n-header-divider-color);
 color: var(--n-header-text-color);
 `),xe("action",`
 padding: var(--n-action-padding);
 transition: 
 color .3s var(--n-bezier);
 border-color .3s var(--n-bezier);
 border-top: 1px solid var(--n-action-divider-color);
 color: var(--n-action-text-color);
 `),cn()])]);function Bt(e,l){const{rawNode:r}=e;return Object.assign(Object.assign({},r),{label:r[l],value:e.key})}function It(e,l,r,c){const{rawNode:i}=e;return Object.assign(Object.assign({},i),{value:e.key,label:l.map(s=>s.rawNode[c]).join(r)})}const Vn=Object.assign(Object.assign(Object.assign(Object.assign({},rt.props),{bordered:{type:Boolean,default:!0},cascade:Boolean,checkable:Boolean,clearable:Boolean,clearFilterAfterSelect:{type:Boolean,default:!0},consistentMenuWidth:{type:Boolean,default:!0},defaultShow:Boolean,defaultValue:{type:[String,Number,Array],default:null},disabled:{type:Boolean,default:void 0},filterable:Boolean,checkStrategy:{type:String,default:"all"},loading:Boolean,maxTagCount:[String,Number],multiple:Boolean,showPath:Boolean,separator:{type:String,default:" / "},options:{type:Array,default:()=>[]},placeholder:String,placement:{type:String,default:"bottom-start"},show:{type:Boolean,default:void 0},size:String,value:[String,Number,Array],to:Rt.propTo,menuProps:Object,virtualScroll:{type:Boolean,default:!0},status:String,renderTag:Function,ellipsisTagPopoverProps:Object}),Wt),{renderLabel:Function,renderPrefix:Function,renderSuffix:Function,nodeProps:Function,watchProps:Array,getChildren:Function,onBlur:Function,onFocus:Function,onLoad:Function,onUpdateShow:[Function,Array],onUpdateValue:[Function,Array],"onUpdate:value":[Function,Array],"onUpdate:show":[Function,Array],leafOnly:Boolean}),ll=Fe({name:"TreeSelect",props:Vn,slots:Object,setup(e){const l=L(null),r=L(null),c=L(null),i=L(null),{mergedClsPrefixRef:s,namespaceRef:p,inlineThemeDisabled:y}=$t(e),{localeRef:w}=Fn("Select"),{mergedSizeRef:k,mergedDisabledRef:x,mergedStatusRef:U,nTriggerFormBlur:C,nTriggerFormChange:_,nTriggerFormFocus:T,nTriggerFormInput:K}=hn(e),b=L(e.defaultValue),v=D(e,"value"),N=Le(v,b),P=L(e.defaultShow),H=D(e,"show"),q=Le(H,P),X=L(""),we=O(()=>{const{filter:n}=e;if(n)return n;const{labelField:d}=e;return(S,m)=>S.length?m[d].toLowerCase().includes(S.toLowerCase()):!0}),Q=O(()=>pt(e.options,Ct(e.keyField,e.childrenField,e.disabledField,void 0))),{value:fe}=N,he=L(e.checkable?null:Array.isArray(fe)&&fe.length?fe[fe.length-1]:null),ge=O(()=>e.multiple&&e.cascade&&e.checkable),pe=L(e.defaultExpandAll?void 0:e.defaultExpandedKeys||e.expandedKeys),qe=D(e,"expandedKeys"),ee=Le(qe,pe),Y=L(!1),ve=O(()=>{const{placeholder:n}=e;return n!==void 0?n:w.value.placeholder}),Ee=O(()=>{const{value:n}=N;return e.multiple?Array.isArray(n)?n:[]:n===null||Array.isArray(n)?[]:[n]}),ne=O(()=>e.checkable?[]:Ee.value),ye=O(()=>{const{multiple:n,showPath:d,separator:S,labelField:m}=e;if(n)return null;const{value:F}=N;if(!Array.isArray(F)&&F!==null){const{value:z}=Q,ie=z.getNode(F);if(ie!==null)return d?It(ie,z.getPath(F).treeNodePath,S,m):Bt(ie,m)}return null}),De=O(()=>{const{multiple:n,showPath:d,separator:S}=e;if(!n)return null;const{value:m}=N;if(Array.isArray(m)){const F=[],{value:z}=Q,{checkedKeys:ie}=z.getCheckedKeys(m,{checkStrategy:e.checkStrategy,cascade:ge.value,allowNotLoaded:e.allowCheckingNotLoaded}),{labelField:ae}=e;return ie.forEach(_e=>{const Ae=z.getNode(_e);Ae!==null&&F.push(d?It(Ae,z.getPath(_e).treeNodePath,S,ae):Bt(Ae,ae))}),F}return[]});function Se(){var n;(n=r.value)===null||n===void 0||n.focus()}function Be(){var n;(n=r.value)===null||n===void 0||n.focusInput()}function u(n){const{onUpdateShow:d,"onUpdate:show":S}=e;d&&j(d,n),S&&j(S,n),P.value=n}function R(n,d,S){const{onUpdateValue:m,"onUpdate:value":F}=e;m&&j(m,n,d,S),F&&j(F,n,d,S),b.value=n,K(),_()}function B(n,d){const{onUpdateIndeterminateKeys:S,"onUpdate:indeterminateKeys":m}=e;S&&j(S,n,d),m&&j(m,n,d)}function M(n,d,S){const{onUpdateExpandedKeys:m,"onUpdate:expandedKeys":F}=e;m&&j(m,n,d,S),F&&j(F,n,d,S),pe.value=n}function W(n){const{onFocus:d}=e;d&&d(n),T()}function Pe(n){le();const{onBlur:d}=e;d&&d(n),C()}function le(){u(!1)}function Re(){x.value||(X.value="",u(!0),e.filterable&&Be())}function dt(){X.value=""}function We(n){var d;q.value&&(!((d=r.value)===null||d===void 0)&&d.$el.contains(vn(n))||le())}function Ge(){x.value||(q.value?e.filterable||le():Re())}function ce(n){const{value:{getNode:d}}=Q;return n.map(S=>{var m;return((m=d(S))===null||m===void 0?void 0:m.rawNode)||null})}function Oe(n,d,S){const m=ce(n),F=S.action==="check"?"select":"unselect",z=S.node;e.multiple?(R(n,m,{node:z,action:F}),e.filterable&&(Be(),e.clearFilterAfterSelect&&(X.value=""))):(n.length?R(n[0],m[0]||null,{node:z,action:F}):R(null,null,{node:z,action:F}),le(),Se())}function et(n){e.checkable&&B(n,ce(n))}function Ce(n){var d;!((d=i.value)===null||d===void 0)&&d.contains(n.relatedTarget)||(Y.value=!0,W(n))}function be(n){var d;!((d=i.value)===null||d===void 0)&&d.contains(n.relatedTarget)||(Y.value=!1,Pe(n))}function st(n){var d,S,m;!((d=i.value)===null||d===void 0)&&d.contains(n.relatedTarget)||!((m=(S=r.value)===null||S===void 0?void 0:S.$el)===null||m===void 0)&&m.contains(n.relatedTarget)||(Y.value=!0,W(n))}function ct(n){var d,S,m;!((d=i.value)===null||d===void 0)&&d.contains(n.relatedTarget)||!((m=(S=r.value)===null||S===void 0?void 0:S.$el)===null||m===void 0)&&m.contains(n.relatedTarget)||(Y.value=!1,Pe(n))}function Ie(n){n.stopPropagation();const{multiple:d}=e;!d&&e.filterable&&le(),d?R([],[],{node:null,action:"clear"}):R(null,null,{node:null,action:"clear"})}function ut(n){const{value:d}=N;if(Array.isArray(d)){const{value:S}=Q,{checkedKeys:m}=S.getCheckedKeys(d,{cascade:ge.value,allowNotLoaded:e.allowCheckingNotLoaded}),F=m.findIndex(z=>z===n.value);if(~F){const z=m[F],ie=ce([z])[0];if(e.checkable){const{checkedKeys:ae}=S.uncheck(n.value,m,{checkStrategy:e.checkStrategy,cascade:ge.value,allowNotLoaded:e.allowCheckingNotLoaded});R(ae,ce(ae),{node:ie,action:"delete"})}else{const ae=Array.from(m);ae.splice(F,1),R(ae,ce(ae),{node:ie,action:"delete"})}}}}function Z(n){const{value:d}=n.target;X.value=d}function me(n){const{value:d}=c;return d?d.handleKeydown(n):{enterBehavior:null}}function tt(n){if(n.key==="Enter"){if(q.value){const{enterBehavior:d}=me(n);if(!e.multiple)switch(d){case"default":case"toggleSelect":le(),Se();break}}else Re();n.preventDefault()}else n.key==="Escape"?q.value&&(yn(n),le(),Se()):q.value?me(n):n.key==="ArrowDown"&&Re()}function ft(){le(),Se()}function Xe(n){!Ve(n,"action")&&!Ve(n,"header")&&n.preventDefault()}const ht=O(()=>{const{renderTag:n}=e;if(n)return function({option:S,handleClose:m}){const{value:F}=S;if(F!==void 0){const z=Q.value.getNode(F);if(z)return n({option:z.rawNode,handleClose:m})}return F}});jt(Nt,{pendingNodeKeyRef:he,dataTreeMate:Q});function nt(){var n;q.value&&((n=l.value)===null||n===void 0||n.syncPosition())}Cn(i,nt);const gt=Vt(e),lt=O(()=>{if(e.checkable){const n=N.value;return e.multiple&&Array.isArray(n)?Q.value.getCheckedKeys(n,{cascade:e.cascade,checkStrategy:gt.value,allowNotLoaded:e.allowCheckingNotLoaded}):{checkedKeys:Array.isArray(n)||n===null?[]:[n],indeterminateKeys:[]}}return{checkedKeys:[],indeterminateKeys:[]}}),vt={getCheckedData:()=>{const{checkedKeys:n}=lt.value;return{keys:n,options:ce(n)}},getIndeterminateData:()=>{const{indeterminateKeys:n}=lt.value;return{keys:n,options:ce(n)}},focus:()=>{var n;return(n=r.value)===null||n===void 0?void 0:n.focus()},focusInput:()=>{var n;return(n=r.value)===null||n===void 0?void 0:n.focusInput()},blur:()=>{var n;return(n=r.value)===null||n===void 0?void 0:n.blur()},blurInput:()=>{var n;return(n=r.value)===null||n===void 0?void 0:n.blurInput()}},Ye=rt("TreeSelect","-tree-select",zn,bn,e,s),Ne=O(()=>{const{common:{cubicBezierEaseInOut:n},self:{menuBoxShadow:d,menuBorderRadius:S,menuColor:m,menuHeight:F,actionPadding:z,actionDividerColor:ie,actionTextColor:ae,headerDividerColor:_e,headerPadding:Ae,headerTextColor:yt}}=Ye.value;return{"--n-menu-box-shadow":d,"--n-menu-border-radius":S,"--n-menu-color":m,"--n-menu-height":F,"--n-bezier":n,"--n-action-padding":z,"--n-action-text-color":ae,"--n-action-divider-color":ie,"--n-header-padding":Ae,"--n-header-text-color":yt,"--n-header-divider-color":_e}}),Ke=y?Mt("tree-select",void 0,Ne,e):void 0,ke=O(()=>{const{self:{menuPadding:n}}=Ye.value;return n});return Object.assign(Object.assign({},vt),{menuElRef:i,mergedStatus:U,triggerInstRef:r,followerInstRef:l,treeInstRef:c,mergedClsPrefix:s,mergedValue:N,mergedShow:q,namespace:p,adjustedTo:Rt(e),isMounted:gn(),focused:Y,menuPadding:ke,mergedPlaceholder:ve,mergedExpandedKeys:ee,treeSelectedKeys:ne,treeCheckedKeys:Ee,mergedSize:k,mergedDisabled:x,selectedOption:ye,selectedOptions:De,pattern:X,pendingNodeKey:he,mergedCascade:ge,mergedFilter:we,selectionRenderTag:ht,handleTriggerOrMenuResize:nt,doUpdateExpandedKeys:M,handleMenuLeave:dt,handleTriggerClick:Ge,handleMenuClickoutside:We,handleUpdateCheckedKeys:Oe,handleUpdateIndeterminateKeys:et,handleTriggerFocus:Ce,handleTriggerBlur:be,handleMenuFocusin:st,handleMenuFocusout:ct,handleClear:Ie,handleDeleteOption:ut,handlePatternInput:Z,handleKeydown:tt,handleTabOut:ft,handleMenuMousedown:Xe,mergedTheme:Ye,cssVars:y?void 0:Ne,themeClass:Ke==null?void 0:Ke.themeClass,onRender:Ke==null?void 0:Ke.onRender})},render(){const{mergedTheme:e,mergedClsPrefix:l,$slots:r}=this;return g("div",{class:`${l}-tree-select`},g(Nn,null,{default:()=>[g(Kn,null,{default:()=>g(Rn,{ref:"triggerInstRef",onResize:this.handleTriggerOrMenuResize,status:this.mergedStatus,focused:this.focused,clsPrefix:l,theme:e.peers.InternalSelection,themeOverrides:e.peerOverrides.InternalSelection,ellipsisTagPopoverProps:this.ellipsisTagPopoverProps,renderTag:this.selectionRenderTag,selectedOption:this.selectedOption,selectedOptions:this.selectedOptions,size:this.mergedSize,bordered:this.bordered,placeholder:this.mergedPlaceholder,disabled:this.mergedDisabled,active:this.mergedShow,loading:this.loading,multiple:this.multiple,maxTagCount:this.maxTagCount,showArrow:!0,filterable:this.filterable,clearable:this.clearable,pattern:this.pattern,onPatternInput:this.handlePatternInput,onClear:this.handleClear,onClick:this.handleTriggerClick,onFocus:this.handleTriggerFocus,onBlur:this.handleTriggerBlur,onDeleteOption:this.handleDeleteOption,onKeydown:this.handleKeydown},{arrow:()=>{var c,i;return[(i=(c=this.$slots).arrow)===null||i===void 0?void 0:i.call(c)]}})}),g(Tn,{ref:"followerInstRef",show:this.mergedShow,placement:this.placement,to:this.adjustedTo,teleportDisabled:this.adjustedTo===Rt.tdkey,containerClass:this.namespace,width:this.consistentMenuWidth?"target":void 0,minWidth:"target"},{default:()=>g(un,{name:"fade-in-scale-up-transition",appear:this.isMounted,onLeave:this.handleMenuLeave},{default:()=>{var c;if(!this.mergedShow)return null;const{mergedClsPrefix:i,checkable:s,multiple:p,menuProps:y,options:w}=this;return(c=this.onRender)===null||c===void 0||c.call(this),kn(g("div",Object.assign({},y,{class:[`${i}-tree-select-menu`,y==null?void 0:y.class,this.themeClass],ref:"menuElRef",style:[(y==null?void 0:y.style)||"",this.cssVars],tabindex:0,onMousedown:this.handleMenuMousedown,onKeydown:this.handleKeydown,onFocusin:this.handleMenuFocusin,onFocusout:this.handleMenuFocusout}),At(r.header,k=>k?g("div",{class:`${i}-tree-select-menu__header`,"data-header":!0},k):null),g(jn,{ref:"treeInstRef",blockLine:!0,allowCheckingNotLoaded:this.allowCheckingNotLoaded,showIrrelevantNodes:!1,animated:!1,pattern:this.pattern,getChildren:this.getChildren,filter:this.mergedFilter,data:w,cancelable:p,labelField:this.labelField,keyField:this.keyField,disabledField:this.disabledField,childrenField:this.childrenField,theme:e.peers.Tree,themeOverrides:e.peerOverrides.Tree,defaultExpandAll:this.defaultExpandAll,defaultExpandedKeys:this.defaultExpandedKeys,expandedKeys:this.mergedExpandedKeys,checkedKeys:this.treeCheckedKeys,selectedKeys:this.treeSelectedKeys,checkable:s,checkStrategy:this.checkStrategy,cascade:this.mergedCascade,leafOnly:this.leafOnly,multiple:this.multiple,renderLabel:this.renderLabel,renderPrefix:this.renderPrefix,renderSuffix:this.renderSuffix,renderSwitcherIcon:this.renderSwitcherIcon,nodeProps:this.nodeProps,watchProps:this.watchProps,virtualScroll:this.consistentMenuWidth&&this.virtualScroll,overrideDefaultNodeClickBehavior:this.overrideDefaultNodeClickBehavior,internalTreeSelect:!0,internalUnifySelectCheck:!0,internalScrollable:!0,internalScrollablePadding:this.menuPadding,internalFocusable:!1,internalCheckboxFocusable:!1,internalRenderEmpty:()=>g("div",{class:`${i}-tree-select-menu__empty`},wt(r.empty,()=>[g(St,{theme:e.peers.Empty,themeOverrides:e.peerOverrides.Empty})])),onLoad:this.onLoad,onUpdateCheckedKeys:this.handleUpdateCheckedKeys,onUpdateIndeterminateKeys:this.handleUpdateIndeterminateKeys,onUpdateExpandedKeys:this.doUpdateExpandedKeys}),At(r.action,k=>k?g("div",{class:`${i}-tree-select-menu__action`,"data-action":!0},k):null),g(wn,{onFocus:this.handleTabOut})),[[fn,this.handleMenuClickoutside,void 0,{capture:!0}]])}})})]}))}});export{ll as NTreeSelect,Vn as treeSelectProps};
