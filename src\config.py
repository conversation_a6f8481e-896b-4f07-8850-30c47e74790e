import os
import yaml
import src.log as log
import chardet

def __loadcfg(path: str, target: dict):

    if not os.path.exists(path):
        log.warning(f"config file '{path}' does not exist.")
        return

    with open(path, 'rb') as _stream:
        try:
            raw = _stream.read()
            result = chardet.detect(raw)
            encoding = result['encoding']
            text = raw.decode(encoding)
            data = yaml.safe_load(text)
            target.update(data)
        except yaml.YAMLError as exc:
            print(exc)
            raise exc
        finally:
            _stream.close()
    pass

def default():
    inst = config()
    __loadcfg(os.path.join(inst.CONFIG_DIR, "machine.yml"), inst.machine)
    __loadcfg(os.path.join(inst.CONFIG_DIR, "joystick.yml"), inst.joystick)
    __loadcfg(os.path.join(inst.CONFIG_DIR, "server.yml"), inst.server)
    __loadcfg(os.path.join(inst.CONFIG_DIR, "job.yml"), inst.job)
    __loadcfg(os.path.join(inst.CONFIG_DIR, "system.yml"), inst.system)
    return inst

def default_job():
    inst = config()
    __loadcfg(os.path.join(inst.CONFIG_DIR, "job.yml"), inst.job)
    return inst

class config:

    LOG_DIR: str = "./log"
    CONFIG_DIR: str = "./config"

    machine: dict = {}
    joystick: dict = {}
    server: dict = {}
    job: dict = {}

    def __init__(self):
        self.machine = {}
        self.joystick = {}
        self.server = {}
        self.job = {}
        self.system = {}

    @staticmethod
    def __savecfg(path: str, data: dict):
        with open(path, 'w') as _stream:
            yaml.dump(data, _stream)
    
    def dump(self, location, item = {"machine", "joystick", "job", "server", "system"}):
        if not os.path.exists(location):
            os.makedirs(location)
        
        for i in item:
            if not hasattr(self, i):
                log.warning(f"config item '{i}' does not exist.")
                continue
            data = getattr(self, i)
            if not isinstance(data, dict):
                log.warning(f"config item '{i}' is not a dict.")
                continue
            self.__savecfg(os.path.join(location, f"{i}.yml"), data)
        # self.__savecfg(os.path.join(location, "joystick.yml"), self.joystick)
        # self.__savecfg(os.path.join(location, "machine.yml"), self.machine)
        # self.__savecfg(os.path.join(location, "job.yml"), self.job)
        # self.__savecfg(os.path.join(location, "server.yml"), self.server)

    def load_overlay(self, location, item = {"machine", "joystick", "job", "server", "system"}):
        if not os.path.exists(location):
            print(f"override config file '{location}' does not exist.")
            return

        def __load_and_update(target: dict, path: str):
            if not os.path.exists(path):
                print(f"override config file '{path}' does not exist.")
                return
            with open(path, 'r') as _stream:
                data = yaml.safe_load(_stream)
                target.update(data)
        
        for i in item:
            __load_and_update(getattr(self, i), os.path.join(location, f"{i}.yml"))
        pass
