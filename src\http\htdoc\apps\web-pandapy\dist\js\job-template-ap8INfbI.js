import{u as n,_ as i}from"./form-1Qpdj_eE.js";import{d as m,j as l,o as c,s as u,g as p,v as d,u as o}from"../jse/index-index-Y3_OtjO-.js";import{E as f}from"./bootstrap-MyT3sENS.js";import"./index-DGcxnQ4T.js";const h=m({__name:"job-template",setup(g){const t=f(),[r,a]=n({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",handleSubmit:s=>{t.success(`表单数据：${JSON.stringify(s)}`)},schema:[]});return a.setValues({string:"string",number:123,radio:"B",radioButton:"C",checkbox:["A","C"],date:Date.now()}),(s,e)=>(c(),l(o(i),{description:"本页面可用于设定新建工程的默认参数",title:"工程模板"},{default:u(()=>[e[0]||(e[0]=p("br",null,null,-1)),d(o(r))]),_:1}))}});export{h as default};
