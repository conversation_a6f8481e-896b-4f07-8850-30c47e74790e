import {
  dark_default as dark_default3,
  light_default as light_default3
} from "./chunk-XKIBEKHX.js";
import {
  dark_default as dark_default4,
  light_default as light_default4
} from "./chunk-TPTSGLBB.js";
import {
  dark_default as dark_default2,
  light_default as light_default2
} from "./chunk-T4COAYJY.js";
import {
  dark_default,
  light_default
} from "./chunk-2AZKBT6I.js";
import {
  createTheme
} from "./chunk-HXOHBLE5.js";

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/time-picker/styles/_common.mjs
var common_default = {
  itemFontSize: "12px",
  itemHeight: "36px",
  itemWidth: "52px",
  panelActionPadding: "8px 0"
};

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/time-picker/styles/light.mjs
function self(vars) {
  const {
    popoverColor,
    textColor2,
    primaryColor,
    hoverColor,
    dividerColor,
    opacityDisabled,
    boxShadow2,
    borderRadius,
    iconColor,
    iconColorDisabled
  } = vars;
  return Object.assign(Object.assign({}, common_default), {
    panelColor: popoverColor,
    panelBoxShadow: boxShadow2,
    panelDividerColor: dividerColor,
    itemTextColor: textColor2,
    itemTextColorActive: primaryColor,
    itemColorHover: hoverColor,
    itemOpacityDisabled: opacityDisabled,
    itemBorderRadius: borderRadius,
    borderRadius,
    iconColor,
    iconColorDisabled
  });
}
var timePickerLight = createTheme({
  name: "TimePicker",
  common: light_default,
  peers: {
    Scrollbar: light_default2,
    Button: light_default4,
    Input: light_default3
  },
  self
});
var light_default5 = timePickerLight;

// ../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/time-picker/styles/dark.mjs
var timePickerDark = {
  name: "TimePicker",
  common: dark_default,
  peers: {
    Scrollbar: dark_default2,
    Button: dark_default4,
    Input: dark_default3
  },
  self
};
var dark_default5 = timePickerDark;

export {
  light_default5 as light_default,
  dark_default5 as dark_default
};
//# sourceMappingURL=chunk-I44DGCY4.js.map
