<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { NDivider, NCard, useMessage } from 'naive-ui';
import { useVbenForm } from '#/adapter/form';
import {
  getJoystickKeyBindings,
  getJoystickKeyFunctionList,
  getInputDevices,
  setJoystickKeyBindings
} from '#/api';

const message = useMessage();
const [JoyStickForm, joyStickFormApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    message.success(`表单数据：${JSON.stringify(values)}`);
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'joystickDevice',
      label: '摇杆设备',
      rules: "selectRequired",
      componentProps: {
        api: getInputDevices,
        afterFetch: (_: any) => _.devices.map((item: any) => ({
          label: item.device,
          value: item.device,
        })),
      },
    }
  ],
});

const [DeadZoneForm, _] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    message.success(`表单数据：${JSON.stringify(values)}`);
  },
  schema: [
  ],
});

const [FunkeyBindingForm, funkeyBindingForm] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    setJoystickKeyBindings({"bindings": values}).then(() => {
      message.success('按键绑定已保存');
    }).catch((error) => {
      console.error(`保存失败: ${error.message}`);
    });
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
  ],
});

getJoystickKeyFunctionList()
  .then(res => {

    if(!res.functions || res.functions.length <= 0) {
      message.warning('没有找到摇杆按键绑定信息');
      return false;
    }
    
    return res.functions;
  })

  .then(bindfunc => {

    getJoystickKeyBindings()
      .then((res) => {

        if(!res.bindings || res.bindings.length <= 0) {
          message.warning('没有找到摇杆按键绑定信息');
          return
        }

        funkeyBindingForm.setState((_) => {

          let newSchema = [];
          res.bindings.forEach(i => {
            newSchema.push({
              field: i.name,
              label: i.option.name,
              value: i.option.bind_func,
              component: 'Select',
              componentProps: {
                options: bindfunc.map((item) => ({
                  label: item.name,
                  value: item.value,
                })),
                placeholder: '请选择绑定功能',
                clearable: true,
                // showArrow: false
              },
              fieldName: `${i.name}`,
            });
          });

          return {schema: newSchema};

        });
      });
    }
);

getInputDevices().then(res => {
  console.log('获取到的输入设备', res);
});

</script>
<template>
  <Page
    description="本页面包含手柄和摇杆的配置项目。可以完成包括摇杆死区、按键功能、绑定选项等设定"
    title="手柄和摇杆">
  
    <NCard title="摇杆设备">
      <p>设置系统使用的摇杆设备</p>
      <NDivider class="my-4" />
      <JoyStickForm/>
    </NCard>

    <br/>

    <!-- <NCard title="摇杆死区"><DeadZoneForm/></NCard><br/> -->
    <NCard title="功能键绑定">
      <p>绑定按键的功能可以在当前的工作流中立即生效</p>
      <NDivider class="my-4" />
      <FunkeyBindingForm/></NCard>
  </Page>
</template>
