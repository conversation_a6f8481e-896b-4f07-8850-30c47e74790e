import{E as h,F as p,G as N,H as w}from"./bootstrap-MyT3sENS.js";import{u as m,_ as S}from"./form-1Qpdj_eE.js";import{h as x,i as y,j as P,k as B,l as v,m as C,n as I}from"./settings-BETc9HZJ.js";import{d as z,r as F,y as A,j as R,o as G,s as a,v as l,g as s,u as o,z as M,x as V}from"../jse/index-index-Y3_OtjO-.js";import{NDivider as _}from"./index-DoPwuNMq.js";import"./index-DGcxnQ4T.js";const k={class:"form-is-required flex-row items-center pb-6 relative flex flex-shrink-0"},D={"label-width":100,"label-placement":"left","label-align":"left","label-col":{span:4},"wrapper-col":{span:20},class:"w-full leading-6"},T=z({__name:"machine",setup(X){const n=h(),[c,u]=m({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{y(e).then(()=>{n.success("串口配置已保存")}).catch(t=>{console.error(`保存失败: ${t.message}`)})},resetButtonOptions:{show:!1},schema:[{component:"ApiSelect",fieldName:"serialPort",label:"串口",rules:"selectRequired",componentProps:{api:x,afterFetch:e=>e.devices.map(t=>({label:t.device,value:t.device}))}},{component:"InputNumber",fieldName:"baudRate",label:"波特率",componentProps:{showButtons:!1}}]}),[d,f]=m({commonConfig:{componentProps:{class:"w-full"}},layout:"vertical",wrapperClass:"grid-cols-3 md:grid-cols-3 lg:grid-cols-3",handleSubmit:e=>{P(e).then(()=>{n.success("电机参数已保存")}).catch(t=>{n.error(`保存失败: ${t.message}`)})},resetButtonOptions:{show:!1},schema:[{component:"InputNumber",label:"X轴微步",fieldName:"xMicrostep",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Y轴微步",fieldName:"yMicrostep",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Z轴微步",fieldName:"zMicrostep",componentProps:{showButtons:!1}},{component:"InputNumber",label:"X电机电流 (mA)",fieldName:"xStepperAmps",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Y电机电流 (mA)",fieldName:"yStepperAmps",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Z电机电流 (mA)",fieldName:"zStepperAmps",componentProps:{showButtons:!1}},{component:"InputNumber",label:"X电机给进率",fieldName:"xStepperFeedrate",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Y电机给进率",fieldName:"yStepperFeedrate",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Z电机给进率",fieldName:"zStepperFeedrate",componentProps:{showButtons:!1}},{component:"InputNumber",label:"X电机比例 (step/mm)",fieldName:"xStepperRatio",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Y电机比例 (step/mm)",fieldName:"yStepperRatio",componentProps:{showButtons:!1}},{component:"InputNumber",label:"Z电机比例 (step/mm)",fieldName:"zStepperRatio",componentProps:{showButtons:!1}}]}),[i]=m({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",resetButtonOptions:{show:!1},schema:[{component:"VbenInput",fieldName:"gcode",componentProps:{type:"textarea",rows:10,placeholder:"请输入初始化G代码",style:{width:"100%",height:"500px"}}}]});function b(){I({gcode:r.value.value}).then(()=>{n.success("初始化G代码已保存")}).catch(e=>{n.error(`保存失败: ${e.message}`)})}const r=F(null);return A(()=>{B().then(e=>{r.value.value=e.gcode.join(`
`)}),v().then(e=>{console.log(e),f.setValues({xMicrostep:e.x.microsteps,yMicrostep:e.y.microsteps,zMicrostep:e.z.microsteps,xStepperAmps:e.x.current,yStepperAmps:e.y.current,zStepperAmps:e.z.current,xStepperFeedrate:e.x.feedrate,yStepperFeedrate:e.y.feedrate,zStepperFeedrate:e.z.feedrate,xStepperRatio:e.x.ratio,yStepperRatio:e.y.ratio,zStepperRatio:e.z.ratio})}),C().then(e=>{u.setValues({serialPort:e.serialPort,baudRate:e.baudRate})})}),(e,t)=>(G(),R(o(S),{description:"本页面用于配置物理硬件设备、初始化G代码、设置默认参数等",title:"硬件"},{default:a(()=>[l(o(p),{title:"串口配置"},{default:a(()=>[l(o(c))]),_:1}),t[5]||(t[5]=s("br",null,null,-1)),l(o(p),{title:"电机参数"},{default:a(()=>[l(o(d))]),_:1}),t[6]||(t[6]=s("br",null,null,-1)),l(o(p),{title:"初始化G代码"},{default:a(()=>[s("div",k,[s("form",D,[t[1]||(t[1]=s("span",{class:"text-sm"},"本选项包含系统启动时对下位机初始化的 gcode 预编程",-1)),t[2]||(t[2]=s("br",null,null,-1)),t[3]||(t[3]=s("span",{class:"text-sm"},"包含简单的表达式支持",-1)),l(o(_),{class:"my-4"}),M(s("textarea",{ref_key:"gcodeRef",ref:r,"vben-form":"GCodeForm","onUpdate:modelValue":t[0]||(t[0]=g=>o(i).gcode=g),class:"w-full h-96 p-2 border rounded-md"},null,512),[[N,o(i).gcode]])])]),l(o(w),{type:"primary",onClick:b},{default:a(()=>t[4]||(t[4]=[V("提交")])),_:1})]),_:1})]),_:1}))}});export{T as default};
