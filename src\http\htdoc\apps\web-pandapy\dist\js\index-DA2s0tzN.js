import{bm as Ye,S as te,i as Ge,u as ue,b as ae,bn as Pt,Z as Se,n as U,l as w,bo as Ue,aa as kt,p as St,Q as E,ap as It,aq as zt,a9 as Ce,I as Bt,bp as Dt,a6 as ce,x as Ke,ad as Mt,P as ye,bq as Te,O as Je,o as N,aT as Ee,m as J,q as Ut,br as pe,aS as Qe,bs as Fe,H as fe,N as Et,L as Ft,bl as jt,bt as _t,w as At,A as je}from"./bootstrap-MyT3sENS.js";import{h as n,d as Q,r as _,c as j,F as ve,z as _e,t as P,w as $t,a0 as et,i as ne,Y as Ht,I as Ie,J as Nt,y as Ae,K as Pe,ap as Zt,H as Vt}from"../jse/index-index-Y3_OtjO-.js";import{N as Wt}from"./Progress-BJgs0fD_.js";import{u as Xt}from"./use-locale-DPELDQgW.js";import{N as qt,p as Yt}from"./Popover-BJHGarS6.js";import{b as Gt}from"./Follower-B0-DuUBY.js";import{E as Kt}from"./Eye-CI8SCzDq.js";import{A as Jt}from"./Add-DqgRNQIU.js";import{u as Qt}from"./use-merged-state-DFvgmEt8.js";import"./format-length-B-p6aW7q.js";function tt(e,o){if(!e)return;const t=document.createElement("a");t.href=e,o!==void 0&&(t.download=o),document.body.appendChild(t),t.click(),document.body.removeChild(t)}function eo(e,o,t,r){for(var l=-1,s=e==null?0:e.length;++l<s;)t=o(t,e[l],l,e);return t}function to(e){return function(o){return e==null?void 0:e[o]}}var oo={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},no=to(oo),ro=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,io="\\u0300-\\u036f",ao="\\ufe20-\\ufe2f",lo="\\u20d0-\\u20ff",so=io+ao+lo,uo="["+so+"]",co=RegExp(uo,"g");function fo(e){return e=Ye(e),e&&e.replace(ro,no).replace(co,"")}var ho=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function go(e){return e.match(ho)||[]}var vo=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function po(e){return vo.test(e)}var ot="\\ud800-\\udfff",mo="\\u0300-\\u036f",wo="\\ufe20-\\ufe2f",bo="\\u20d0-\\u20ff",xo=mo+wo+bo,nt="\\u2700-\\u27bf",rt="a-z\\xdf-\\xf6\\xf8-\\xff",Co="\\xac\\xb1\\xd7\\xf7",yo="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Ro="\\u2000-\\u206f",Oo=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",it="A-Z\\xc0-\\xd6\\xd8-\\xde",Lo="\\ufe0e\\ufe0f",at=Co+yo+Ro+Oo,lt="['’]",$e="["+at+"]",To="["+xo+"]",st="\\d+",Po="["+nt+"]",ut="["+rt+"]",dt="[^"+ot+at+st+nt+rt+it+"]",ko="\\ud83c[\\udffb-\\udfff]",So="(?:"+To+"|"+ko+")",Io="[^"+ot+"]",ct="(?:\\ud83c[\\udde6-\\uddff]){2}",ft="[\\ud800-\\udbff][\\udc00-\\udfff]",ie="["+it+"]",zo="\\u200d",He="(?:"+ut+"|"+dt+")",Bo="(?:"+ie+"|"+dt+")",Ne="(?:"+lt+"(?:d|ll|m|re|s|t|ve))?",Ze="(?:"+lt+"(?:D|LL|M|RE|S|T|VE))?",ht=So+"?",gt="["+Lo+"]?",Do="(?:"+zo+"(?:"+[Io,ct,ft].join("|")+")"+gt+ht+")*",Mo="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Uo="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Eo=gt+ht+Do,Fo="(?:"+[Po,ct,ft].join("|")+")"+Eo,jo=RegExp([ie+"?"+ut+"+"+Ne+"(?="+[$e,ie,"$"].join("|")+")",Bo+"+"+Ze+"(?="+[$e,ie+He,"$"].join("|")+")",ie+"?"+He+"+"+Ne,ie+"+"+Ze,Uo,Mo,st,Fo].join("|"),"g");function _o(e){return e.match(jo)||[]}function Ao(e,o,t){return e=Ye(e),o=o,o===void 0?po(e)?_o(e):go(e):e.match(o)||[]}var $o="['’]",Ho=RegExp($o,"g");function No(e){return function(o){return eo(Ao(fo(o).replace(Ho,"")),e,"")}}var Zo=No(function(e,o,t){return e+(t?"-":"")+o.toLowerCase()});const Vo=te("attach",()=>n("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M3.25735931,8.70710678 L7.85355339,4.1109127 C8.82986412,3.13460197 10.4127766,3.13460197 11.3890873,4.1109127 C12.365398,5.08722343 12.365398,6.67013588 11.3890873,7.64644661 L6.08578644,12.9497475 C5.69526215,13.3402718 5.06209717,13.3402718 4.67157288,12.9497475 C4.28104858,12.5592232 4.28104858,11.9260582 4.67157288,11.5355339 L9.97487373,6.23223305 C10.1701359,6.0369709 10.1701359,5.72038841 9.97487373,5.52512627 C9.77961159,5.32986412 9.4630291,5.32986412 9.26776695,5.52512627 L3.96446609,10.8284271 C3.18341751,11.6094757 3.18341751,12.8758057 3.96446609,13.6568542 C4.74551468,14.4379028 6.01184464,14.4379028 6.79289322,13.6568542 L12.0961941,8.35355339 C13.4630291,6.98671837 13.4630291,4.77064094 12.0961941,3.40380592 C10.7293591,2.0369709 8.51328163,2.0369709 7.14644661,3.40380592 L2.55025253,8 C2.35499039,8.19526215 2.35499039,8.51184464 2.55025253,8.70710678 C2.74551468,8.90236893 3.06209717,8.90236893 3.25735931,8.70710678 Z"}))))),Wo=te("cancel",()=>n("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M2.58859116,2.7156945 L2.64644661,2.64644661 C2.82001296,2.47288026 3.08943736,2.45359511 3.2843055,2.58859116 L3.35355339,2.64644661 L8,7.293 L12.6464466,2.64644661 C12.8417088,2.45118446 13.1582912,2.45118446 13.3535534,2.64644661 C13.5488155,2.84170876 13.5488155,3.15829124 13.3535534,3.35355339 L8.707,8 L13.3535534,12.6464466 C13.5271197,12.820013 13.5464049,13.0894374 13.4114088,13.2843055 L13.3535534,13.3535534 C13.179987,13.5271197 12.9105626,13.5464049 12.7156945,13.4114088 L12.6464466,13.3535534 L8,8.707 L3.35355339,13.3535534 C3.15829124,13.5488155 2.84170876,13.5488155 2.64644661,13.3535534 C2.45118446,13.1582912 2.45118446,12.8417088 2.64644661,12.6464466 L7.293,8 L2.64644661,3.35355339 C2.47288026,3.17998704 2.45359511,2.91056264 2.58859116,2.7156945 L2.64644661,2.64644661 L2.58859116,2.7156945 Z"}))))),vt=te("download",()=>n("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},n("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},n("g",{fill:"currentColor","fill-rule":"nonzero"},n("path",{d:"M3.5,13 L12.5,13 C12.7761424,13 13,13.2238576 13,13.5 C13,13.7454599 12.8231248,13.9496084 12.5898756,13.9919443 L12.5,14 L3.5,14 C3.22385763,14 3,13.7761424 3,13.5 C3,13.2545401 3.17687516,13.0503916 3.41012437,13.0080557 L3.5,13 L12.5,13 L3.5,13 Z M7.91012437,1.00805567 L8,1 C8.24545989,1 8.44960837,1.17687516 8.49194433,1.41012437 L8.5,1.5 L8.5,10.292 L11.1819805,7.6109127 C11.3555469,7.43734635 11.6249713,7.4180612 11.8198394,7.55305725 L11.8890873,7.6109127 C12.0626536,7.78447906 12.0819388,8.05390346 11.9469427,8.2487716 L11.8890873,8.31801948 L8.35355339,11.8535534 C8.17998704,12.0271197 7.91056264,12.0464049 7.7156945,11.9114088 L7.64644661,11.8535534 L4.1109127,8.31801948 C3.91565056,8.12275734 3.91565056,7.80617485 4.1109127,7.6109127 C4.28447906,7.43734635 4.55390346,7.4180612 4.7487716,7.55305725 L4.81801948,7.6109127 L7.5,10.292 L7.5,1.5 C7.5,1.25454011 7.67687516,1.05039163 7.91012437,1.00805567 L8,1 L7.91012437,1.00805567 Z"}))))),Xo=Q({name:"ResizeSmall",render(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},n("g",{fill:"none"},n("path",{d:"M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zM16 5.5A1.5 1.5 0 0 0 14.5 4h-1a.5.5 0 0 1 0-1h1A2.5 2.5 0 0 1 17 5.5v1a.5.5 0 0 1-1 0v-1zm0 9a1.5 1.5 0 0 1-1.5 1.5h-1a.5.5 0 0 0 0 1h1a2.5 2.5 0 0 0 2.5-2.5v-1a.5.5 0 0 0-1 0v1zm-12 0A1.5 1.5 0 0 0 5.5 16h1.25a.5.5 0 0 1 0 1H5.5A2.5 2.5 0 0 1 3 14.5v-1.25a.5.5 0 0 1 1 0v1.25zM8.5 7A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zM8 8.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-3z",fill:"currentColor"})))}}),qo=te("retry",()=>n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},n("path",{d:"M320,146s24.36-12-64-12A160,160,0,1,0,416,294",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 32px;"}),n("polyline",{points:"256 58 336 138 256 218",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}))),Yo=te("rotateClockwise",()=>n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 12.7916 15.3658 15.2026 13 16.3265V14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5V17.5C12 17.7761 12.2239 18 12.5 18H15.5C15.7761 18 16 17.7761 16 17.5C16 17.2239 15.7761 17 15.5 17H13.8758C16.3346 15.6357 18 13.0128 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 10.2761 2.22386 10.5 2.5 10.5C2.77614 10.5 3 10.2761 3 10Z",fill:"currentColor"}),n("path",{d:"M10 12C11.1046 12 12 11.1046 12 10C12 8.89543 11.1046 8 10 8C8.89543 8 8 8.89543 8 10C8 11.1046 8.89543 12 10 12ZM10 11C9.44772 11 9 10.5523 9 10C9 9.44772 9.44772 9 10 9C10.5523 9 11 9.44772 11 10C11 10.5523 10.5523 11 10 11Z",fill:"currentColor"}))),Go=te("rotateClockwise",()=>n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 12.7916 4.63419 15.2026 7 16.3265V14.5C7 14.2239 7.22386 14 7.5 14C7.77614 14 8 14.2239 8 14.5V17.5C8 17.7761 7.77614 18 7.5 18H4.5C4.22386 18 4 17.7761 4 17.5C4 17.2239 4.22386 17 4.5 17H6.12422C3.66539 15.6357 2 13.0128 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 10.2761 17.7761 10.5 17.5 10.5C17.2239 10.5 17 10.2761 17 10Z",fill:"currentColor"}),n("path",{d:"M10 12C8.89543 12 8 11.1046 8 10C8 8.89543 8.89543 8 10 8C11.1046 8 12 8.89543 12 10C12 11.1046 11.1046 12 10 12ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z",fill:"currentColor"}))),Ko=te("trash",()=>n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},n("path",{d:"M432,144,403.33,419.74A32,32,0,0,1,371.55,448H140.46a32,32,0,0,1-31.78-28.26L80,144",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),n("rect",{x:"32",y:"64",width:"448",height:"80",rx:"16",ry:"16",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),n("line",{x1:"312",y1:"240",x2:"200",y2:"352",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),n("line",{x1:"312",y1:"352",x2:"200",y2:"240",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}))),Jo=te("zoomIn",()=>n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M11.5 8.5C11.5 8.22386 11.2761 8 11 8H9V6C9 5.72386 8.77614 5.5 8.5 5.5C8.22386 5.5 8 5.72386 8 6V8H6C5.72386 8 5.5 8.22386 5.5 8.5C5.5 8.77614 5.72386 9 6 9H8V11C8 11.2761 8.22386 11.5 8.5 11.5C8.77614 11.5 9 11.2761 9 11V9H11C11.2761 9 11.5 8.77614 11.5 8.5Z",fill:"currentColor"}),n("path",{d:"M8.5 3C11.5376 3 14 5.46243 14 8.5C14 9.83879 13.5217 11.0659 12.7266 12.0196L16.8536 16.1464C17.0488 16.3417 17.0488 16.6583 16.8536 16.8536C16.68 17.0271 16.4106 17.0464 16.2157 16.9114L16.1464 16.8536L12.0196 12.7266C11.0659 13.5217 9.83879 14 8.5 14C5.46243 14 3 11.5376 3 8.5C3 5.46243 5.46243 3 8.5 3ZM8.5 4C6.01472 4 4 6.01472 4 8.5C4 10.9853 6.01472 13 8.5 13C10.9853 13 13 10.9853 13 8.5C13 6.01472 10.9853 4 8.5 4Z",fill:"currentColor"}))),Qo=te("zoomOut",()=>n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M11 8C11.2761 8 11.5 8.22386 11.5 8.5C11.5 8.77614 11.2761 9 11 9H6C5.72386 9 5.5 8.77614 5.5 8.5C5.5 8.22386 5.72386 8 6 8H11Z",fill:"currentColor"}),n("path",{d:"M14 8.5C14 5.46243 11.5376 3 8.5 3C5.46243 3 3 5.46243 3 8.5C3 11.5376 5.46243 14 8.5 14C9.83879 14 11.0659 13.5217 12.0196 12.7266L16.1464 16.8536L16.2157 16.9114C16.4106 17.0464 16.68 17.0271 16.8536 16.8536C17.0488 16.6583 17.0488 16.3417 16.8536 16.1464L12.7266 12.0196C13.5217 11.0659 14 9.83879 14 8.5ZM4 8.5C4 6.01472 6.01472 4 8.5 4C10.9853 4 13 6.01472 13 8.5C13 10.9853 10.9853 13 8.5 13C6.01472 13 4 10.9853 4 8.5Z",fill:"currentColor"}))),en=Ge&&"loading"in document.createElement("img");function tn(e={}){var o;const{root:t=null}=e;return{hash:`${e.rootMargin||"0px 0px 0px 0px"}-${Array.isArray(e.threshold)?e.threshold.join(","):(o=e.threshold)!==null&&o!==void 0?o:"0"}`,options:Object.assign(Object.assign({},e),{root:(typeof t=="string"?document.querySelector(t):t)||document.documentElement})}}const Re=new WeakMap,Oe=new WeakMap,Le=new WeakMap,on=(e,o,t)=>{if(!e)return()=>{};const r=tn(o),{root:l}=r.options;let s;const d=Re.get(l);d?s=d:(s=new Map,Re.set(l,s));let c,u;s.has(r.hash)?(u=s.get(r.hash),u[1].has(e)||(c=u[0],u[1].add(e),c.observe(e))):(c=new IntersectionObserver(g=>{g.forEach(b=>{if(b.isIntersecting){const L=Oe.get(b.target),y=Le.get(b.target);L&&L(),y&&(y.value=!0)}})},r.options),c.observe(e),u=[c,new Set([e])],s.set(r.hash,u));let i=!1;const a=()=>{i||(Oe.delete(e),Le.delete(e),i=!0,u[1].has(e)&&(u[0].unobserve(e),u[1].delete(e)),u[1].size<=0&&s.delete(r.hash),s.size||Re.delete(l))};return Oe.set(e,a),Le.set(e,t),a},nn=Object.assign(Object.assign({},Yt),ae.props),rn=Q({name:"Tooltip",props:nn,slots:Object,__popover__:!0,setup(e){const{mergedClsPrefixRef:o}=ue(e),t=ae("Tooltip","-tooltip",void 0,Pt,e,o),r=_(null);return Object.assign(Object.assign({},{syncPosition(){r.value.syncPosition()},setShow(s){r.value.setShow(s)}}),{popoverRef:r,mergedTheme:t,popoverThemeOverrides:j(()=>t.value.self)})},render(){const{mergedTheme:e,internalExtraClass:o}=this;return n(qt,Object.assign(Object.assign({},this.$props),{theme:e.peers.Popover,themeOverrides:e.peerOverrides.Popover,builtinThemeOverrides:this.popoverThemeOverrides,internalExtraClass:o.concat("tooltip"),ref:"popoverRef"}),this.$slots)}});function an(){return n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M6 5C5.75454 5 5.55039 5.17688 5.50806 5.41012L5.5 5.5V14.5C5.5 14.7761 5.72386 15 6 15C6.24546 15 6.44961 14.8231 6.49194 14.5899L6.5 14.5V5.5C6.5 5.22386 6.27614 5 6 5ZM13.8536 5.14645C13.68 4.97288 13.4106 4.9536 13.2157 5.08859L13.1464 5.14645L8.64645 9.64645C8.47288 9.82001 8.4536 10.0894 8.58859 10.2843L8.64645 10.3536L13.1464 14.8536C13.3417 15.0488 13.6583 15.0488 13.8536 14.8536C14.0271 14.68 14.0464 14.4106 13.9114 14.2157L13.8536 14.1464L9.70711 10L13.8536 5.85355C14.0488 5.65829 14.0488 5.34171 13.8536 5.14645Z",fill:"currentColor"}))}function ln(){return n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M13.5 5C13.7455 5 13.9496 5.17688 13.9919 5.41012L14 5.5V14.5C14 14.7761 13.7761 15 13.5 15C13.2545 15 13.0504 14.8231 13.0081 14.5899L13 14.5V5.5C13 5.22386 13.2239 5 13.5 5ZM5.64645 5.14645C5.82001 4.97288 6.08944 4.9536 6.28431 5.08859L6.35355 5.14645L10.8536 9.64645C11.0271 9.82001 11.0464 10.0894 10.9114 10.2843L10.8536 10.3536L6.35355 14.8536C6.15829 15.0488 5.84171 15.0488 5.64645 14.8536C5.47288 14.68 5.4536 14.4106 5.58859 14.2157L5.64645 14.1464L9.79289 10L5.64645 5.85355C5.45118 5.65829 5.45118 5.34171 5.64645 5.14645Z",fill:"currentColor"}))}function sn(){return n("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M4.089 4.216l.057-.07a.5.5 0 0 1 .638-.057l.07.057L10 9.293l5.146-5.147a.5.5 0 0 1 .638-.057l.07.057a.5.5 0 0 1 .057.638l-.057.07L10.707 10l5.147 5.146a.5.5 0 0 1 .057.638l-.057.07a.5.5 0 0 1-.638.057l-.07-.057L10 10.707l-5.146 5.147a.5.5 0 0 1-.638.057l-.07-.057a.5.5 0 0 1-.057-.638l.057-.07L9.293 10L4.146 4.854a.5.5 0 0 1-.057-.638l.057-.07l-.057.07z",fill:"currentColor"}))}const ze=Object.assign(Object.assign({},ae.props),{onPreviewPrev:Function,onPreviewNext:Function,showToolbar:{type:Boolean,default:!0},showToolbarTooltip:Boolean,renderToolbar:Function}),pt=Se("n-image"),un=U([U("body >",[w("image-container","position: fixed;")]),w("image-preview-container",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 `),w("image-preview-overlay",`
 z-index: -1;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background: rgba(0, 0, 0, .3);
 `,[Ue()]),w("image-preview-toolbar",`
 z-index: 1;
 position: absolute;
 left: 50%;
 transform: translateX(-50%);
 border-radius: var(--n-toolbar-border-radius);
 height: 48px;
 bottom: 40px;
 padding: 0 12px;
 background: var(--n-toolbar-color);
 box-shadow: var(--n-toolbar-box-shadow);
 color: var(--n-toolbar-icon-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 `,[w("base-icon",`
 padding: 0 8px;
 font-size: 28px;
 cursor: pointer;
 `),Ue()]),w("image-preview-wrapper",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 pointer-events: none;
 `,[kt()]),w("image-preview",`
 user-select: none;
 -webkit-user-select: none;
 pointer-events: all;
 margin: auto;
 max-height: calc(100vh - 32px);
 max-width: calc(100vw - 32px);
 transition: transform .3s var(--n-bezier);
 `),w("image",`
 display: inline-flex;
 max-height: 100%;
 max-width: 100%;
 `,[St("preview-disabled",`
 cursor: pointer;
 `),U("img",`
 border-radius: inherit;
 `)])]),he=32,mt=Q({name:"ImagePreview",props:Object.assign(Object.assign({},ze),{onNext:Function,onPrev:Function,clsPrefix:{type:String,required:!0}}),setup(e){const o=ae("Image","-image",un,Dt,e,P(e,"clsPrefix"));let t=null;const r=_(null),l=_(null),s=_(void 0),d=_(!1),c=_(!1),{localeRef:u}=Xt("Image");function i(){const{value:f}=l;if(!t||!f)return;const{style:m}=f,h=t.getBoundingClientRect(),k=h.left+h.width/2,S=h.top+h.height/2;m.transformOrigin=`${k}px ${S}px`}function a(f){var m,h;switch(f.key){case" ":f.preventDefault();break;case"ArrowLeft":(m=e.onPrev)===null||m===void 0||m.call(e);break;case"ArrowRight":(h=e.onNext)===null||h===void 0||h.call(e);break;case"Escape":Be();break}}$t(d,f=>{f?ye("keydown",document,a):ce("keydown",document,a)}),et(()=>{ce("keydown",document,a)});let g=0,b=0,L=0,y=0,A=0,V=0,$=0,F=0,Y=!1;function z(f){const{clientX:m,clientY:h}=f;L=m-g,y=h-b,Gt(G)}function p(f){const{mouseUpClientX:m,mouseUpClientY:h,mouseDownClientX:k,mouseDownClientY:S}=f,Z=k-m,q=S-h,K=`vertical${q>0?"Top":"Bottom"}`,oe=`horizontal${Z>0?"Left":"Right"}`;return{moveVerticalDirection:K,moveHorizontalDirection:oe,deltaHorizontal:Z,deltaVertical:q}}function C(f){const{value:m}=r;if(!m)return{offsetX:0,offsetY:0};const h=m.getBoundingClientRect(),{moveVerticalDirection:k,moveHorizontalDirection:S,deltaHorizontal:Z,deltaVertical:q}=f||{};let K=0,oe=0;return h.width<=window.innerWidth?K=0:h.left>0?K=(h.width-window.innerWidth)/2:h.right<window.innerWidth?K=-(h.width-window.innerWidth)/2:S==="horizontalRight"?K=Math.min((h.width-window.innerWidth)/2,A-(Z!=null?Z:0)):K=Math.max(-((h.width-window.innerWidth)/2),A-(Z!=null?Z:0)),h.height<=window.innerHeight?oe=0:h.top>0?oe=(h.height-window.innerHeight)/2:h.bottom<window.innerHeight?oe=-(h.height-window.innerHeight)/2:k==="verticalBottom"?oe=Math.min((h.height-window.innerHeight)/2,V-(q!=null?q:0)):oe=Math.max(-((h.height-window.innerHeight)/2),V-(q!=null?q:0)),{offsetX:K,offsetY:oe}}function x(f){ce("mousemove",document,z),ce("mouseup",document,x);const{clientX:m,clientY:h}=f;Y=!1;const k=p({mouseUpClientX:m,mouseUpClientY:h,mouseDownClientX:$,mouseDownClientY:F}),S=C(k);L=S.offsetX,y=S.offsetY,G()}const B=ne(pt,null);function v(f){var m,h;if((h=(m=B==null?void 0:B.previewedImgPropsRef.value)===null||m===void 0?void 0:m.onMousedown)===null||h===void 0||h.call(m,f),f.button!==0)return;const{clientX:k,clientY:S}=f;Y=!0,g=k-L,b=S-y,A=L,V=y,$=k,F=S,G(),ye("mousemove",document,z),ye("mouseup",document,x)}const T=1.5;let R=0,O=1,D=0;function H(f){var m,h;(h=(m=B==null?void 0:B.previewedImgPropsRef.value)===null||m===void 0?void 0:m.onDblclick)===null||h===void 0||h.call(m,f);const k=de();O=O===k?1:k,G()}function M(){O=1,R=0}function I(){var f;M(),D=0,(f=e.onPrev)===null||f===void 0||f.call(e)}function X(){var f;M(),D=0,(f=e.onNext)===null||f===void 0||f.call(e)}function W(){D-=90,G()}function ee(){D+=90,G()}function me(){const{value:f}=r;if(!f)return 1;const{innerWidth:m,innerHeight:h}=window,k=Math.max(1,f.naturalHeight/(h-he)),S=Math.max(1,f.naturalWidth/(m-he));return Math.max(3,k*2,S*2)}function de(){const{value:f}=r;if(!f)return 1;const{innerWidth:m,innerHeight:h}=window,k=f.naturalHeight/(h-he),S=f.naturalWidth/(m-he);return k<1&&S<1?1:Math.max(k,S)}function we(){const f=me();O<f&&(R+=1,O=Math.min(f,Math.pow(T,R)),G())}function be(){if(O>.5){const f=O;R-=1,O=Math.max(.5,Math.pow(T,R));const m=f-O;G(!1);const h=C();O+=m,G(!1),O-=m,L=h.offsetX,y=h.offsetY,G()}}function xe(){const f=s.value;f&&tt(f,void 0)}function G(f=!0){var m;const{value:h}=r;if(!h)return;const{style:k}=h,S=Ht((m=B==null?void 0:B.previewedImgPropsRef.value)===null||m===void 0?void 0:m.style);let Z="";if(typeof S=="string")Z=`${S};`;else for(const K in S)Z+=`${Zo(K)}: ${S[K]};`;const q=`transform-origin: center; transform: translateX(${L}px) translateY(${y}px) rotate(${D}deg) scale(${O});`;Y?k.cssText=`${Z}cursor: grabbing; transition: none;${q}`:k.cssText=`${Z}cursor: grab;${q}${f?"":"transition: none;"}`,f||h.offsetHeight}function Be(){d.value=!d.value,c.value=!0}function Ot(){O=de(),R=Math.ceil(Math.log(O)/Math.log(T)),L=0,y=0,G()}const Lt={setPreviewSrc:f=>{s.value=f},setThumbnailEl:f=>{t=f},toggleShow:Be};function Tt(f,m){if(e.showToolbarTooltip){const{value:h}=o;return n(rn,{to:!1,theme:h.peers.Tooltip,themeOverrides:h.peerOverrides.Tooltip,keepAliveOnHover:!1},{default:()=>u.value[m],trigger:()=>f})}else return f}const De=j(()=>{const{common:{cubicBezierEaseInOut:f},self:{toolbarIconColor:m,toolbarBorderRadius:h,toolbarBoxShadow:k,toolbarColor:S}}=o.value;return{"--n-bezier":f,"--n-toolbar-icon-color":m,"--n-toolbar-color":S,"--n-toolbar-border-radius":h,"--n-toolbar-box-shadow":k}}),{inlineThemeDisabled:Me}=ue(),re=Me?Ke("image-preview",void 0,De,e):void 0;return Object.assign({previewRef:r,previewWrapperRef:l,previewSrc:s,show:d,appear:Mt(),displayed:c,previewedImgProps:B==null?void 0:B.previewedImgPropsRef,handleWheel(f){f.preventDefault()},handlePreviewMousedown:v,handlePreviewDblclick:H,syncTransformOrigin:i,handleAfterLeave:()=>{M(),D=0,c.value=!1},handleDragStart:f=>{var m,h;(h=(m=B==null?void 0:B.previewedImgPropsRef.value)===null||m===void 0?void 0:m.onDragstart)===null||h===void 0||h.call(m,f),f.preventDefault()},zoomIn:we,zoomOut:be,handleDownloadClick:xe,rotateCounterclockwise:W,rotateClockwise:ee,handleSwitchPrev:I,handleSwitchNext:X,withTooltip:Tt,resizeToOrignalImageSize:Ot,cssVars:Me?void 0:De,themeClass:re==null?void 0:re.themeClass,onRender:re==null?void 0:re.onRender},Lt)},render(){var e,o;const{clsPrefix:t,renderToolbar:r,withTooltip:l}=this,s=l(n(E,{clsPrefix:t,onClick:this.handleSwitchPrev},{default:an}),"tipPrevious"),d=l(n(E,{clsPrefix:t,onClick:this.handleSwitchNext},{default:ln}),"tipNext"),c=l(n(E,{clsPrefix:t,onClick:this.rotateCounterclockwise},{default:()=>n(Go,null)}),"tipCounterclockwise"),u=l(n(E,{clsPrefix:t,onClick:this.rotateClockwise},{default:()=>n(Yo,null)}),"tipClockwise"),i=l(n(E,{clsPrefix:t,onClick:this.resizeToOrignalImageSize},{default:()=>n(Xo,null)}),"tipOriginalSize"),a=l(n(E,{clsPrefix:t,onClick:this.zoomOut},{default:()=>n(Qo,null)}),"tipZoomOut"),g=l(n(E,{clsPrefix:t,onClick:this.handleDownloadClick},{default:()=>n(vt,null)}),"tipDownload"),b=l(n(E,{clsPrefix:t,onClick:this.toggleShow},{default:sn}),"tipClose"),L=l(n(E,{clsPrefix:t,onClick:this.zoomIn},{default:()=>n(Jo,null)}),"tipZoomIn");return n(ve,null,(o=(e=this.$slots).default)===null||o===void 0?void 0:o.call(e),n(It,{show:this.show},{default:()=>{var y;return this.show||this.displayed?((y=this.onRender)===null||y===void 0||y.call(this),_e(n("div",{class:[`${t}-image-preview-container`,this.themeClass],style:this.cssVars,onWheel:this.handleWheel},n(Ce,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?n("div",{class:`${t}-image-preview-overlay`,onClick:this.toggleShow}):null}),this.showToolbar?n(Ce,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?n("div",{class:`${t}-image-preview-toolbar`},r?r({nodes:{prev:s,next:d,rotateCounterclockwise:c,rotateClockwise:u,resizeToOriginalSize:i,zoomOut:a,zoomIn:L,download:g,close:b}}):n(ve,null,this.onPrev?n(ve,null,s,d):null,c,u,i,a,L,g,b)):null}):null,n(Ce,{name:"fade-in-scale-up-transition",onAfterLeave:this.handleAfterLeave,appear:this.appear,onEnter:this.syncTransformOrigin,onBeforeLeave:this.syncTransformOrigin},{default:()=>{const{previewedImgProps:A={}}=this;return _e(n("div",{class:`${t}-image-preview-wrapper`,ref:"previewWrapperRef"},n("img",Object.assign({},A,{draggable:!1,onMousedown:this.handlePreviewMousedown,onDblclick:this.handlePreviewDblclick,class:[`${t}-image-preview`,A.class],key:this.previewSrc,src:this.previewSrc,ref:"previewRef",onDragstart:this.handleDragStart}))),[[Bt,this.show]])}})),[[zt,{enabled:this.show}]])):null}}))}}),wt=Se("n-image-group"),dn=ze,cn=Q({name:"ImageGroup",props:dn,setup(e){let o;const{mergedClsPrefixRef:t}=ue(e),r=`c${Te()}`,l=Nt(),s=_(null),d=u=>{var i;o=u,(i=s.value)===null||i===void 0||i.setPreviewSrc(u)};function c(u){var i,a;if(!(l!=null&&l.proxy))return;const b=l.proxy.$el.parentElement.querySelectorAll(`[data-group-id=${r}]:not([data-error=true])`);if(!b.length)return;const L=Array.from(b).findIndex(y=>y.dataset.previewSrc===o);~L?d(b[(L+u+b.length)%b.length].dataset.previewSrc):d(b[0].dataset.previewSrc),u===1?(i=e.onPreviewNext)===null||i===void 0||i.call(e):(a=e.onPreviewPrev)===null||a===void 0||a.call(e)}return Ie(wt,{mergedClsPrefixRef:t,setPreviewSrc:d,setThumbnailEl:u=>{var i;(i=s.value)===null||i===void 0||i.setThumbnailEl(u)},toggleShow:()=>{var u;(u=s.value)===null||u===void 0||u.toggleShow()},groupId:r,renderToolbarRef:P(e,"renderToolbar")}),{mergedClsPrefix:t,previewInstRef:s,next:()=>{c(1)},prev:()=>{c(-1)}}},render(){return n(mt,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:this.mergedClsPrefix,ref:"previewInstRef",onPrev:this.prev,onNext:this.next,showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip,renderToolbar:this.renderToolbar},this.$slots)}}),fn=Object.assign({alt:String,height:[String,Number],imgProps:Object,previewedImgProps:Object,lazy:Boolean,intersectionObserverOptions:Object,objectFit:{type:String,default:"fill"},previewSrc:String,fallbackSrc:String,width:[String,Number],src:String,previewDisabled:Boolean,loadDescription:String,onError:Function,onLoad:Function},ze),hn=Q({name:"Image",props:fn,slots:Object,inheritAttrs:!1,setup(e){const o=_(null),t=_(!1),r=_(null),l=ne(wt,null),{mergedClsPrefixRef:s}=l||ue(e),d={click:()=>{if(e.previewDisabled||t.value)return;const i=e.previewSrc||e.src;if(l){l.setPreviewSrc(i),l.setThumbnailEl(o.value),l.toggleShow();return}const{value:a}=r;a&&(a.setPreviewSrc(i),a.setThumbnailEl(o.value),a.toggleShow())}},c=_(!e.lazy);Ae(()=>{var i;(i=o.value)===null||i===void 0||i.setAttribute("data-group-id",(l==null?void 0:l.groupId)||"")}),Ae(()=>{if(e.lazy&&e.intersectionObserverOptions){let i;const a=Pe(()=>{i==null||i(),i=void 0,i=on(o.value,e.intersectionObserverOptions,c)});et(()=>{a(),i==null||i()})}}),Pe(()=>{var i;e.src||((i=e.imgProps)===null||i===void 0||i.src),t.value=!1});const u=_(!1);return Ie(pt,{previewedImgPropsRef:P(e,"previewedImgProps")}),Object.assign({mergedClsPrefix:s,groupId:l==null?void 0:l.groupId,previewInstRef:r,imageRef:o,showError:t,shouldStartLoading:c,loaded:u,mergedOnClick:i=>{var a,g;d.click(),(g=(a=e.imgProps)===null||a===void 0?void 0:a.onClick)===null||g===void 0||g.call(a,i)},mergedOnError:i=>{if(!c.value)return;t.value=!0;const{onError:a,imgProps:{onError:g}={}}=e;a==null||a(i),g==null||g(i)},mergedOnLoad:i=>{const{onLoad:a,imgProps:{onLoad:g}={}}=e;a==null||a(i),g==null||g(i),u.value=!0}},d)},render(){var e,o;const{mergedClsPrefix:t,imgProps:r={},loaded:l,$attrs:s,lazy:d}=this,c=Je(this.$slots.error,()=>[]),u=(o=(e=this.$slots).placeholder)===null||o===void 0?void 0:o.call(e),i=this.src||r.src,a=this.showError&&c.length?c:n("img",Object.assign(Object.assign({},r),{ref:"imageRef",width:this.width||r.width,height:this.height||r.height,src:this.showError?this.fallbackSrc:d&&this.intersectionObserverOptions?this.shouldStartLoading?i:void 0:i,alt:this.alt||r.alt,"aria-label":this.alt||r.alt,onClick:this.mergedOnClick,onError:this.mergedOnError,onLoad:this.mergedOnLoad,loading:en&&d&&!this.intersectionObserverOptions?"lazy":"eager",style:[r.style||"",u&&!l?{height:"0",width:"0",visibility:"hidden"}:"",{objectFit:this.objectFit}],"data-error":this.showError,"data-preview-src":this.previewSrc||this.src}));return n("div",Object.assign({},s,{role:"none",class:[s.class,`${t}-image`,(this.previewDisabled||this.showError)&&`${t}-image--preview-disabled`]}),this.groupId?a:n(mt,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:t,ref:"previewInstRef",showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip,renderToolbar:this.renderToolbar},{default:()=>a}),!l&&u)}}),le=Se("n-upload"),gn=U([w("upload","width: 100%;",[N("dragger-inside",[w("upload-trigger",`
 display: block;
 `)]),N("drag-over",[w("upload-dragger",`
 border: var(--n-dragger-border-hover);
 `)])]),w("upload-dragger",`
 cursor: pointer;
 box-sizing: border-box;
 width: 100%;
 text-align: center;
 border-radius: var(--n-border-radius);
 padding: 24px;
 opacity: 1;
 transition:
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 background-color: var(--n-dragger-color);
 border: var(--n-dragger-border);
 `,[U("&:hover",`
 border: var(--n-dragger-border-hover);
 `),N("disabled",`
 cursor: not-allowed;
 `)]),w("upload-trigger",`
 display: inline-block;
 box-sizing: border-box;
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 `,[U("+",[w("upload-file-list","margin-top: 8px;")]),N("disabled",`
 opacity: var(--n-item-disabled-opacity);
 cursor: not-allowed;
 `),N("image-card",`
 width: 96px;
 height: 96px;
 `,[w("base-icon",`
 font-size: 24px;
 `),w("upload-dragger",`
 padding: 0;
 height: 100%;
 width: 100%;
 display: flex;
 align-items: center;
 justify-content: center;
 `)])]),w("upload-file-list",`
 line-height: var(--n-line-height);
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 `,[U("a, img","outline: none;"),N("disabled",`
 opacity: var(--n-item-disabled-opacity);
 cursor: not-allowed;
 `,[w("upload-file","cursor: not-allowed;")]),N("grid",`
 display: grid;
 grid-template-columns: repeat(auto-fill, 96px);
 grid-gap: 8px;
 margin-top: 0;
 `),w("upload-file",`
 display: block;
 box-sizing: border-box;
 cursor: default;
 padding: 0px 12px 0 6px;
 transition: background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 `,[Ee(),w("progress",[Ee({foldPadding:!0})]),U("&:hover",`
 background-color: var(--n-item-color-hover);
 `,[w("upload-file-info",[J("action",`
 opacity: 1;
 `)])]),N("image-type",`
 border-radius: var(--n-border-radius);
 text-decoration: underline;
 text-decoration-color: #0000;
 `,[w("upload-file-info",`
 padding-top: 0px;
 padding-bottom: 0px;
 width: 100%;
 height: 100%;
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 6px 0;
 `,[w("progress",`
 padding: 2px 0;
 margin-bottom: 0;
 `),J("name",`
 padding: 0 8px;
 `),J("thumbnail",`
 width: 32px;
 height: 32px;
 font-size: 28px;
 display: flex;
 justify-content: center;
 align-items: center;
 `,[U("img",`
 width: 100%;
 `)])])]),N("text-type",[w("progress",`
 box-sizing: border-box;
 padding-bottom: 6px;
 margin-bottom: 6px;
 `)]),N("image-card-type",`
 position: relative;
 width: 96px;
 height: 96px;
 border: var(--n-item-border-image-card);
 border-radius: var(--n-border-radius);
 padding: 0;
 display: flex;
 align-items: center;
 justify-content: center;
 transition: border-color .3s var(--n-bezier), background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 overflow: hidden;
 `,[w("progress",`
 position: absolute;
 left: 8px;
 bottom: 8px;
 right: 8px;
 width: unset;
 `),w("upload-file-info",`
 padding: 0;
 width: 100%;
 height: 100%;
 `,[J("thumbnail",`
 width: 100%;
 height: 100%;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 font-size: 36px;
 `,[U("img",`
 width: 100%;
 `)])]),U("&::before",`
 position: absolute;
 z-index: 1;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
 opacity: 0;
 transition: opacity .2s var(--n-bezier);
 content: "";
 `),U("&:hover",[U("&::before","opacity: 1;"),w("upload-file-info",[J("thumbnail","opacity: .12;")])])]),N("error-status",[U("&:hover",`
 background-color: var(--n-item-color-hover-error);
 `),w("upload-file-info",[J("name","color: var(--n-item-text-color-error);"),J("thumbnail","color: var(--n-item-text-color-error);")]),N("image-card-type",`
 border: var(--n-item-border-image-card-error);
 `)]),N("with-url",`
 cursor: pointer;
 `,[w("upload-file-info",[J("name",`
 color: var(--n-item-text-color-success);
 text-decoration-color: var(--n-item-text-color-success);
 `,[U("a",`
 text-decoration: underline;
 `)])])]),w("upload-file-info",`
 position: relative;
 padding-top: 6px;
 padding-bottom: 6px;
 display: flex;
 flex-wrap: nowrap;
 `,[J("thumbnail",`
 font-size: 18px;
 opacity: 1;
 transition: opacity .2s var(--n-bezier);
 color: var(--n-item-icon-color);
 `,[w("base-icon",`
 margin-right: 2px;
 vertical-align: middle;
 transition: color .3s var(--n-bezier);
 `)]),J("action",`
 padding-top: inherit;
 padding-bottom: inherit;
 position: absolute;
 right: 0;
 top: 0;
 bottom: 0;
 width: 80px;
 display: flex;
 align-items: center;
 transition: opacity .2s var(--n-bezier);
 justify-content: flex-end;
 opacity: 0;
 `,[w("button",[U("&:not(:last-child)",{marginRight:"4px"}),w("base-icon",[U("svg",[Ut()])])]),N("image-type",`
 position: relative;
 max-width: 80px;
 width: auto;
 `),N("image-card-type",`
 z-index: 2;
 position: absolute;
 width: 100%;
 height: 100%;
 left: 0;
 right: 0;
 bottom: 0;
 top: 0;
 display: flex;
 justify-content: center;
 align-items: center;
 `)]),J("name",`
 color: var(--n-item-text-color);
 flex: 1;
 display: flex;
 justify-content: center;
 text-overflow: ellipsis;
 overflow: hidden;
 flex-direction: column;
 text-decoration-color: #0000;
 font-size: var(--n-font-size);
 transition:
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier); 
 `,[U("a",`
 color: inherit;
 text-decoration: underline;
 `)])])])]),w("upload-file-input",`
 display: none;
 width: 0;
 height: 0;
 opacity: 0;
 `)]),bt="__UPLOAD_DRAGGER__",vn=Q({name:"UploadDragger",[bt]:!0,setup(e,{slots:o}){const t=ne(le,null);return t||pe("upload-dragger","`n-upload-dragger` must be placed inside `n-upload`."),()=>{const{mergedClsPrefixRef:{value:r},mergedDisabledRef:{value:l},maxReachedRef:{value:s}}=t;return n("div",{class:[`${r}-upload-dragger`,(l||s)&&`${r}-upload-dragger--disabled`]},o)}}}),pn=n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 28 28"},n("g",{fill:"none"},n("path",{d:"M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z",fill:"currentColor"}))),mn=n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 28 28"},n("g",{fill:"none"},n("path",{d:"M6.4 2A2.4 2.4 0 0 0 4 4.4v19.2A2.4 2.4 0 0 0 6.4 26h15.2a2.4 2.4 0 0 0 2.4-2.4V11.578c0-.729-.29-1.428-.805-1.944l-6.931-6.931A2.4 2.4 0 0 0 14.567 2H6.4zm-.9 2.4a.9.9 0 0 1 .9-.9H14V10a2 2 0 0 0 2 2h6.5v11.6a.9.9 0 0 1-.9.9H6.4a.9.9 0 0 1-.9-.9V4.4zm16.44 6.1H16a.5.5 0 0 1-.5-.5V4.06l6.44 6.44z",fill:"currentColor"}))),wn=Q({name:"UploadProgress",props:{show:Boolean,percentage:{type:Number,required:!0},status:{type:String,required:!0}},setup(){return{mergedTheme:ne(le).mergedThemeRef}},render(){return n(Qe,null,{default:()=>this.show?n(Wt,{type:"line",showIndicator:!1,percentage:this.percentage,status:this.status,height:2,theme:this.mergedTheme.peers.Progress,themeOverrides:this.mergedTheme.peerOverrides.Progress}):null})}});var ke=function(e,o,t,r){function l(s){return s instanceof t?s:new t(function(d){d(s)})}return new(t||(t=Promise))(function(s,d){function c(a){try{i(r.next(a))}catch(g){d(g)}}function u(a){try{i(r.throw(a))}catch(g){d(g)}}function i(a){a.done?s(a.value):l(a.value).then(c,u)}i((r=r.apply(e,o||[])).next())})};function xt(e){return e.includes("image/")}function Ve(e=""){const o=e.split("/"),r=o[o.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(r)||[""])[0]}const We=/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico)$/i,Ct=e=>{if(e.type)return xt(e.type);const o=Ve(e.name||"");if(We.test(o))return!0;const t=e.thumbnailUrl||e.url||"",r=Ve(t);return!!(/^data:image\//.test(t)||We.test(r))};function bn(e){return ke(this,void 0,void 0,function*(){return yield new Promise(o=>{if(!e.type||!xt(e.type)){o("");return}o(window.URL.createObjectURL(e))})})}const xn=Ge&&window.FileReader&&window.File;function Cn(e){return e.isDirectory}function yn(e){return e.isFile}function Rn(e,o){return ke(this,void 0,void 0,function*(){const t=[];function r(l){return ke(this,void 0,void 0,function*(){for(const s of l)if(s){if(o&&Cn(s)){const d=s.createReader();let c=[],u;try{do u=yield new Promise((i,a)=>{d.readEntries(i,a)}),c=c.concat(u);while(u.length>0)}catch(i){Fe("upload","error happens when handling directory upload",i)}yield r(c)}else if(yn(s))try{const d=yield new Promise((c,u)=>{s.file(c,u)});t.push({file:d,entry:s,source:"dnd"})}catch(d){Fe("upload","error happens when handling file upload",d)}}})}return yield r(e),t})}function se(e){const{id:o,name:t,percentage:r,status:l,url:s,file:d,thumbnailUrl:c,type:u,fullPath:i,batchId:a}=e;return{id:o,name:t,percentage:r!=null?r:null,status:l,url:s!=null?s:null,file:d!=null?d:null,thumbnailUrl:c!=null?c:null,type:u!=null?u:null,fullPath:i!=null?i:null,batchId:a!=null?a:null}}function On(e,o,t){return e=e.toLowerCase(),o=o.toLocaleLowerCase(),t=t.toLocaleLowerCase(),t.split(",").map(l=>l.trim()).filter(Boolean).some(l=>{if(l.startsWith(".")){if(e.endsWith(l))return!0}else if(l.includes("/")){const[s,d]=o.split("/"),[c,u]=l.split("/");if((c==="*"||s&&c&&c===s)&&(u==="*"||d&&u&&u===d))return!0}else return!0;return!1})}var Xe=function(e,o,t,r){function l(s){return s instanceof t?s:new t(function(d){d(s)})}return new(t||(t=Promise))(function(s,d){function c(a){try{i(r.next(a))}catch(g){d(g)}}function u(a){try{i(r.throw(a))}catch(g){d(g)}}function i(a){a.done?s(a.value):l(a.value).then(c,u)}i((r=r.apply(e,o||[])).next())})};const ge={paddingMedium:"0 3px",heightMedium:"24px",iconSizeMedium:"18px"},Ln=Q({name:"UploadFile",props:{clsPrefix:{type:String,required:!0},file:{type:Object,required:!0},listType:{type:String,required:!0},index:{type:Number,required:!0}},setup(e){const o=ne(le),t=_(null),r=_(""),l=j(()=>{const{file:p}=e;return p.status==="finished"?"success":p.status==="error"?"error":"info"}),s=j(()=>{const{file:p}=e;if(p.status==="error")return"error"}),d=j(()=>{const{file:p}=e;return p.status==="uploading"}),c=j(()=>{if(!o.showCancelButtonRef.value)return!1;const{file:p}=e;return["uploading","pending","error"].includes(p.status)}),u=j(()=>{if(!o.showRemoveButtonRef.value)return!1;const{file:p}=e;return["finished"].includes(p.status)}),i=j(()=>{if(!o.showDownloadButtonRef.value)return!1;const{file:p}=e;return["finished"].includes(p.status)}),a=j(()=>{if(!o.showRetryButtonRef.value)return!1;const{file:p}=e;return["error"].includes(p.status)}),g=Ft(()=>r.value||e.file.thumbnailUrl||e.file.url),b=j(()=>{if(!o.showPreviewButtonRef.value)return!1;const{file:{status:p},listType:C}=e;return["finished"].includes(p)&&g.value&&C==="image-card"});function L(){return Xe(this,void 0,void 0,function*(){const p=o.onRetryRef.value;p&&(yield p({file:e.file}))===!1||o.submit(e.file.id)})}function y(p){p.preventDefault();const{file:C}=e;["finished","pending","error"].includes(C.status)?V(C):["uploading"].includes(C.status)?F(C):jt("upload","The button clicked type is unknown.")}function A(p){p.preventDefault(),$(e.file)}function V(p){const{xhrMap:C,doChange:x,onRemoveRef:{value:B},mergedFileListRef:{value:v}}=o;Promise.resolve(B?B({file:Object.assign({},p),fileList:v,index:e.index}):!0).then(T=>{if(T===!1)return;const R=Object.assign({},p,{status:"removed"});C.delete(p.id),x(R,void 0,{remove:!0})})}function $(p){const{onDownloadRef:{value:C}}=o;Promise.resolve(C?C(Object.assign({},p)):!0).then(x=>{x!==!1&&tt(p.url,p.name)})}function F(p){const{xhrMap:C}=o,x=C.get(p.id);x==null||x.abort(),V(Object.assign({},p))}function Y(p){const{onPreviewRef:{value:C}}=o;if(C)C(e.file,{event:p});else if(e.listType==="image-card"){const{value:x}=t;if(!x)return;x.click()}}const z=()=>Xe(this,void 0,void 0,function*(){const{listType:p}=e;p!=="image"&&p!=="image-card"||o.shouldUseThumbnailUrlRef.value(e.file)&&(r.value=yield o.getFileThumbnailUrlResolver(e.file))});return Pe(()=>{z()}),{mergedTheme:o.mergedThemeRef,progressStatus:l,buttonType:s,showProgress:d,disabled:o.mergedDisabledRef,showCancelButton:c,showRemoveButton:u,showDownloadButton:i,showRetryButton:a,showPreviewButton:b,mergedThumbnailUrl:g,shouldUseThumbnailUrl:o.shouldUseThumbnailUrlRef,renderIcon:o.renderIconRef,imageRef:t,handleRemoveOrCancelClick:y,handleDownloadClick:A,handleRetryClick:L,handlePreviewClick:Y}},render(){const{clsPrefix:e,mergedTheme:o,listType:t,file:r,renderIcon:l}=this;let s;const d=t==="image";d||t==="image-card"?s=!this.shouldUseThumbnailUrl(r)||!this.mergedThumbnailUrl?n("span",{class:`${e}-upload-file-info__thumbnail`},l?l(r):Ct(r)?n(E,{clsPrefix:e},{default:pn}):n(E,{clsPrefix:e},{default:mn})):n("a",{rel:"noopener noreferer",target:"_blank",href:r.url||void 0,class:`${e}-upload-file-info__thumbnail`,onClick:this.handlePreviewClick},t==="image-card"?n(hn,{src:this.mergedThumbnailUrl||void 0,previewSrc:r.url||void 0,alt:r.name,ref:"imageRef"}):n("img",{src:this.mergedThumbnailUrl||void 0,alt:r.name})):s=n("span",{class:`${e}-upload-file-info__thumbnail`},l?l(r):n(E,{clsPrefix:e},{default:()=>n(Vo,null)}));const u=n(wn,{show:this.showProgress,percentage:r.percentage||0,status:this.progressStatus}),i=t==="text"||t==="image";return n("div",{class:[`${e}-upload-file`,`${e}-upload-file--${this.progressStatus}-status`,r.url&&r.status!=="error"&&t!=="image-card"&&`${e}-upload-file--with-url`,`${e}-upload-file--${t}-type`]},n("div",{class:`${e}-upload-file-info`},s,n("div",{class:`${e}-upload-file-info__name`},i&&(r.url&&r.status!=="error"?n("a",{rel:"noopener noreferer",target:"_blank",href:r.url||void 0,onClick:this.handlePreviewClick},r.name):n("span",{onClick:this.handlePreviewClick},r.name)),d&&u),n("div",{class:[`${e}-upload-file-info__action`,`${e}-upload-file-info__action--${t}-type`]},this.showPreviewButton?n(fe,{key:"preview",quaternary:!0,type:this.buttonType,onClick:this.handlePreviewClick,theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,builtinThemeOverrides:ge},{icon:()=>n(E,{clsPrefix:e},{default:()=>n(Kt,null)})}):null,(this.showRemoveButton||this.showCancelButton)&&!this.disabled&&n(fe,{key:"cancelOrTrash",theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,quaternary:!0,builtinThemeOverrides:ge,type:this.buttonType,onClick:this.handleRemoveOrCancelClick},{icon:()=>n(Et,null,{default:()=>this.showRemoveButton?n(E,{clsPrefix:e,key:"trash"},{default:()=>n(Ko,null)}):n(E,{clsPrefix:e,key:"cancel"},{default:()=>n(Wo,null)})})}),this.showRetryButton&&!this.disabled&&n(fe,{key:"retry",quaternary:!0,type:this.buttonType,onClick:this.handleRetryClick,theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,builtinThemeOverrides:ge},{icon:()=>n(E,{clsPrefix:e},{default:()=>n(qo,null)})}),this.showDownloadButton?n(fe,{key:"download",quaternary:!0,type:this.buttonType,onClick:this.handleDownloadClick,theme:o.peers.Button,themeOverrides:o.peerOverrides.Button,builtinThemeOverrides:ge},{icon:()=>n(E,{clsPrefix:e},{default:()=>n(vt,null)})}):null)),!d&&u)}}),yt=Q({name:"UploadTrigger",props:{abstract:Boolean},slots:Object,setup(e,{slots:o}){const t=ne(le,null);t||pe("upload-trigger","`n-upload-trigger` must be placed inside `n-upload`.");const{mergedClsPrefixRef:r,mergedDisabledRef:l,maxReachedRef:s,listTypeRef:d,dragOverRef:c,openOpenFileDialog:u,draggerInsideRef:i,handleFileAddition:a,mergedDirectoryDndRef:g,triggerClassRef:b,triggerStyleRef:L}=t,y=j(()=>d.value==="image-card");function A(){l.value||s.value||u()}function V(z){z.preventDefault(),c.value=!0}function $(z){z.preventDefault(),c.value=!0}function F(z){z.preventDefault(),c.value=!1}function Y(z){var p;if(z.preventDefault(),!i.value||l.value||s.value){c.value=!1;return}const C=(p=z.dataTransfer)===null||p===void 0?void 0:p.items;C!=null&&C.length?Rn(Array.from(C).map(x=>x.webkitGetAsEntry()),g.value).then(x=>{a(x)}).finally(()=>{c.value=!1}):c.value=!1}return()=>{var z;const{value:p}=r;return e.abstract?(z=o.default)===null||z===void 0?void 0:z.call(o,{handleClick:A,handleDrop:Y,handleDragOver:V,handleDragEnter:$,handleDragLeave:F}):n("div",{class:[`${p}-upload-trigger`,(l.value||s.value)&&`${p}-upload-trigger--disabled`,y.value&&`${p}-upload-trigger--image-card`,b.value],style:L.value,onClick:A,onDrop:Y,onDragover:V,onDragenter:$,onDragleave:F},y.value?n(vn,null,{default:()=>Je(o.default,()=>[n(E,{clsPrefix:p},{default:()=>n(Jt,null)})])}):o)}}}),Tn=Q({name:"UploadFileList",setup(e,{slots:o}){const t=ne(le,null);t||pe("upload-file-list","`n-upload-file-list` must be placed inside `n-upload`.");const{abstractRef:r,mergedClsPrefixRef:l,listTypeRef:s,mergedFileListRef:d,fileListClassRef:c,fileListStyleRef:u,cssVarsRef:i,themeClassRef:a,maxReachedRef:g,showTriggerRef:b,imageGroupPropsRef:L}=t,y=j(()=>s.value==="image-card"),A=()=>d.value.map(($,F)=>n(Ln,{clsPrefix:l.value,key:$.id,file:$,index:F,listType:s.value})),V=()=>y.value?n(cn,Object.assign({},L.value),{default:A}):n(Qe,{group:!0},{default:A});return()=>{const{value:$}=l,{value:F}=r;return n("div",{class:[`${$}-upload-file-list`,y.value&&`${$}-upload-file-list--grid`,F?a==null?void 0:a.value:void 0,c.value],style:[F&&i?i.value:"",u.value]},V(),b.value&&!g.value&&y.value&&n(yt,null,o))}}});var qe=function(e,o,t,r){function l(s){return s instanceof t?s:new t(function(d){d(s)})}return new(t||(t=Promise))(function(s,d){function c(a){try{i(r.next(a))}catch(g){d(g)}}function u(a){try{i(r.throw(a))}catch(g){d(g)}}function i(a){a.done?s(a.value):l(a.value).then(c,u)}i((r=r.apply(e,o||[])).next())})};function Pn(e,o,t){const{doChange:r,xhrMap:l}=e;let s=0;function d(u){var i;let a=Object.assign({},o,{status:"error",percentage:s});l.delete(o.id),a=se(((i=e.onError)===null||i===void 0?void 0:i.call(e,{file:a,event:u}))||a),r(a,u)}function c(u){var i;if(e.isErrorState){if(e.isErrorState(t)){d(u);return}}else if(t.status<200||t.status>=300){d(u);return}let a=Object.assign({},o,{status:"finished",percentage:s});l.delete(o.id),a=se(((i=e.onFinish)===null||i===void 0?void 0:i.call(e,{file:a,event:u}))||a),r(a,u)}return{handleXHRLoad:c,handleXHRError:d,handleXHRAbort(u){const i=Object.assign({},o,{status:"removed",file:null,percentage:s});l.delete(o.id),r(i,u)},handleXHRProgress(u){const i=Object.assign({},o,{status:"uploading"});if(u.lengthComputable){const a=Math.ceil(u.loaded/u.total*100);i.percentage=a,s=a}r(i,u)}}}function kn(e){const{inst:o,file:t,data:r,headers:l,withCredentials:s,action:d,customRequest:c}=e,{doChange:u}=e.inst;let i=0;c({file:t,data:r,headers:l,withCredentials:s,action:d,onProgress(a){const g=Object.assign({},t,{status:"uploading"}),b=a.percent;g.percentage=b,i=b,u(g)},onFinish(){var a;let g=Object.assign({},t,{status:"finished",percentage:i});g=se(((a=o.onFinish)===null||a===void 0?void 0:a.call(o,{file:g}))||g),u(g)},onError(){var a;let g=Object.assign({},t,{status:"error",percentage:i});g=se(((a=o.onError)===null||a===void 0?void 0:a.call(o,{file:g}))||g),u(g)}})}function Sn(e,o,t){const r=Pn(e,o,t);t.onabort=r.handleXHRAbort,t.onerror=r.handleXHRError,t.onload=r.handleXHRLoad,t.upload&&(t.upload.onprogress=r.handleXHRProgress)}function Rt(e,o){return typeof e=="function"?e({file:o}):e||{}}function In(e,o,t){const r=Rt(o,t);r&&Object.keys(r).forEach(l=>{e.setRequestHeader(l,r[l])})}function zn(e,o,t){const r=Rt(o,t);r&&Object.keys(r).forEach(l=>{e.append(l,r[l])})}function Bn(e,o,t,{method:r,action:l,withCredentials:s,responseType:d,headers:c,data:u}){const i=new XMLHttpRequest;i.responseType=d,e.xhrMap.set(t.id,i),i.withCredentials=s;const a=new FormData;if(zn(a,u,t),t.file!==null&&a.append(o,t.file),Sn(e,t,i),l!==void 0){i.open(r.toUpperCase(),l),In(i,c,t),i.send(a);const g=Object.assign({},t,{status:"uploading"});e.doChange(g)}}const Dn=Object.assign(Object.assign({},ae.props),{name:{type:String,default:"file"},accept:String,action:String,customRequest:Function,directory:Boolean,directoryDnd:{type:Boolean,default:void 0},method:{type:String,default:"POST"},multiple:Boolean,showFileList:{type:Boolean,default:!0},data:[Object,Function],headers:[Object,Function],withCredentials:Boolean,responseType:{type:String,default:""},disabled:{type:Boolean,default:void 0},onChange:Function,onRemove:Function,onFinish:Function,onError:Function,onRetry:Function,onBeforeUpload:Function,isErrorState:Function,onDownload:Function,defaultUpload:{type:Boolean,default:!0},fileList:Array,"onUpdate:fileList":[Function,Array],onUpdateFileList:[Function,Array],fileListClass:String,fileListStyle:[String,Object],defaultFileList:{type:Array,default:()=>[]},showCancelButton:{type:Boolean,default:!0},showRemoveButton:{type:Boolean,default:!0},showDownloadButton:Boolean,showRetryButton:{type:Boolean,default:!0},showPreviewButton:{type:Boolean,default:!0},listType:{type:String,default:"text"},onPreview:Function,shouldUseThumbnailUrl:{type:Function,default:e=>xn?Ct(e):!1},createThumbnailUrl:Function,abstract:Boolean,max:Number,showTrigger:{type:Boolean,default:!0},imageGroupProps:Object,inputProps:Object,triggerClass:String,triggerStyle:[String,Object],renderIcon:Function}),Zn=Q({name:"Upload",props:Dn,setup(e){e.abstract&&e.listType==="image-card"&&pe("upload","when the list-type is image-card, abstract is not supported.");const{mergedClsPrefixRef:o,inlineThemeDisabled:t}=ue(e),r=ae("Upload","-upload",gn,_t,e,o),l=At(e),s=_(e.defaultFileList),d=P(e,"fileList"),c=_(null),u={value:!1},i=_(!1),a=new Map,g=Qt(d,s),b=j(()=>g.value.map(se)),L=j(()=>{const{max:v}=e;return v!==void 0?b.value.length>=v:!1});function y(){var v;(v=c.value)===null||v===void 0||v.click()}function A(v){const T=v.target;Y(T.files?Array.from(T.files).map(R=>({file:R,entry:null,source:"input"})):null,v),T.value=""}function V(v){const{"onUpdate:fileList":T,onUpdateFileList:R}=e;T&&je(T,v),R&&je(R,v),s.value=v}const $=j(()=>e.multiple||e.directory),F=(v,T,R={append:!1,remove:!1})=>{const{append:O,remove:D}=R,H=Array.from(b.value),M=H.findIndex(I=>I.id===v.id);if(O||D||~M){O?H.push(v):D?H.splice(M,1):H.splice(M,1,v);const{onChange:I}=e;I&&I({file:v,fileList:H,event:T}),V(H)}};function Y(v,T){if(!v||v.length===0)return;const{onBeforeUpload:R}=e;v=$.value?v:[v[0]];const{max:O,accept:D}=e;v=v.filter(({file:M,source:I})=>I==="dnd"&&(D!=null&&D.trim())?On(M.name,M.type,D):!0),O&&(v=v.slice(0,O-b.value.length));const H=Te();Promise.all(v.map(M=>qe(this,[M],void 0,function*({file:I,entry:X}){var W;const ee={id:Te(),batchId:H,name:I.name,status:"pending",percentage:0,file:I,url:null,type:I.type,thumbnailUrl:null,fullPath:(W=X==null?void 0:X.fullPath)!==null&&W!==void 0?W:`/${I.webkitRelativePath||I.name}`};return!R||(yield R({file:ee,fileList:b.value}))!==!1?ee:null}))).then(M=>qe(this,void 0,void 0,function*(){let I=Promise.resolve();M.forEach(X=>{I=I.then(Vt).then(()=>{X&&F(X,T,{append:!0})})}),yield I})).then(()=>{e.defaultUpload&&z()})}function z(v){const{method:T,action:R,withCredentials:O,headers:D,data:H,name:M}=e,I=v!==void 0?b.value.filter(W=>W.id===v):b.value,X=v!==void 0;I.forEach(W=>{const{status:ee}=W;(ee==="pending"||ee==="error"&&X)&&(e.customRequest?kn({inst:{doChange:F,xhrMap:a,onFinish:e.onFinish,onError:e.onError},file:W,action:R,withCredentials:O,headers:D,data:H,customRequest:e.customRequest}):Bn({doChange:F,xhrMap:a,onFinish:e.onFinish,onError:e.onError,isErrorState:e.isErrorState},M,W,{method:T,action:R,withCredentials:O,responseType:e.responseType,headers:D,data:H}))})}function p(v){var T;if(v.thumbnailUrl)return v.thumbnailUrl;const{createThumbnailUrl:R}=e;return R?(T=R(v.file,v))!==null&&T!==void 0?T:v.url||"":v.url?v.url:v.file?bn(v.file):""}const C=j(()=>{const{common:{cubicBezierEaseInOut:v},self:{draggerColor:T,draggerBorder:R,draggerBorderHover:O,itemColorHover:D,itemColorHoverError:H,itemTextColorError:M,itemTextColorSuccess:I,itemTextColor:X,itemIconColor:W,itemDisabledOpacity:ee,lineHeight:me,borderRadius:de,fontSize:we,itemBorderImageCardError:be,itemBorderImageCard:xe}}=r.value;return{"--n-bezier":v,"--n-border-radius":de,"--n-dragger-border":R,"--n-dragger-border-hover":O,"--n-dragger-color":T,"--n-font-size":we,"--n-item-color-hover":D,"--n-item-color-hover-error":H,"--n-item-disabled-opacity":ee,"--n-item-icon-color":W,"--n-item-text-color":X,"--n-item-text-color-error":M,"--n-item-text-color-success":I,"--n-line-height":me,"--n-item-border-image-card-error":be,"--n-item-border-image-card":xe}}),x=t?Ke("upload",void 0,C,e):void 0;Ie(le,{mergedClsPrefixRef:o,mergedThemeRef:r,showCancelButtonRef:P(e,"showCancelButton"),showDownloadButtonRef:P(e,"showDownloadButton"),showRemoveButtonRef:P(e,"showRemoveButton"),showRetryButtonRef:P(e,"showRetryButton"),onRemoveRef:P(e,"onRemove"),onDownloadRef:P(e,"onDownload"),mergedFileListRef:b,triggerClassRef:P(e,"triggerClass"),triggerStyleRef:P(e,"triggerStyle"),shouldUseThumbnailUrlRef:P(e,"shouldUseThumbnailUrl"),renderIconRef:P(e,"renderIcon"),xhrMap:a,submit:z,doChange:F,showPreviewButtonRef:P(e,"showPreviewButton"),onPreviewRef:P(e,"onPreview"),getFileThumbnailUrlResolver:p,listTypeRef:P(e,"listType"),dragOverRef:i,openOpenFileDialog:y,draggerInsideRef:u,handleFileAddition:Y,mergedDisabledRef:l.mergedDisabledRef,maxReachedRef:L,fileListClassRef:P(e,"fileListClass"),fileListStyleRef:P(e,"fileListStyle"),abstractRef:P(e,"abstract"),acceptRef:P(e,"accept"),cssVarsRef:t?void 0:C,themeClassRef:x==null?void 0:x.themeClass,onRender:x==null?void 0:x.onRender,showTriggerRef:P(e,"showTrigger"),imageGroupPropsRef:P(e,"imageGroupProps"),mergedDirectoryDndRef:j(()=>{var v;return(v=e.directoryDnd)!==null&&v!==void 0?v:e.directory}),onRetryRef:P(e,"onRetry")});const B={clear:()=>{s.value=[]},submit:z,openOpenFileDialog:y};return Object.assign({mergedClsPrefix:o,draggerInsideRef:u,inputElRef:c,mergedTheme:r,dragOver:i,mergedMultiple:$,cssVars:t?void 0:C,themeClass:x==null?void 0:x.themeClass,onRender:x==null?void 0:x.onRender,handleFileInputChange:A},B)},render(){var e,o;const{draggerInsideRef:t,mergedClsPrefix:r,$slots:l,directory:s,onRender:d}=this;if(l.default&&!this.abstract){const u=l.default()[0];!((e=u==null?void 0:u.type)===null||e===void 0)&&e[bt]&&(t.value=!0)}const c=n("input",Object.assign({},this.inputProps,{ref:"inputElRef",type:"file",class:`${r}-upload-file-input`,accept:this.accept,multiple:this.mergedMultiple,onChange:this.handleFileInputChange,webkitdirectory:s||void 0,directory:s||void 0}));return this.abstract?n(ve,null,(o=l.default)===null||o===void 0?void 0:o.call(l),n(Zt,{to:"body"},c)):(d==null||d(),n("div",{class:[`${r}-upload`,t.value&&`${r}-upload--dragger-inside`,this.dragOver&&`${r}-upload--drag-over`,this.themeClass],style:this.cssVars},c,this.showTrigger&&this.listType!=="image-card"&&n(yt,null,l),this.showFileList&&n(Tn,null,l)))}});export{Zn as NUpload,vn as NUploadDragger,Tn as NUploadFileList,yt as NUploadTrigger,Dn as uploadProps};
