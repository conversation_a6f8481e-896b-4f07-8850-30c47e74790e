import{a6 as ie,P as se,bG as te,bH as oe,bI as or,bJ as ar,bK as ir,bL as ye,bM as F,bN as Xe,bO as Se,bm as sr,bP as je,bQ as G,bR as ge,bS as Te,bT as lr,bU as Pe,bV as Ee,bW as q,bX as ur,bY as Me,bZ as dr,b_ as fr,b$ as cr,c0 as vr,c1 as hr,c2 as gr,c3 as pr,n as D,l as B,p as le,m as ue,o as ee,c4 as wr,a9 as br,u as mr,b as Ae,ac as Re,I as yr,x as Sr,ae as xe,c5 as Ar,t as Be,c6 as $r,v as de,aU as Cr,al as Or,ak as _r,aj as Tr,c7 as Pr,aq as Er,c8 as Mr,ad as Rr,L as Ie,A as Y}from"./bootstrap-MyT3sENS.js";import{e as xr,u as re,B as Br,V as Ir}from"./Follower-B0-DuUBY.js";import{f as fe}from"./format-length-B-p6aW7q.js";import{c as U,d as Je,h as S,r as z,K as Ye,i as Dr,a0 as Lr,w as zr,t as Q,L as Fr,z as Ze,F as Nr,I as V,aH as Hr,aI as Wr}from"../jse/index-index-Y3_OtjO-.js";import{u as Kr}from"./use-merged-state-DFvgmEt8.js";function Ur(e,r){return U(()=>{for(const n of r)if(e[n]!==void 0)return e[n];return e[r[r.length-1]]})}const K="@@mmoContext",Gr={mounted(e,{value:r}){e[K]={handler:void 0},typeof r=="function"&&(e[K].handler=r,se("mousemoveoutside",e,r))},updated(e,{value:r}){const n=e[K];typeof r=="function"?n.handler?n.handler!==r&&(ie("mousemoveoutside",e,n.handler),n.handler=r,se("mousemoveoutside",e,r)):(e[K].handler=r,se("mousemoveoutside",e,r)):n.handler&&(ie("mousemoveoutside",e,n.handler),n.handler=void 0)},unmounted(e){const{handler:r}=e[K];r&&ie("mousemoveoutside",e,r),e[K].handler=void 0}};let ce;function kr(){return ce===void 0&&(ce=navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),ce}var pe=te(oe,"WeakMap"),Xr=or(Object.keys,Object),jr=Object.prototype,Jr=jr.hasOwnProperty;function Yr(e){if(!ar(e))return Xr(e);var r=[];for(var n in Object(e))Jr.call(e,n)&&n!="constructor"&&r.push(n);return r}function $e(e){return ye(e)?ir(e):Yr(e)}var Zr=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,qr=/^\w*$/;function Ce(e,r){if(F(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||Xe(e)?!0:qr.test(e)||!Zr.test(e)||r!=null&&e in Object(r)}var Qr="Expected a function";function Oe(e,r){if(typeof e!="function"||r!=null&&typeof r!="function")throw new TypeError(Qr);var n=function(){var t=arguments,a=r?r.apply(this,t):t[0],o=n.cache;if(o.has(a))return o.get(a);var i=e.apply(this,t);return n.cache=o.set(a,i)||o,i};return n.cache=new(Oe.Cache||Se),n}Oe.Cache=Se;var Vr=500;function en(e){var r=Oe(e,function(t){return n.size===Vr&&n.clear(),t}),n=r.cache;return r}var rn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,nn=/\\(\\)?/g,tn=en(function(e){var r=[];return e.charCodeAt(0)===46&&r.push(""),e.replace(rn,function(n,t,a,o){r.push(a?o.replace(nn,"$1"):t||n)}),r});function qe(e,r){return F(e)?e:Ce(e,r)?[e]:tn(sr(e))}function ae(e){if(typeof e=="string"||Xe(e))return e;var r=e+"";return r=="0"&&1/e==-1/0?"-0":r}function Qe(e,r){r=qe(r,e);for(var n=0,t=r.length;e!=null&&n<t;)e=e[ae(r[n++])];return n&&n==t?e:void 0}function on(e,r,n){var t=e==null?void 0:Qe(e,r);return t===void 0?n:t}function an(e,r){for(var n=-1,t=r.length,a=e.length;++n<t;)e[a+n]=r[n];return e}function sn(e,r){for(var n=-1,t=e==null?0:e.length,a=0,o=[];++n<t;){var i=e[n];r(i,n,e)&&(o[a++]=i)}return o}function ln(){return[]}var un=Object.prototype,dn=un.propertyIsEnumerable,De=Object.getOwnPropertySymbols,fn=De?function(e){return e==null?[]:(e=Object(e),sn(De(e),function(r){return dn.call(e,r)}))}:ln;function cn(e,r,n){var t=r(e);return F(e)?t:an(t,n(e))}function Le(e){return cn(e,$e,fn)}var we=te(oe,"DataView"),be=te(oe,"Promise"),me=te(oe,"Set"),ze="[object Map]",vn="[object Object]",Fe="[object Promise]",Ne="[object Set]",He="[object WeakMap]",We="[object DataView]",hn=G(we),gn=G(ge),pn=G(be),wn=G(me),bn=G(pe),L=je;(we&&L(new we(new ArrayBuffer(1)))!=We||ge&&L(new ge)!=ze||be&&L(be.resolve())!=Fe||me&&L(new me)!=Ne||pe&&L(new pe)!=He)&&(L=function(e){var r=je(e),n=r==vn?e.constructor:void 0,t=n?G(n):"";if(t)switch(t){case hn:return We;case gn:return ze;case pn:return Fe;case wn:return Ne;case bn:return He}return r});var mn="__lodash_hash_undefined__";function yn(e){return this.__data__.set(e,mn),this}function Sn(e){return this.__data__.has(e)}function ne(e){var r=-1,n=e==null?0:e.length;for(this.__data__=new Se;++r<n;)this.add(e[r])}ne.prototype.add=ne.prototype.push=yn;ne.prototype.has=Sn;function An(e,r){for(var n=-1,t=e==null?0:e.length;++n<t;)if(r(e[n],n,e))return!0;return!1}function $n(e,r){return e.has(r)}var Cn=1,On=2;function Ve(e,r,n,t,a,o){var i=n&Cn,s=e.length,u=r.length;if(s!=u&&!(i&&u>s))return!1;var d=o.get(e),c=o.get(r);if(d&&c)return d==r&&c==e;var p=-1,g=!0,b=n&On?new ne:void 0;for(o.set(e,r),o.set(r,e);++p<s;){var m=e[p],f=r[p];if(t)var T=i?t(f,m,p,r,e,o):t(m,f,p,e,r,o);if(T!==void 0){if(T)continue;g=!1;break}if(b){if(!An(r,function($,C){if(!$n(b,C)&&(m===$||a(m,$,n,t,o)))return b.push(C)})){g=!1;break}}else if(!(m===f||a(m,f,n,t,o))){g=!1;break}}return o.delete(e),o.delete(r),g}function _n(e){var r=-1,n=Array(e.size);return e.forEach(function(t,a){n[++r]=[a,t]}),n}function Tn(e){var r=-1,n=Array(e.size);return e.forEach(function(t){n[++r]=t}),n}var Pn=1,En=2,Mn="[object Boolean]",Rn="[object Date]",xn="[object Error]",Bn="[object Map]",In="[object Number]",Dn="[object RegExp]",Ln="[object Set]",zn="[object String]",Fn="[object Symbol]",Nn="[object ArrayBuffer]",Hn="[object DataView]",Ke=Te?Te.prototype:void 0,ve=Ke?Ke.valueOf:void 0;function Wn(e,r,n,t,a,o,i){switch(n){case Hn:if(e.byteLength!=r.byteLength||e.byteOffset!=r.byteOffset)return!1;e=e.buffer,r=r.buffer;case Nn:return!(e.byteLength!=r.byteLength||!o(new Pe(e),new Pe(r)));case Mn:case Rn:case In:return lr(+e,+r);case xn:return e.name==r.name&&e.message==r.message;case Dn:case zn:return e==r+"";case Bn:var s=_n;case Ln:var u=t&Pn;if(s||(s=Tn),e.size!=r.size&&!u)return!1;var d=i.get(e);if(d)return d==r;t|=En,i.set(e,r);var c=Ve(s(e),s(r),t,a,o,i);return i.delete(e),c;case Fn:if(ve)return ve.call(e)==ve.call(r)}return!1}var Kn=1,Un=Object.prototype,Gn=Un.hasOwnProperty;function kn(e,r,n,t,a,o){var i=n&Kn,s=Le(e),u=s.length,d=Le(r),c=d.length;if(u!=c&&!i)return!1;for(var p=u;p--;){var g=s[p];if(!(i?g in r:Gn.call(r,g)))return!1}var b=o.get(e),m=o.get(r);if(b&&m)return b==r&&m==e;var f=!0;o.set(e,r),o.set(r,e);for(var T=i;++p<u;){g=s[p];var $=e[g],C=r[g];if(t)var N=i?t(C,$,g,r,e,o):t($,C,g,e,r,o);if(!(N===void 0?$===C||a($,C,n,t,o):N)){f=!1;break}T||(T=g=="constructor")}if(f&&!T){var M=e.constructor,E=r.constructor;M!=E&&"constructor"in e&&"constructor"in r&&!(typeof M=="function"&&M instanceof M&&typeof E=="function"&&E instanceof E)&&(f=!1)}return o.delete(e),o.delete(r),f}var Xn=1,Ue="[object Arguments]",Ge="[object Array]",Z="[object Object]",jn=Object.prototype,ke=jn.hasOwnProperty;function Jn(e,r,n,t,a,o){var i=F(e),s=F(r),u=i?Ge:L(e),d=s?Ge:L(r);u=u==Ue?Z:u,d=d==Ue?Z:d;var c=u==Z,p=d==Z,g=u==d;if(g&&Ee(e)){if(!Ee(r))return!1;i=!0,c=!1}if(g&&!c)return o||(o=new q),i||ur(e)?Ve(e,r,n,t,a,o):Wn(e,r,u,n,t,a,o);if(!(n&Xn)){var b=c&&ke.call(e,"__wrapped__"),m=p&&ke.call(r,"__wrapped__");if(b||m){var f=b?e.value():e,T=m?r.value():r;return o||(o=new q),a(f,T,n,t,o)}}return g?(o||(o=new q),kn(e,r,n,t,a,o)):!1}function _e(e,r,n,t,a){return e===r?!0:e==null||r==null||!Me(e)&&!Me(r)?e!==e&&r!==r:Jn(e,r,n,t,_e,a)}var Yn=1,Zn=2;function qn(e,r,n,t){var a=n.length,o=a;if(e==null)return!o;for(e=Object(e);a--;){var i=n[a];if(i[2]?i[1]!==e[i[0]]:!(i[0]in e))return!1}for(;++a<o;){i=n[a];var s=i[0],u=e[s],d=i[1];if(i[2]){if(u===void 0&&!(s in e))return!1}else{var c=new q,p;if(!(p===void 0?_e(d,u,Yn|Zn,t,c):p))return!1}}return!0}function er(e){return e===e&&!dr(e)}function Qn(e){for(var r=$e(e),n=r.length;n--;){var t=r[n],a=e[t];r[n]=[t,a,er(a)]}return r}function rr(e,r){return function(n){return n==null?!1:n[e]===r&&(r!==void 0||e in Object(n))}}function Vn(e){var r=Qn(e);return r.length==1&&r[0][2]?rr(r[0][0],r[0][1]):function(n){return n===e||qn(n,e,r)}}function et(e,r){return e!=null&&r in Object(e)}function rt(e,r,n){r=qe(r,e);for(var t=-1,a=r.length,o=!1;++t<a;){var i=ae(r[t]);if(!(o=e!=null&&n(e,i)))break;e=e[i]}return o||++t!=a?o:(a=e==null?0:e.length,!!a&&fr(a)&&cr(i,a)&&(F(e)||vr(e)))}function nt(e,r){return e!=null&&rt(e,r,et)}var tt=1,ot=2;function at(e,r){return Ce(e)&&er(r)?rr(ae(e),r):function(n){var t=on(n,e);return t===void 0&&t===r?nt(n,e):_e(r,t,tt|ot)}}function it(e){return function(r){return r==null?void 0:r[e]}}function st(e){return function(r){return Qe(r,e)}}function lt(e){return Ce(e)?it(ae(e)):st(e)}function ut(e){return typeof e=="function"?e:e==null?hr:typeof e=="object"?F(e)?at(e[0],e[1]):Vn(e):lt(e)}function dt(e,r){return e&&gr(e,r,$e)}function ft(e,r){return function(n,t){if(n==null)return n;if(!ye(n))return e(n,t);for(var a=n.length,o=-1,i=Object(n);++o<a&&t(i[o],o,i)!==!1;);return n}}var ct=ft(dt);function vt(e,r){var n=-1,t=ye(e)?Array(e.length):[];return ct(e,function(a,o,i){t[++n]=r(a,o,i)}),t}function ht(e,r){var n=F(e)?pr:vt;return n(e,ut(r))}const he={top:"bottom",bottom:"top",left:"right",right:"left"},w="var(--n-arrow-height) * 1.414",gt=D([B("popover",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 position: relative;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 box-shadow: var(--n-box-shadow);
 word-break: break-word;
 `,[D(">",[B("scrollbar",`
 height: inherit;
 max-height: inherit;
 `)]),le("raw",`
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 `,[le("scrollable",[le("show-header-or-footer","padding: var(--n-padding);")])]),ue("header",`
 padding: var(--n-padding);
 border-bottom: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),ue("footer",`
 padding: var(--n-padding);
 border-top: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),ee("scrollable, show-header-or-footer",[ue("content",`
 padding: var(--n-padding);
 `)])]),B("popover-shared",`
 transform-origin: inherit;
 `,[B("popover-arrow-wrapper",`
 position: absolute;
 overflow: hidden;
 pointer-events: none;
 `,[B("popover-arrow",`
 transition: background-color .3s var(--n-bezier);
 position: absolute;
 display: block;
 width: calc(${w});
 height: calc(${w});
 box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12);
 transform: rotate(45deg);
 background-color: var(--n-color);
 pointer-events: all;
 `)]),D("&.popover-transition-enter-from, &.popover-transition-leave-to",`
 opacity: 0;
 transform: scale(.85);
 `),D("&.popover-transition-enter-to, &.popover-transition-leave-from",`
 transform: scale(1);
 opacity: 1;
 `),D("&.popover-transition-enter-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-out),
 transform .15s var(--n-bezier-ease-out);
 `),D("&.popover-transition-leave-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-in),
 transform .15s var(--n-bezier-ease-in);
 `)]),P("top-start",`
 top: calc(${w} / -2);
 left: calc(${x("top-start")} - var(--v-offset-left));
 `),P("top",`
 top: calc(${w} / -2);
 transform: translateX(calc(${w} / -2)) rotate(45deg);
 left: 50%;
 `),P("top-end",`
 top: calc(${w} / -2);
 right: calc(${x("top-end")} + var(--v-offset-left));
 `),P("bottom-start",`
 bottom: calc(${w} / -2);
 left: calc(${x("bottom-start")} - var(--v-offset-left));
 `),P("bottom",`
 bottom: calc(${w} / -2);
 transform: translateX(calc(${w} / -2)) rotate(45deg);
 left: 50%;
 `),P("bottom-end",`
 bottom: calc(${w} / -2);
 right: calc(${x("bottom-end")} + var(--v-offset-left));
 `),P("left-start",`
 left: calc(${w} / -2);
 top: calc(${x("left-start")} - var(--v-offset-top));
 `),P("left",`
 left: calc(${w} / -2);
 transform: translateY(calc(${w} / -2)) rotate(45deg);
 top: 50%;
 `),P("left-end",`
 left: calc(${w} / -2);
 bottom: calc(${x("left-end")} + var(--v-offset-top));
 `),P("right-start",`
 right: calc(${w} / -2);
 top: calc(${x("right-start")} - var(--v-offset-top));
 `),P("right",`
 right: calc(${w} / -2);
 transform: translateY(calc(${w} / -2)) rotate(45deg);
 top: 50%;
 `),P("right-end",`
 right: calc(${w} / -2);
 bottom: calc(${x("right-end")} + var(--v-offset-top));
 `),...ht({top:["right-start","left-start"],right:["top-end","bottom-end"],bottom:["right-end","left-end"],left:["top-start","bottom-start"]},(e,r)=>{const n=["right","left"].includes(r),t=n?"width":"height";return e.map(a=>{const o=a.split("-")[1]==="end",s=`calc((${`var(--v-target-${t}, 0px)`} - ${w}) / 2)`,u=x(a);return D(`[v-placement="${a}"] >`,[B("popover-shared",[ee("center-arrow",[B("popover-arrow",`${r}: calc(max(${s}, ${u}) ${o?"+":"-"} var(--v-offset-${n?"left":"top"}));`)])])])})})]);function x(e){return["top","bottom"].includes(e.split("-")[0])?"var(--n-arrow-offset)":"var(--n-arrow-offset-vertical)"}function P(e,r){const n=e.split("-")[0],t=["top","bottom"].includes(n)?"height: var(--n-space-arrow);":"width: var(--n-space-arrow);";return D(`[v-placement="${e}"] >`,[B("popover-shared",`
 margin-${he[n]}: var(--n-space);
 `,[ee("show-arrow",`
 margin-${he[n]}: var(--n-space-arrow);
 `),ee("overlap",`
 margin: 0;
 `),wr("popover-arrow-wrapper",`
 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 ${n}: 100%;
 ${he[n]}: auto;
 ${t}
 `,[B("popover-arrow",r)])])])}const nr=Object.assign(Object.assign({},Ae.props),{to:re.propTo,show:Boolean,trigger:String,showArrow:Boolean,delay:Number,duration:Number,raw:Boolean,arrowPointToCenter:Boolean,arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],displayDirective:String,x:Number,y:Number,flip:Boolean,overlap:Boolean,placement:String,width:[Number,String],keepAliveOnHover:Boolean,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],internalDeactivateImmediately:Boolean,animated:Boolean,onClickoutside:Function,internalTrapFocus:Boolean,internalOnAfterLeave:Function,minWidth:Number,maxWidth:Number});function pt({arrowClass:e,arrowStyle:r,arrowWrapperClass:n,arrowWrapperStyle:t,clsPrefix:a}){return S("div",{key:"__popover-arrow__",style:t,class:[`${a}-popover-arrow-wrapper`,n]},S("div",{class:[`${a}-popover-arrow`,e],style:r}))}const wt=Je({name:"PopoverBody",inheritAttrs:!1,props:nr,setup(e,{slots:r,attrs:n}){const{namespaceRef:t,mergedClsPrefixRef:a,inlineThemeDisabled:o}=mr(e),i=Ae("Popover","-popover",gt,Ar,e,a),s=z(null),u=Dr("NPopover"),d=z(null),c=z(e.show),p=z(!1);Ye(()=>{const{show:v}=e;v&&!kr()&&!e.internalDeactivateImmediately&&(p.value=!0)});const g=U(()=>{const{trigger:v,onClickoutside:A}=e,O=[],{positionManuallyRef:{value:h}}=u;return h||(v==="click"&&!A&&O.push([Re,M,void 0,{capture:!0}]),v==="hover"&&O.push([Gr,N])),A&&O.push([Re,M,void 0,{capture:!0}]),(e.displayDirective==="show"||e.animated&&p.value)&&O.push([yr,e.show]),O}),b=U(()=>{const{common:{cubicBezierEaseInOut:v,cubicBezierEaseIn:A,cubicBezierEaseOut:O},self:{space:h,spaceArrow:X,padding:j,fontSize:I,textColor:J,dividerColor:l,color:y,boxShadow:_,borderRadius:H,arrowHeight:W,arrowOffset:R,arrowOffsetVertical:tr}}=i.value;return{"--n-box-shadow":_,"--n-bezier":v,"--n-bezier-ease-in":A,"--n-bezier-ease-out":O,"--n-font-size":I,"--n-text-color":J,"--n-color":y,"--n-divider-color":l,"--n-border-radius":H,"--n-arrow-height":W,"--n-arrow-offset":R,"--n-arrow-offset-vertical":tr,"--n-padding":j,"--n-space":h,"--n-space-arrow":X}}),m=U(()=>{const v=e.width==="trigger"?void 0:fe(e.width),A=[];v&&A.push({width:v});const{maxWidth:O,minWidth:h}=e;return O&&A.push({maxWidth:fe(O)}),h&&A.push({maxWidth:fe(h)}),o||A.push(b.value),A}),f=o?Sr("popover",void 0,b,e):void 0;u.setBodyInstance({syncPosition:T}),Lr(()=>{u.setBodyInstance(null)}),zr(Q(e,"show"),v=>{e.animated||(v?c.value=!0:c.value=!1)});function T(){var v;(v=s.value)===null||v===void 0||v.syncPosition()}function $(v){e.trigger==="hover"&&e.keepAliveOnHover&&e.show&&u.handleMouseEnter(v)}function C(v){e.trigger==="hover"&&e.keepAliveOnHover&&u.handleMouseLeave(v)}function N(v){e.trigger==="hover"&&!E().contains(xe(v))&&u.handleMouseMoveOutside(v)}function M(v){(e.trigger==="click"&&!E().contains(xe(v))||e.onClickoutside)&&u.handleClickOutside(v)}function E(){return u.getTriggerElement()}V(Or,d),V(_r,null),V(Tr,null);function k(){if(f==null||f.onRender(),!(e.displayDirective==="show"||e.show||e.animated&&p.value))return null;let A;const O=u.internalRenderBodyRef.value,{value:h}=a;if(O)A=O([`${h}-popover-shared`,f==null?void 0:f.themeClass.value,e.overlap&&`${h}-popover-shared--overlap`,e.showArrow&&`${h}-popover-shared--show-arrow`,e.arrowPointToCenter&&`${h}-popover-shared--center-arrow`],d,m.value,$,C);else{const{value:X}=u.extraClassRef,{internalTrapFocus:j}=e,I=!Be(r.header)||!Be(r.footer),J=()=>{var l,y;const _=I?S(Nr,null,de(r.header,R=>R?S("div",{class:[`${h}-popover__header`,e.headerClass],style:e.headerStyle},R):null),de(r.default,R=>R?S("div",{class:[`${h}-popover__content`,e.contentClass],style:e.contentStyle},r):null),de(r.footer,R=>R?S("div",{class:[`${h}-popover__footer`,e.footerClass],style:e.footerStyle},R):null)):e.scrollable?(l=r.default)===null||l===void 0?void 0:l.call(r):S("div",{class:[`${h}-popover__content`,e.contentClass],style:e.contentStyle},r),H=e.scrollable?S(Cr,{contentClass:I?void 0:`${h}-popover__content ${(y=e.contentClass)!==null&&y!==void 0?y:""}`,contentStyle:I?void 0:e.contentStyle},{default:()=>_}):_,W=e.showArrow?pt({arrowClass:e.arrowClass,arrowStyle:e.arrowStyle,arrowWrapperClass:e.arrowWrapperClass,arrowWrapperStyle:e.arrowWrapperStyle,clsPrefix:h}):null;return[H,W]};A=S("div",Fr({class:[`${h}-popover`,`${h}-popover-shared`,f==null?void 0:f.themeClass.value,X.map(l=>`${h}-${l}`),{[`${h}-popover--scrollable`]:e.scrollable,[`${h}-popover--show-header-or-footer`]:I,[`${h}-popover--raw`]:e.raw,[`${h}-popover-shared--overlap`]:e.overlap,[`${h}-popover-shared--show-arrow`]:e.showArrow,[`${h}-popover-shared--center-arrow`]:e.arrowPointToCenter}],ref:d,style:m.value,onKeydown:u.handleKeydown,onMouseenter:$,onMouseleave:C},n),j?S($r,{active:e.show,autoFocus:!0},{default:J}):J())}return Ze(A,g.value)}return{displayed:p,namespace:t,isMounted:u.isMountedRef,zIndex:u.zIndexRef,followerRef:s,adjustedTo:re(e),followerEnabled:c,renderContentNode:k}},render(){return S(xr,{ref:"followerRef",zIndex:this.zIndex,show:this.show,enabled:this.followerEnabled,to:this.adjustedTo,x:this.x,y:this.y,flip:this.flip,placement:this.placement,containerClass:this.namespace,overlap:this.overlap,width:this.width==="trigger"?"target":void 0,teleportDisabled:this.adjustedTo===re.tdkey},{default:()=>this.animated?S(br,{name:"popover-transition",appear:this.isMounted,onEnter:()=>{this.followerEnabled=!0},onAfterLeave:()=>{var e;(e=this.internalOnAfterLeave)===null||e===void 0||e.call(this),this.followerEnabled=!1,this.displayed=!1}},{default:this.renderContentNode}):this.renderContentNode()})}}),bt=Object.keys(nr),mt={focus:["onFocus","onBlur"],click:["onClick"],hover:["onMouseenter","onMouseleave"],manual:[],nested:["onFocus","onBlur","onMouseenter","onMouseleave","onClick"]};function yt(e,r,n){mt[r].forEach(t=>{e.props?e.props=Object.assign({},e.props):e.props={};const a=e.props[t],o=n[t];a?e.props[t]=(...i)=>{a(...i),o(...i)}:e.props[t]=o})}const St={show:{type:Boolean,default:void 0},defaultShow:Boolean,showArrow:{type:Boolean,default:!0},trigger:{type:String,default:"hover"},delay:{type:Number,default:100},duration:{type:Number,default:100},raw:Boolean,placement:{type:String,default:"top"},x:Number,y:Number,arrowPointToCenter:Boolean,disabled:Boolean,getDisabled:Function,displayDirective:{type:String,default:"if"},arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],flip:{type:Boolean,default:!0},animated:{type:Boolean,default:!0},width:{type:[Number,String],default:void 0},overlap:Boolean,keepAliveOnHover:{type:Boolean,default:!0},zIndex:Number,to:re.propTo,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],onClickoutside:Function,"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],internalDeactivateImmediately:Boolean,internalSyncTargetWithParent:Boolean,internalInheritedEventHandlers:{type:Array,default:()=>[]},internalTrapFocus:Boolean,internalExtraClass:{type:Array,default:()=>[]},onShow:[Function,Array],onHide:[Function,Array],arrow:{type:Boolean,default:void 0},minWidth:Number,maxWidth:Number},At=Object.assign(Object.assign(Object.assign({},Ae.props),St),{internalOnAfterLeave:Function,internalRenderBody:Function}),Pt=Je({name:"Popover",inheritAttrs:!1,props:At,slots:Object,__popover__:!0,setup(e){const r=Rr(),n=z(null),t=U(()=>e.show),a=z(e.defaultShow),o=Kr(t,a),i=Ie(()=>e.disabled?!1:o.value),s=()=>{if(e.disabled)return!0;const{getDisabled:l}=e;return!!(l!=null&&l())},u=()=>s()?!1:o.value,d=Ur(e,["arrow","showArrow"]),c=U(()=>e.overlap?!1:d.value);let p=null;const g=z(null),b=z(null),m=Ie(()=>e.x!==void 0&&e.y!==void 0);function f(l){const{"onUpdate:show":y,onUpdateShow:_,onShow:H,onHide:W}=e;a.value=l,y&&Y(y,l),_&&Y(_,l),l&&H&&Y(H,!0),l&&W&&Y(W,!1)}function T(){p&&p.syncPosition()}function $(){const{value:l}=g;l&&(window.clearTimeout(l),g.value=null)}function C(){const{value:l}=b;l&&(window.clearTimeout(l),b.value=null)}function N(){const l=s();if(e.trigger==="focus"&&!l){if(u())return;f(!0)}}function M(){const l=s();if(e.trigger==="focus"&&!l){if(!u())return;f(!1)}}function E(){const l=s();if(e.trigger==="hover"&&!l){if(C(),g.value!==null||u())return;const y=()=>{f(!0),g.value=null},{delay:_}=e;_===0?y():g.value=window.setTimeout(y,_)}}function k(){const l=s();if(e.trigger==="hover"&&!l){if($(),b.value!==null||!u())return;const y=()=>{f(!1),b.value=null},{duration:_}=e;_===0?y():b.value=window.setTimeout(y,_)}}function v(){k()}function A(l){var y;u()&&(e.trigger==="click"&&($(),C(),f(!1)),(y=e.onClickoutside)===null||y===void 0||y.call(e,l))}function O(){if(e.trigger==="click"&&!s()){$(),C();const l=!u();f(l)}}function h(l){e.internalTrapFocus&&l.key==="Escape"&&($(),C(),f(!1))}function X(l){a.value=l}function j(){var l;return(l=n.value)===null||l===void 0?void 0:l.targetRef}function I(l){p=l}return V("NPopover",{getTriggerElement:j,handleKeydown:h,handleMouseEnter:E,handleMouseLeave:k,handleClickOutside:A,handleMouseMoveOutside:v,setBodyInstance:I,positionManuallyRef:m,isMountedRef:r,zIndexRef:Q(e,"zIndex"),extraClassRef:Q(e,"internalExtraClass"),internalRenderBodyRef:Q(e,"internalRenderBody")}),Ye(()=>{o.value&&s()&&f(!1)}),{binderInstRef:n,positionManually:m,mergedShowConsideringDisabledProp:i,uncontrolledShow:a,mergedShowArrow:c,getMergedShow:u,setShow:X,handleClick:O,handleMouseEnter:E,handleMouseLeave:k,handleFocus:N,handleBlur:M,syncPosition:T}},render(){var e;const{positionManually:r,$slots:n}=this;let t,a=!1;if(!r&&(t=Pr(n,"trigger"),t)){t=Hr(t),t=t.type===Wr?S("span",[t]):t;const o={onClick:this.handleClick,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onFocus:this.handleFocus,onBlur:this.handleBlur};if(!((e=t.type)===null||e===void 0)&&e.__popover__)a=!0,t.props||(t.props={internalSyncTargetWithParent:!0,internalInheritedEventHandlers:[]}),t.props.internalSyncTargetWithParent=!0,t.props.internalInheritedEventHandlers?t.props.internalInheritedEventHandlers=[o,...t.props.internalInheritedEventHandlers]:t.props.internalInheritedEventHandlers=[o];else{const{internalInheritedEventHandlers:i}=this,s=[o,...i],u={onBlur:d=>{s.forEach(c=>{c.onBlur(d)})},onFocus:d=>{s.forEach(c=>{c.onFocus(d)})},onClick:d=>{s.forEach(c=>{c.onClick(d)})},onMouseenter:d=>{s.forEach(c=>{c.onMouseenter(d)})},onMouseleave:d=>{s.forEach(c=>{c.onMouseleave(d)})}};yt(t,i?"nested":r?"manual":this.trigger,u)}}return S(Br,{ref:"binderInstRef",syncTarget:!a,syncTargetWithParent:this.internalSyncTargetWithParent},{default:()=>{this.mergedShowConsideringDisabledProp;const o=this.getMergedShow();return[this.internalTrapFocus&&o?Ze(S("div",{style:{position:"fixed",top:0,right:0,bottom:0,left:0}}),[[Er,{enabled:o,zIndex:this.zIndex}]]):null,r?null:S(Ir,null,{default:()=>t}),S(wt,Mr(this.$props,bt,Object.assign(Object.assign({},this.$attrs),{showArrow:this.mergedShowArrow,show:o})),{default:()=>{var i,s;return(s=(i=this.$slots).default)===null||s===void 0?void 0:s.call(i)},header:()=>{var i,s;return(s=(i=this.$slots).header)===null||s===void 0?void 0:s.call(i)},footer:()=>{var i,s;return(s=(i=this.$slots).footer)===null||s===void 0?void 0:s.call(i)}})]}})}});export{Pt as N,St as p,Ur as u};
