var We=Object.defineProperty,Ge=Object.defineProperties;var Qe=Object.getOwnPropertyDescriptors;var Q=Object.getOwnPropertySymbols;var be=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable;var ie=(s,o,t)=>o in s?We(s,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[o]=t,f=(s,o)=>{for(var t in o||(o={}))be.call(o,t)&&ie(s,t,o[t]);if(Q)for(var t of Q(o))ke.call(o,t)&&ie(s,t,o[t]);return s},I=(s,o)=>Ge(s,Qe(o));var N=(s,o)=>{var t={};for(var a in s)be.call(s,a)&&o.indexOf(a)<0&&(t[a]=s[a]);if(s!=null&&Q)for(var a of Q(s))o.indexOf(a)<0&&ke.call(s,a)&&(t[a]=s[a]);return t};var R=(s,o,t)=>ie(s,typeof o!="symbol"?o+"":o,t);var X=(s,o,t)=>new Promise((a,i)=>{var c=d=>{try{v(t.next(d))}catch(r){i(r)}},m=d=>{try{v(t.throw(d))}catch(r){i(r)}},v=d=>d.done?a(d.value):Promise.resolve(d.value).then(c,m);v((t=t.apply(s,o)).next())});import{d as M,j as h,o as p,u as e,a3 as Ze,a4 as eo,s as y,n as b,i as De,e as ue,c as S,r as _,v as V,l as B,Y as oo,D as L,a5 as O,L as pe,$ as $e,y as to,K as so,a0 as ao,a6 as ce,a7 as no,w as lo,g as ro,x as Y,q as H,m as Be,H as fe,I as Ae,a8 as io,a9 as co,N as uo,h as we}from"../jse/index-index-Y3_OtjO-.js";import{_ as po,S as fo,u as mo}from"./index-DGcxnQ4T.js";import{a as me,ar as Se,aC as ho,aD as yo,aE as vo,a9 as go,aF as Co,aG as bo,aw as Ee,aH as ko,aI as Bo,aJ as wo,aK as _o,aL as Oo,aM as Mo,aN as xo,aO as Do,aP as $o,aQ as Ao,D as _e}from"./bootstrap-MyT3sENS.js";const So=me("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const Eo=me("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]);const To=me("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Io=M({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:o}){const i=Se(s,o);return(c,m)=>(p(),h(e(ho),Ze(eo(e(i))),{default:y(()=>[b(c.$slots,"default")]),_:3},16))}}),Lo=["data-dismissable-modal"],Po=M({__name:"DialogOverlay",setup(s){yo();const o=De("DISMISSABLE_MODAL_ID");return(t,a)=>(p(),ue("div",{"data-dismissable-modal":e(o),class:"bg-overlay z-popup inset-0"},null,8,Lo))}}),zo=M({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},appendTo:{default:"body"},class:{},closeClass:{},closeDisabled:{type:Boolean,default:!1},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},showClose:{type:Boolean,default:!0},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(s,{expose:o,emit:t}){const a=s,i=t,c=S(()=>{const E=a,{class:l,modal:u,open:k,showClose:x}=E;return N(E,["class","modal","open","showClose"])});function m(){return a.appendTo==="body"||a.appendTo===document.body||!a.appendTo}const v=S(()=>m()?"fixed":"absolute"),d=Se(c,i),r=_(null);function g(l){var u;l.target===((u=r.value)==null?void 0:u.$el)&&(a.open?i("opened"):i("closed"))}return o({getContentRef:()=>r.value}),(l,u)=>(p(),h(e(vo),{to:l.appendTo},{default:y(()=>[V(go,{name:"fade"},{default:y(()=>[l.open&&l.modal?(p(),h(Po,{key:0,style:oo(I(f({},l.zIndex?{zIndex:l.zIndex}:{}),{position:v.value,backdropFilter:l.overlayBlur&&l.overlayBlur>0?`blur(${l.overlayBlur}px)`:"none"})),onClick:u[0]||(u[0]=()=>i("close"))},null,8,["style"])):B("",!0)]),_:1}),V(e(bo),pe({ref_key:"contentRef",ref:r,style:I(f({},l.zIndex?{zIndex:l.zIndex}:{}),{position:v.value}),onAnimationend:g},e(d),{class:e(O)("z-popup bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] w-full p-6 shadow-lg outline-none sm:rounded-xl",a.class)}),{default:y(()=>[b(l.$slots,"default"),l.showClose?(p(),h(e(Co),{key:0,disabled:l.closeDisabled,class:L(e(O)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",a.closeClass)),onClick:u[1]||(u[1]=()=>i("close"))},{default:y(()=>[V(e(To),{class:"h-4 w-4"})]),_:1},8,["disabled","class"])):B("",!0)]),_:3},16,["style","class"])]),_:3},8,["to"]))}}),Oe=M({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const o=s,t=S(()=>{const m=o,{class:i}=m;return N(m,["class"])}),a=Ee(t);return(i,c)=>(p(),h(e(ko),pe(e(a),{class:e(O)("text-muted-foreground text-sm",o.class)}),{default:y(()=>[b(i.$slots,"default")]),_:3},16,["class"]))}}),Fo=M({__name:"DialogFooter",props:{class:{}},setup(s){const o=s;return(t,a)=>(p(),ue("div",{class:L(e(O)("flex flex-row flex-col-reverse justify-end gap-x-2",o.class))},[b(t.$slots,"default")],2))}}),Vo=M({__name:"DialogHeader",props:{class:{}},setup(s){const o=s;return(t,a)=>(p(),ue("div",{class:L(e(O)("flex flex-col gap-y-1.5 text-center sm:text-left",o.class))},[b(t.$slots,"default")],2))}}),Me=M({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const o=s,t=S(()=>{const m=o,{class:i}=m;return N(m,["class"])}),a=Ee(t);return(i,c)=>(p(),h(e(Bo),pe(e(a),{class:e(O)("text-lg font-semibold leading-none tracking-tight",o.class)}),{default:y(()=>[b(i.$slots,"default")]),_:3},16,["class"]))}});function No(s,o,t){const a=$e({offsetX:0,offsetY:0}),i=_(!1),c=r=>{const g=r.clientX,l=r.clientY;if(!s.value)return;const u=s.value.getBoundingClientRect(),{offsetX:k,offsetY:x}=a,w=u.left,E=u.top,Z=u.width,K=u.height,j=document.documentElement,ee=j.clientWidth,oe=j.clientHeight,te=-w+k,se=-E+x,P=ee-w-Z+k,ae=oe-E-K+x,q=J=>{let $=k+J.clientX-g,z=x+J.clientY-l;$=Math.min(Math.max($,te),P),z=Math.min(Math.max(z,se),ae),a.offsetX=$,a.offsetY=z,s.value&&(s.value.style.transform=`translate(${$}px, ${z}px)`,i.value=!0)},U=()=>{i.value=!1,document.removeEventListener("mousemove",q),document.removeEventListener("mouseup",U)};document.addEventListener("mousemove",q),document.addEventListener("mouseup",U)},m=()=>{const r=ce(o);r&&s.value&&r.addEventListener("mousedown",c)},v=()=>{const r=ce(o);r&&s.value&&r.removeEventListener("mousedown",c)},d=()=>{a.offsetX=0,a.offsetY=0;const r=ce(s);r&&(r.style.transform="none")};return to(()=>{so(()=>{t.value?m():v()})}),ao(()=>{v()}),{dragging:i,resetPosition:d,transform:a}}const Ro=M({__name:"modal",props:{modalApi:{default:void 0},appendToMain:{type:Boolean,default:!1},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmDisabled:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean},title:{},titleTooltip:{},zIndex:{}},setup(s){var G,Ce;const o=s,t=wo.getComponents(),a=_(),i=_(),c=_(),m=_(),v=_(),d=no();Ae("DISMISSABLE_MODAL_ID",d);const{$t:r}=_o(),{isMobile:g}=Oo(),l=(Ce=(G=o.modalApi)==null?void 0:G.useStore)==null?void 0:Ce.call(G),{appendToMain:u,bordered:k,cancelText:x,centered:w,class:E,closable:Z,closeOnClickModal:K,closeOnPressEscape:j,confirmDisabled:ee,confirmLoading:oe,confirmText:te,contentClass:se,description:P,destroyOnClose:ae,draggable:q,footer:U,footerClass:J,fullscreen:$,fullscreenButton:z,header:ne,headerClass:Te,loading:he,modal:Ie,openAutoFocus:Le,overlayBlur:Pe,showCancelButton:ze,showConfirmButton:Fe,submitting:D,title:W,titleTooltip:ye,zIndex:Ve}=Mo(o,l),le=S(()=>$.value&&ne.value||g.value),ve=S(()=>q.value&&!le.value&&ne.value),{dragging:Ne,transform:Re}=No(c,m,ve),re=_(!1),de=_(!0);lo(()=>{var n;return(n=l==null?void 0:l.value)==null?void 0:n.isOpen},n=>X(null,null,function*(){if(n){if(de.value=!1,re.value||(re.value=!0),yield fe(),!a.value)return;const C=a.value.getContentRef();c.value=C.$el;const{offsetX:F,offsetY:T}=Re;c.value.style.transform=`translate(${F}px, ${T}px)`}}),{immediate:!0});function Xe(){var n;(n=o.modalApi)==null||n.setState(C=>I(f({},C),{fullscreen:!$.value}))}function Ye(n){(!K.value||D.value)&&(n.preventDefault(),n.stopPropagation())}function He(n){(!j.value||D.value)&&n.preventDefault()}function Ke(n){Le.value||n==null||n.preventDefault()}function je(n){const C=n.target,F=C==null?void 0:C.dataset.dismissableModal;(!K.value||F!==d||D.value)&&(n.preventDefault(),n.stopPropagation())}function ge(n){n.preventDefault(),n.stopPropagation()}const qe=S(()=>u.value?`#${xo}>div:not(.absolute)>div`:void 0),Ue=S(()=>!e(ae)&&e(re));function Je(){var n;de.value=!0,(n=o.modalApi)==null||n.onClosed()}return(n,C)=>{var F;return p(),h(e(Io),{modal:!1,open:(F=e(l))==null?void 0:F.isOpen,"onUpdate:open":C[3]||(C[3]=()=>{var T;return e(D)||(T=n.modalApi)==null?void 0:T.close()})},{default:y(()=>{var T;return[V(e(zo),{ref_key:"contentRef",ref:a,"append-to":qe.value,class:L(e(O)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0 sm:rounded-[var(--radius)]",e(E),{"border-border border":e(k),"shadow-3xl":!e(k),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":le.value,"top-1/2 !-translate-y-1/2":e(w)&&!le.value,"duration-300":!e(Ne),hidden:de.value})),"force-mount":Ue.value,modal:e(Ie),open:(T=e(l))==null?void 0:T.isOpen,"show-close":e(Z),"z-index":e(Ve),"overlay-blur":e(Pe),"close-class":"top-3",onCloseAutoFocus:ge,onClosed:Je,"close-disabled":e(D),onEscapeKeyDown:He,onFocusOutside:ge,onInteractOutside:Ye,onOpenAutoFocus:Ke,onOpened:C[2]||(C[2]=()=>{var A;return(A=n.modalApi)==null?void 0:A.onOpened()}),onPointerDownOutside:je},{default:y(()=>[V(e(Vo),{ref_key:"headerRef",ref:m,class:L(e(O)("px-5 py-4",{"border-b":e(k),hidden:!e(ne),"cursor-move select-none":ve.value},e(Te)))},{default:y(()=>[e(W)?(p(),h(e(Me),{key:0,class:"text-left"},{default:y(()=>[b(n.$slots,"title",{},()=>[Y(H(e(W))+" ",1),e(ye)?b(n.$slots,"titleTooltip",{key:0},()=>[V(e(po),{"trigger-class":"pb-1"},{default:y(()=>[Y(H(e(ye)),1)]),_:1})]):B("",!0)])]),_:3})):B("",!0),e(P)?(p(),h(e(Oe),{key:1},{default:y(()=>[b(n.$slots,"description",{},()=>[Y(H(e(P)),1)])]),_:3})):B("",!0),!e(W)||!e(P)?(p(),h(e(Do),{key:2},{default:y(()=>[e(W)?B("",!0):(p(),h(e(Me),{key:0})),e(P)?B("",!0):(p(),h(e(Oe),{key:1}))]),_:1})):B("",!0)]),_:3},8,["class"]),ro("div",{ref_key:"wrapperRef",ref:i,class:L(e(O)("relative min-h-40 flex-1 overflow-y-auto p-3",e(se),{"pointer-events-none":e(he)||e(D)}))},[b(n.$slots,"default")],2),e(he)||e(D)?(p(),h(e($o),{key:0,spinning:""})):B("",!0),e(z)?(p(),h(e(Ao),{key:1,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:Xe},{default:y(()=>[e($)?(p(),h(e(Eo),{key:0,class:"size-3.5"})):(p(),h(e(So),{key:1,class:"size-3.5"}))]),_:1})):B("",!0),e(U)?(p(),h(e(Fo),{key:2,ref_key:"footerRef",ref:v,class:L(e(O)("flex-row items-center justify-end p-2",{"border-t":e(k)},e(J)))},{default:y(()=>[b(n.$slots,"prepend-footer"),b(n.$slots,"footer",{},()=>[e(ze)?(p(),h(Be(e(t).DefaultButton||e(_e)),{key:0,variant:"ghost",disabled:e(D),onClick:C[0]||(C[0]=()=>{var A;return(A=n.modalApi)==null?void 0:A.onCancel()})},{default:y(()=>[b(n.$slots,"cancelText",{},()=>[Y(H(e(x)||e(r)("cancel")),1)])]),_:3},8,["disabled"])):B("",!0),b(n.$slots,"center-footer"),e(Fe)?(p(),h(Be(e(t).PrimaryButton||e(_e)),{key:1,disabled:e(ee),loading:e(oe)||e(D),onClick:C[1]||(C[1]=()=>{var A;return(A=n.modalApi)==null?void 0:A.onConfirm()})},{default:y(()=>[b(n.$slots,"confirmText",{},()=>[Y(H(e(te)||e(r)("confirm")),1)])]),_:3},8,["disabled","loading"])):B("",!0)]),b(n.$slots,"append-footer")]),_:3},8,["class"])):B("",!0)]),_:3},8,["append-to","class","force-mount","modal","open","show-close","z-index","overlay-blur","close-disabled"])]}),_:3},8,["open"])}}});class Xo{constructor(o={}){R(this,"sharedData",{payload:{}});R(this,"store");R(this,"api");R(this,"state");const l=o,{connectedComponent:t,onBeforeClose:a,onCancel:i,onClosed:c,onConfirm:m,onOpenChange:v,onOpened:d}=l,r=N(l,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),g={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmDisabled:!1,confirmLoading:!1,contentClass:"",destroyOnClose:!0,draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new fo(f(f({},g),r),{onUpdate:()=>{var k,x,w;const u=this.store.state;(u==null?void 0:u.isOpen)===((k=this.state)==null?void 0:k.isOpen)?this.state=u:(this.state=u,(w=(x=this.api).onOpenChange)==null||w.call(x,!!(u!=null&&u.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:a,onCancel:i,onClosed:c,onConfirm:m,onOpenChange:v,onOpened:d},io(this)}close(){return X(this,null,function*(){var t,a,i;((i=yield(a=(t=this.api).onBeforeClose)==null?void 0:a.call(t))!=null?i:!0)&&this.store.setState(c=>I(f({},c),{isOpen:!1,submitting:!1}))})}getData(){var o,t;return(t=(o=this.sharedData)==null?void 0:o.payload)!=null?t:{}}lock(o=!0){return this.setState({submitting:o})}onCancel(){var o,t;this.api.onCancel?(t=(o=this.api).onCancel)==null||t.call(o):this.close()}onClosed(){var o,t;this.state.isOpen||(t=(o=this.api).onClosed)==null||t.call(o)}onConfirm(){var o,t;(t=(o=this.api).onConfirm)==null||t.call(o)}onOpened(){var o,t;this.state.isOpen&&((t=(o=this.api).onOpened)==null||t.call(o))}open(){this.store.setState(o=>I(f({},o),{isOpen:!0}))}setData(o){return this.sharedData.payload=o,this}setState(o){return co(o)?this.store.setState(o):this.store.setState(t=>f(f({},t),o)),this}unlock(){return this.lock(!1)}}const xe=Symbol("VBEN_MODAL_INJECT"),Yo={};function Jo(s={}){var v;const{connectedComponent:o}=s;if(o){const d=$e({}),r=_(!0),g=M((l,{attrs:u,slots:k})=>(Ae(xe,{extendApi(w){Object.setPrototypeOf(d,w)},options:s,reCreateModal(){return X(this,null,function*(){r.value=!1,yield fe(),r.value=!0})}}),Ho(d,f(f(f({},l),u),k)),()=>we(r.value?o:"div",f(f({},l),u),k)),{name:"VbenParentModal",inheritAttrs:!1});return uo(()=>{var l;(l=d==null?void 0:d.close)==null||l.call(d)}),[g,d]}const t=De(xe,{}),a=f(f(f({},Yo),t.options),s);a.onOpenChange=d=>{var r,g,l;(r=s.onOpenChange)==null||r.call(s,d),(l=(g=t.options)==null?void 0:g.onOpenChange)==null||l.call(g,d)},a.onClosed=()=>{var d,r;(d=s.onClosed)==null||d.call(s),a.destroyOnClose&&((r=t.reCreateModal)==null||r.call(t))};const i=new Xo(a),c=i;c.useStore=d=>mo(i.store,d);const m=M((d,{attrs:r,slots:g})=>()=>we(Ro,I(f(f({},d),r),{modalApi:c}),g),{name:"VbenModal",inheritAttrs:!1});return(v=t.extendApi)==null||v.call(t,c),[m,c]}function Ho(s,o){return X(this,null,function*(){var i;if(!o||Object.keys(o).length===0)return;yield fe();const t=(i=s==null?void 0:s.store)==null?void 0:i.state;if(!t)return;const a=new Set(Object.keys(t));for(const c of Object.keys(o))a.has(c)&&!["class"].includes(c)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${c}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}export{To as X,Jo as u};
