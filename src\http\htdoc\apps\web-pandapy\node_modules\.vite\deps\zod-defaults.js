import {
  z
} from "./chunk-UD6AZL74.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/zod-defaults@0.1.3_zod@3.24.3/node_modules/zod-defaults/dist/zod-defaults.js
var a = (e, o) => e.constructor.name === o.name;
var n = /* @__PURE__ */ new Map();
n.set(z.ZodBoolean.name, () => false), n.set(z.ZodNumber.name, () => 0), n.set(z.ZodString.name, () => ""), n.set(z.ZodArray.name, () => []), n.set(z.ZodRecord.name, () => ({})), n.set(z.ZodDefault.name, (e) => e._def.defaultValue()), n.set(z.ZodEffects.name, (e) => c(e._def.schema)), n.set(z.ZodOptional.name, (e) => a(e._def.innerType, z.ZodDefault) ? e._def.innerType._def.defaultValue() : void 0), n.set(z.ZodTuple.name, (e) => {
  const o = [];
  for (const d of e._def.items) o.push(c(d));
  return o;
}), n.set(z.ZodEffects.name, (e) => c(e._def.schema)), n.set(z.ZodUnion.name, (e) => c(e._def.options[0])), n.set(z.ZodObject.name, (e) => r(e)), n.set(z.ZodRecord.name, (e) => r(e)), n.set(z.ZodIntersection.name, (e) => r(e));
function c(e) {
  const o = e.constructor.name;
  if (!n.has(o)) {
    console.warn("getSchemaDefaultForField: Unhandled type", e.constructor.name);
    return;
  }
  return n.get(o)(e);
}
function r(e) {
  if (a(e, z.ZodRecord)) return {};
  if (a(e, z.ZodEffects)) return r(e._def.schema);
  if (a(e, z.ZodIntersection)) return { ...r(e._def.left), ...r(e._def.right) };
  if (a(e, z.ZodUnion)) {
    for (const o of e._def.options) if (a(o, z.ZodObject)) return r(o);
    return console.warn("getSchemaDefaultObject: No object found in union, returning empty object"), {};
  }
  return a(e, z.ZodObject) ? Object.fromEntries(Object.entries(e.shape).map(([o, d]) => [o, c(d)]).filter((o) => o[1] !== void 0)) : (console.warn(`getSchemaDefaultObject: Expected object schema, got ${e.constructor.name}`), {});
}
function s(e) {
  return r(e);
}
export {
  s as getDefaultsForSchema
};
//# sourceMappingURL=zod-defaults.js.map
