import Vue from '@vitejs/plugin-vue';
import VueJsx from '@vitejs/plugin-vue-jsx';
import { configDefaults, defineConfig } from 'vitest/config';
import { templateCompilerOptions } from '@tresjs/core';

export default defineConfig({
  plugins: [
    Vue({ ...templateCompilerOptions }),
    VueJsx(),
  ],
  test: {
    environment: 'happy-dom',
    exclude: [...configDefaults.exclude, '**/e2e/**'],
  },
});
