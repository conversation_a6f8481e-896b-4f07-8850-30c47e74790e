import time
from src.service import code
from src.service import service
from src.http.api import httpapi

class backend(service):
    
    _server: httpapi = None

    def init(self):

        _config = self.context.get_config()
        _addr, _port = _config.server["http"]["listen"].split(":")
        _port = int(_port)

        self._server = httpapi(self, _addr, _port)
        self.log_info(f"starting http server on `{_addr}:{_port}`")
        return code.SUCCESS

    def start(self):
        self._server.start()

        while True:
            time.sleep(1)
            pass
        pass

    def get_name(self):
        return "http"

    def get_status(self):
        return code.SUCCESS
