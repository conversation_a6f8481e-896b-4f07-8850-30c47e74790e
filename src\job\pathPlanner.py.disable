from typing import Tuple, List

# 定义三维点的类型别名，三元组 (x, y, z)
Point = Tuple[int, int, int]

# 轴对应的索引映射，方便通过字符访问对应的坐标值
axes = {"X":0, "Y":1, "Z": 2}

# 两种走线模式标识
SerpentineMode = 'S'  # 蛇形走线
ZahapeMode = 'Z'      # Z形走线

class PathPlanner():
    # 记录每个轴是否反向（起点>终点时为True）
    pathReverseOrder = [False, False, False]

    def __init__(self, start: Point, end: Point, step: Point, order: str = "XYZ", mode: str = "SSZ"):
        """
        初始化路径规划器
        
        参数：
        start: 起点坐标 (x,y,z)
        end: 终点坐标 (x,y,z)
        step: 三轴步长 (sx, sy, sz)
        order: 轴移动优先顺序，如"XYZ"
        mode: 走线模式字符串，每个轴对应一种模式，长度应为3，如"SSZ"
        """
        self.start = start
        self.end = end
        self.step = step
        self.order = order
        self.mode = mode

        # 判断各轴是否需要倒序遍历（起点坐标 > 终点坐标时需要）
        for i in range(len(start)):
            if start[i] > end[i]:
                self.pathReverseOrder[i] = True

        # 根据order字符串得到轴索引列表，方便按顺序处理
        self.idx = []
        for i in self.order:
            self.idx.append(axes[i])

        # 计算每个轴上总共要走的步数
        self.totalSetps = [0, 0, 0]
        for i in range(len(step)):  # 这里改成step，不是setp
            d = abs(self.start[i] - self.end[i] ) 
            # 整除时步数是d/step，否则要加1步保证覆盖终点
            self.totalSetps[i] = int(d / self.step[i]) + 1 #+ (1 if d % self.step[i] else 0)

        print(self.idx)
        print(self.pathReverseOrder)
        print(self.totalSetps)

    def pathGeneration(self) -> List[Point]:
        axes_idx = [axes[ch] for ch in self.order]
        starts = [self.start[i] for i in axes_idx]
        ends = [self.end[i] for i in axes_idx]
        steps = [self.step[i] for i in axes_idx]
        modes = [self.mode[i] for i in range(3)]
        totalSteps = [self.totalSetps[i] for i in axes_idx]
        
        path_points = []
    
        def generate_axis(level: int, current_point: List[int], reverse: bool):
            if level == 3:
                point_xyz = [0, 0, 0]
                for i, axis_i in enumerate(axes_idx):
                    point_xyz[axis_i] = current_point[i]
                path_points.append(tuple(point_xyz))
                return
    
            axis_start = starts[level]
            axis_end = ends[level]
            axis_step = steps[level]
            axis_total = totalSteps[level]
            axis_mode = modes[level]
    
            # 方向标志：起点小于终点为 +1，否则 -1
            step_dir = 1 if axis_end >= axis_start else -1
    
            indices = list(range(axis_total))
            if reverse:
                indices.reverse()
    
            for idx_i in indices:
                # 计算当前点坐标
                coord = axis_start + idx_i * axis_step * step_dir
                current_point[level] = coord
    
                next_reverse = reverse
                if axis_mode == SerpentineMode and idx_i % 2 == 1:
                    next_reverse = not reverse
    
                generate_axis(level + 1, current_point, next_reverse)
    
        generate_axis(0, [0, 0, 0], False)
        return path_points




if __name__ == "__main__":
    end = (6, 0, 1)
    start = (2, 5, 2)
    step = (1, 1, 1)
    pathPlanner = PathPlanner(start, end, step, "ZYX", "SSS")
    path = pathPlanner.pathGeneration()
    print(path)

    import matplotlib.pyplot as plt
    xs, ys, zs = zip(*path)
    fig = plt.figure(); ax = fig.add_subplot(111, projection='3d')
    ax.plot(xs, ys, zs, marker='o'); plt.show()