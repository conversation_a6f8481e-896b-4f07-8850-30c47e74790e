from src.service import code
from src.service import service
from src.controller.marlin import marlinctl
from src.controller.gbuilder import gbuilder

class controller(service):
    
    _marlin: marlinctl = None

    def init(self):

        # get config
        _config = self.context.get_config()
        _serialfd = _config.machine["serial"]["device"]
        _baudrate = _config.machine["serial"]["baudrate"]
        _use_simulate = _config.machine["serial"]["use_simulate"]
        self.log_info(f"opening serial device: {_serialfd}@{_baudrate}")

        # create marlin controller
        self._marlin = marlinctl(self, _serialfd, _baudrate, simulate=_use_simulate)

        if not self._marlin.wait_init():
            self.log_fatal("failed to initialize marlin controller")
            return code.FAILURE

        # print firmware version
        _fwversion = self._marlin.get_firmware()
        self.log_info(f"firmware version: {_fwversion}")

        # send initial commands
        _gbuilder = gbuilder.parse(_config, _config.machine["gcode"]["init"])
        self._marlin.write_gbuilder(_gbuilder)

        return code.SUCCESS

    def start(self):
        pass

    def stop(self):
        if self._marlin:
            self._marlin.stop()
        return code.SUCCESS

    def get_status(self):
        return code.SUCCESS

    def get_name(self):
        return "controller"

    def get_marlin(self) -> marlinctl:
        if self._marlin is None:
            raise ValueError("marlin controller is not initialized")
        return self._marlin
