
class constants:
    NONE            = 0      # 无
    SET_PATH_START  = 0x100  # 设定包围起点
    SET_PATH_END    = 0x101  # 设定包围终点
    SWAP_PATH_POINT = 0x102  # 交换包围点
    START_JOB       = 0x103  # 开始工作
    DECREASE_SPEED  = 0x104  # 减速
    INCREASE_SPEED  = 0x105  # 加速
    STOP_JOB        = 0x106  # 停止工作
    MOVE_X_POSITIVE = 0x107  # X轴正向移动
    MOVE_X_NEGATIVE = 0x108  # X轴负向移动
    MOVE_Y_POSITIVE = 0x109  # Y轴正向移动
    MOVE_Y_NEGATIVE = 0x10A  # Y轴负向移动
    MOVE_Z_POSITIVE = 0x10B  # Z轴正向移动
    MOVE_Z_NEGATIVE = 0x10C  # Z轴负向移动
    EMERGENCY_STOP  = 0x10D  # 紧急停止
