import {
  select_default
} from "./chunk-XYVZ4HU5.js";
import {
  input_default
} from "./chunk-QVKWU4R6.js";
import "./chunk-FWZ7IGE4.js";
import "./chunk-45Y6P64G.js";
import "./chunk-JL3MBCJ6.js";
import "./chunk-PLEPSOXJ.js";
import {
  dynamicApp,
  errLog
} from "./chunk-TLDIGKI7.js";
import {
  GLOBAL_EVENT_KEYS,
  VxeUI,
  createEvent,
  getConfig,
  getI18n,
  getIcon,
  globalEvents,
  require_xe_utils,
  useSize
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import {
  computed,
  defineComponent,
  h,
  inject,
  nextTick,
  reactive,
  ref,
  watch
} from "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/pager/src/pager.js
var import_xe_utils = __toESM(require_xe_utils());
var pager_default = defineComponent({
  name: "VxePager",
  props: {
    size: {
      type: String,
      default: () => getConfig().pager.size || getConfig().size
    },
    // 自定义布局
    layouts: {
      type: Array,
      default: () => getConfig().pager.layouts || ["PrevJump", "PrevPage", "Jump", "PageCount", "NextPage", "NextJump", "Sizes", "Total"]
    },
    // 当前页
    currentPage: {
      type: Number,
      default: 1
    },
    // 加载中
    loading: Boolean,
    // 每页大小
    pageSize: {
      type: Number,
      default: () => getConfig().pager.pageSize || 10
    },
    // 总条数
    total: { type: Number, default: 0 },
    // 显示页码按钮的数量
    pagerCount: {
      type: Number,
      default: () => getConfig().pager.pagerCount || 7
    },
    // 每页大小选项列表
    pageSizes: {
      type: Array,
      default: () => getConfig().pager.pageSizes || [10, 15, 20, 50, 100]
    },
    // 列对其方式
    align: {
      type: String,
      default: () => getConfig().pager.align
    },
    // 带边框
    border: {
      type: Boolean,
      default: () => getConfig().pager.border
    },
    // 带背景颜色
    background: {
      type: Boolean,
      default: () => getConfig().pager.background
    },
    // 配套的样式
    perfect: {
      type: Boolean,
      default: () => getConfig().pager.perfect
    },
    // 当只有一页时隐藏
    autoHidden: {
      type: Boolean,
      default: () => getConfig().pager.autoHidden
    },
    transfer: {
      type: Boolean,
      default: () => getConfig().pager.transfer
    },
    className: [String, Function],
    pageSizePlacement: {
      type: String,
      default: () => getConfig().pager.pageSizePlacement
    },
    // 自定义图标
    iconPrevPage: String,
    iconJumpPrev: String,
    iconJumpNext: String,
    iconNextPage: String,
    iconJumpMore: String,
    iconHomePage: String,
    iconEndPage: String
  },
  emits: [
    "update:pageSize",
    "update:currentPage",
    "page-change"
  ],
  setup(props, context) {
    const { slots, emit } = context;
    const xID = import_xe_utils.default.uniqueId();
    const { computeSize } = useSize(props);
    const $xeGrid = inject("$xeGrid", null);
    const reactData = reactive({
      inpCurrPage: props.currentPage
    });
    const refElem = ref();
    const refMaps = {
      refElem
    };
    const computePageCount = computed(() => {
      return getPageCount(props.total, props.pageSize);
    });
    const computeNumList = computed(() => {
      const { pagerCount } = props;
      const pageCount = computePageCount.value;
      const len = pageCount > pagerCount ? pagerCount - 2 : pagerCount;
      const rest = [];
      for (let index = 0; index < len; index++) {
        rest.push(index);
      }
      return rest;
    });
    const computeOffsetNumber = computed(() => {
      return Math.floor((props.pagerCount - 2) / 2);
    });
    const computeSizeList = computed(() => {
      return props.pageSizes.map((item) => {
        if (import_xe_utils.default.isNumber(item)) {
          return {
            value: item,
            label: `${getI18n("vxe.pager.pagesize", [item])}`
          };
        }
        return Object.assign({ value: "", label: "" }, item);
      });
    });
    const $xePager = {
      xID,
      props,
      context,
      getRefMaps: () => refMaps
    };
    let pagerMethods = {};
    let pagerPrivateMethods = {};
    const getPageCount = (total, size) => {
      return Math.max(Math.ceil(total / size), 1);
    };
    const jumpPageEvent = (evnt, currentPage) => {
      emit("update:currentPage", currentPage);
      if (evnt && currentPage !== props.currentPage) {
        pagerMethods.dispatchEvent("page-change", { type: "current", pageSize: props.pageSize, currentPage }, evnt);
      }
    };
    const changeCurrentPage = (currentPage, evnt) => {
      emit("update:currentPage", currentPage);
      if (evnt && currentPage !== props.currentPage) {
        pagerMethods.dispatchEvent("page-change", { type: "current", pageSize: props.pageSize, currentPage }, evnt);
      }
    };
    const triggerJumpEvent = (params) => {
      const { $event } = params;
      const inputElem = $event.target;
      const inpValue = import_xe_utils.default.toInteger(inputElem.value);
      const pageCount = computePageCount.value;
      const current = inpValue <= 0 ? 1 : inpValue >= pageCount ? pageCount : inpValue;
      const currPage = import_xe_utils.default.toValueString(current);
      inputElem.value = currPage;
      reactData.inpCurrPage = currPage;
      changeCurrentPage(current, $event);
    };
    const handleHomePage = (evnt) => {
      const { currentPage } = props;
      if (currentPage > 1) {
        changeCurrentPage(1, evnt);
      }
    };
    const handleEndPage = (evnt) => {
      const { currentPage } = props;
      const pageCount = computePageCount.value;
      if (currentPage < pageCount) {
        changeCurrentPage(pageCount, evnt);
      }
    };
    const handlePrevPage = (evnt) => {
      const { currentPage } = props;
      const pageCount = computePageCount.value;
      if (currentPage > 1) {
        changeCurrentPage(Math.min(pageCount, Math.max(currentPage - 1, 1)), evnt);
      }
    };
    const handleNextPage = (evnt) => {
      const { currentPage } = props;
      const pageCount = computePageCount.value;
      if (currentPage < pageCount) {
        changeCurrentPage(Math.min(pageCount, currentPage + 1), evnt);
      }
    };
    const handlePrevJump = (evnt) => {
      const numList = computeNumList.value;
      changeCurrentPage(Math.max(props.currentPage - numList.length, 1), evnt);
    };
    const handleNextJump = (evnt) => {
      const pageCount = computePageCount.value;
      const numList = computeNumList.value;
      changeCurrentPage(Math.min(props.currentPage + numList.length, pageCount), evnt);
    };
    const pageSizeEvent = (params) => {
      const { value } = params;
      const pageSize = import_xe_utils.default.toNumber(value);
      const pageCount = getPageCount(props.total, pageSize);
      let currentPage = props.currentPage;
      if (currentPage > pageCount) {
        currentPage = pageCount;
        emit("update:currentPage", pageCount);
      }
      emit("update:pageSize", pageSize);
      pagerMethods.dispatchEvent("page-change", { type: "size", pageSize, currentPage }, params.$event);
    };
    const jumpKeydownEvent = (params) => {
      const { $event } = params;
      if (globalEvents.hasKey($event, GLOBAL_EVENT_KEYS.ENTER)) {
        triggerJumpEvent(params);
      } else if (globalEvents.hasKey($event, GLOBAL_EVENT_KEYS.ARROW_UP)) {
        $event.preventDefault();
        handleNextPage($event);
      } else if (globalEvents.hasKey($event, GLOBAL_EVENT_KEYS.ARROW_DOWN)) {
        $event.preventDefault();
        handlePrevPage($event);
      }
    };
    const renderHomePage = () => {
      const { currentPage, total } = props;
      const homeSlot = slots.home;
      const pageCount = computePageCount.value;
      if (homeSlot) {
        return h("span", {
          class: "vxe-pager--custom-home-btn"
        }, homeSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h("button", {
        class: ["vxe-pager--home-btn", {
          "is--disabled": currentPage <= 1
        }],
        type: "button",
        title: getI18n("vxe.pager.homePageTitle"),
        onClick: handleHomePage
      }, [
        h("i", {
          class: ["vxe-pager--btn-icon", props.iconHomePage || getIcon().PAGER_HOME]
        })
      ]);
    };
    const renderPrevPage = () => {
      const { currentPage, total } = props;
      const prevPageSlot = slots.prevPage || slots["prev-page"];
      const pageCount = computePageCount.value;
      if (prevPageSlot) {
        return h("span", {
          class: "vxe-pager--custom-prev-btn"
        }, prevPageSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h("button", {
        class: ["vxe-pager--prev-btn", {
          "is--disabled": currentPage <= 1
        }],
        type: "button",
        title: getI18n("vxe.pager.prevPageTitle"),
        onClick: handlePrevPage
      }, [
        h("i", {
          class: ["vxe-pager--btn-icon", props.iconPrevPage || getIcon().PAGER_PREV_PAGE]
        })
      ]);
    };
    const renderPrevJump = (tagName) => {
      const { currentPage, total } = props;
      const prevJumpSlot = slots.prevJump || slots["prev-jump"];
      const pageCount = computePageCount.value;
      if (prevJumpSlot) {
        return h("span", {
          class: "vxe-pager--custom-jump-prev"
        }, prevJumpSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h(tagName || "button", {
        class: ["vxe-pager--jump-prev", {
          "is--fixed": !tagName,
          "is--disabled": currentPage <= 1
        }],
        type: "button",
        title: getI18n("vxe.pager.prevJumpTitle"),
        onClick: handlePrevJump
      }, [
        tagName ? h("i", {
          class: ["vxe-pager--jump-more-icon", props.iconJumpMore || getIcon().PAGER_JUMP_MORE]
        }) : null,
        h("i", {
          class: ["vxe-pager--jump-icon", props.iconJumpPrev || getIcon().PAGER_JUMP_PREV]
        })
      ]);
    };
    const renderNextJump = (tagName) => {
      const { currentPage, total } = props;
      const nextJumpSlot = slots.nextJump || slots["next-jump"];
      const pageCount = computePageCount.value;
      if (nextJumpSlot) {
        return h("span", {
          class: "vxe-pager--custom-jump-next"
        }, nextJumpSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h(tagName || "button", {
        class: ["vxe-pager--jump-next", {
          "is--fixed": !tagName,
          "is--disabled": currentPage >= pageCount
        }],
        type: "button",
        title: getI18n("vxe.pager.nextJumpTitle"),
        onClick: handleNextJump
      }, [
        tagName ? h("i", {
          class: ["vxe-pager--jump-more-icon", props.iconJumpMore || getIcon().PAGER_JUMP_MORE]
        }) : null,
        h("i", {
          class: ["vxe-pager--jump-icon", props.iconJumpNext || getIcon().PAGER_JUMP_NEXT]
        })
      ]);
    };
    const renderNextPage = () => {
      const { currentPage, total } = props;
      const nextPageSlot = slots.nextPage || slots["next-page"];
      const pageCount = computePageCount.value;
      if (nextPageSlot) {
        return h("span", {
          class: "vxe-pager--custom-next-btn"
        }, nextPageSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h("button", {
        class: ["vxe-pager--next-btn", {
          "is--disabled": currentPage >= pageCount
        }],
        type: "button",
        title: getI18n("vxe.pager.nextPageTitle"),
        onClick: handleNextPage
      }, [
        h("i", {
          class: ["vxe-pager--btn-icon", props.iconNextPage || getIcon().PAGER_NEXT_PAGE]
        })
      ]);
    };
    const renderEndPage = () => {
      const { currentPage, total } = props;
      const endSlot = slots.end;
      const pageCount = computePageCount.value;
      if (endSlot) {
        return h("span", {
          class: "vxe-pager--custom-end-btn"
        }, endSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h("button", {
        class: ["vxe-pager--end-btn", {
          "is--disabled": currentPage >= pageCount
        }],
        type: "button",
        title: getI18n("vxe.pager.endPageTitle"),
        onClick: handleEndPage
      }, [
        h("i", {
          class: ["vxe-pager--btn-icon", props.iconEndPage || getIcon().PAGER_END]
        })
      ]);
    };
    const renderNumber = (showJump) => {
      const { currentPage, total, pagerCount } = props;
      const numberSlot = showJump ? slots.numberJump || slots["number-jump"] : slots.number;
      const nums = [];
      const pageCount = computePageCount.value;
      const numList = computeNumList.value;
      const offsetNumber = computeOffsetNumber.value;
      const isOv = pageCount > pagerCount;
      const isLt = isOv && currentPage > offsetNumber + 1;
      const isGt = isOv && currentPage < pageCount - offsetNumber;
      const restList = [];
      let startNumber = 1;
      if (isOv) {
        if (currentPage >= pageCount - offsetNumber) {
          startNumber = Math.max(pageCount - numList.length + 1, 1);
        } else {
          startNumber = Math.max(currentPage - offsetNumber, 1);
        }
      }
      if (showJump && isLt) {
        restList.push(1);
        nums.push(h("button", {
          class: "vxe-pager--num-btn",
          type: "button",
          onClick: (evnt) => jumpPageEvent(evnt, 1)
        }, "1"), renderPrevJump("span"));
      }
      numList.forEach((item, index) => {
        const number = startNumber + index;
        if (number <= pageCount) {
          restList.push(number);
          nums.push(h("button", {
            key: number,
            class: ["vxe-pager--num-btn", {
              "is--active": currentPage === number
            }],
            type: "button",
            onClick: (evnt) => jumpPageEvent(evnt, number)
          }, `${number}`));
        }
      });
      if (showJump && isGt) {
        restList.push(pageCount);
        nums.push(renderNextJump("button"), h("button", {
          class: "vxe-pager--num-btn",
          type: "button",
          onClick: (evnt) => jumpPageEvent(evnt, pageCount)
        }, pageCount));
      }
      if (numberSlot) {
        return h("span", {
          class: "vxe-pager--custom-btn-wrapper"
        }, numberSlot({ $pager: $xePager, total, numList: restList, currentPage, pageCount }));
      }
      return h("span", {
        class: "vxe-pager--btn-wrapper"
      }, nums);
    };
    const renderJumpNumber = () => {
      return renderNumber(true);
    };
    const renderSizes = () => {
      const { total, currentPage, pageSize, pageSizePlacement, transfer } = props;
      const sizesSlot = slots.sizes;
      const sizeList = computeSizeList.value;
      const pageCount = computePageCount.value;
      if (sizesSlot) {
        return h("span", {
          class: "vxe-pager--custom-sizes"
        }, sizesSlot({ $pager: $xePager, total, currentPage, pageCount, pageSize, options: sizeList }));
      }
      return h(select_default, {
        class: "vxe-pager--sizes",
        modelValue: pageSize,
        placement: pageSizePlacement,
        transfer,
        options: sizeList,
        onChange: pageSizeEvent
      });
    };
    const renderJump = (isFull) => {
      const { total } = props;
      const { inpCurrPage } = reactData;
      const jumpSlot = isFull ? slots.fullJump || slots["full-jump"] : slots.jump;
      const pageCount = computePageCount.value;
      if (jumpSlot) {
        return h("span", {
          class: "vxe-pager--custom-jump"
        }, jumpSlot({ $pager: $xePager, total, currentPage: inpCurrPage, pageCount }));
      }
      return h("span", {
        class: "vxe-pager--jump"
      }, [
        isFull ? h("span", {
          class: "vxe-pager--goto-text"
        }, getI18n("vxe.pager.goto")) : null,
        h(input_default, {
          class: "vxe-pager--goto",
          modelValue: reactData.inpCurrPage,
          placeholder: getI18n("vxe.pager.gotoTitle"),
          align: "center",
          type: "integer",
          max: pageCount,
          min: 1,
          controls: false,
          onKeydown: jumpKeydownEvent,
          onBlur: triggerJumpEvent,
          "onUpdate:modelValue"(val) {
            reactData.inpCurrPage = val;
          }
        }),
        isFull ? h("span", {
          class: "vxe-pager--classifier-text"
        }, getI18n("vxe.pager.pageClassifier")) : null
      ]);
    };
    const renderFullJump = () => {
      return renderJump(true);
    };
    const renderPageCount = () => {
      const { currentPage, total } = props;
      const pageCountSlot = slots.pageCount || slots["page-count"];
      const pageCount = computePageCount.value;
      if (pageCountSlot) {
        return h("span", {
          class: "vxe-pager--custom-count"
        }, pageCountSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h("span", {
        class: "vxe-pager--count"
      }, [
        h("span", {
          class: "vxe-pager--separator"
        }),
        h("span", pageCount)
      ]);
    };
    const renderTotal = () => {
      const { currentPage, total } = props;
      const totalSlot = slots.total;
      const pageCount = computePageCount.value;
      if (totalSlot) {
        return h("span", {
          class: "vxe-pager--custom-total"
        }, totalSlot({ $pager: $xePager, total, currentPage, pageCount }));
      }
      return h("span", {
        class: "vxe-pager--total"
      }, getI18n("vxe.pager.total", [total]));
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $pager: $xePager }, params));
    };
    pagerMethods = {
      dispatchEvent,
      homePage() {
        handleHomePage();
        return nextTick();
      },
      endPage() {
        handleEndPage();
        return nextTick();
      },
      prevPage() {
        handlePrevPage();
        return nextTick();
      },
      nextPage() {
        handleNextPage();
        return nextTick();
      },
      prevJump() {
        handlePrevJump();
        return nextTick();
      },
      nextJump() {
        handleNextJump();
        return nextTick();
      },
      jumpPage(currentPage) {
        const current = import_xe_utils.default.toNumber(currentPage) || 1;
        reactData.inpCurrPage = current;
        changeCurrentPage(current);
        return nextTick();
      }
    };
    pagerPrivateMethods = {
      handlePrevPage,
      handleNextPage,
      handlePrevJump,
      handleNextJump
    };
    Object.assign($xePager, pagerMethods, pagerPrivateMethods);
    watch(() => props.currentPage, (value) => {
      reactData.inpCurrPage = value;
    });
    const renderVN = () => {
      const { align, layouts, className } = props;
      const childNodes = [];
      const vSize = computeSize.value;
      const pageCount = computePageCount.value;
      if (slots.left) {
        childNodes.push(h("span", {
          class: "vxe-pager--left-wrapper"
        }, slots.left({ $grid: $xeGrid })));
      }
      layouts.forEach((name) => {
        let renderFn;
        switch (name) {
          case "Home":
            renderFn = renderHomePage;
            break;
          case "PrevJump":
            renderFn = renderPrevJump;
            break;
          case "PrevPage":
            renderFn = renderPrevPage;
            break;
          case "Number":
            renderFn = renderNumber;
            break;
          case "JumpNumber":
            renderFn = renderJumpNumber;
            break;
          case "NextPage":
            renderFn = renderNextPage;
            break;
          case "NextJump":
            renderFn = renderNextJump;
            break;
          case "End":
            renderFn = renderEndPage;
            break;
          case "Sizes":
            renderFn = renderSizes;
            break;
          case "FullJump":
            renderFn = renderFullJump;
            break;
          case "Jump":
            renderFn = renderJump;
            break;
          case "PageCount":
            renderFn = renderPageCount;
            break;
          case "Total":
            renderFn = renderTotal;
            break;
        }
        if (renderFn) {
          childNodes.push(renderFn());
        } else {
          if (true) {
            errLog("vxe.error.notProp", [`layouts -> ${name}`]);
          }
        }
      });
      if (slots.right) {
        childNodes.push(h("span", {
          class: "vxe-pager--right-wrapper"
        }, slots.right({ $grid: $xeGrid })));
      }
      return h("div", {
        ref: refElem,
        class: ["vxe-pager", className ? import_xe_utils.default.isFunction(className) ? className({ $pager: $xePager }) : className : "", {
          [`size--${vSize}`]: vSize,
          [`align--${align}`]: align,
          "is--border": props.border,
          "is--background": props.background,
          "is--perfect": props.perfect,
          "is--hidden": props.autoHidden && pageCount === 1,
          "is--loading": props.loading
        }]
      }, [
        h("div", {
          class: "vxe-pager--wrapper"
        }, childNodes)
      ]);
    };
    $xePager.renderVN = renderVN;
    return $xePager;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/pager/index.js
var VxePager = Object.assign(pager_default, {
  install: function(app) {
    app.component(pager_default.name, pager_default);
  }
});
dynamicApp.use(VxePager);
VxeUI.component(pager_default);
var Pager = VxePager;
var pager_default2 = VxePager;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-pager/index.js
var vxe_pager_default = pager_default2;
export {
  Pager,
  VxePager,
  vxe_pager_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-pager_index__js.js.map
