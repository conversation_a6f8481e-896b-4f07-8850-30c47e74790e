import {
  input_default
} from "./chunk-QVKWU4R6.js";
import "./chunk-FWZ7IGE4.js";
import "./chunk-45Y6P64G.js";
import "./chunk-JL3MBCJ6.js";
import "./chunk-PLEPSOXJ.js";
import {
  dynamicApp
} from "./chunk-TLDIGKI7.js";
import {
  VxeUI
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/input/index.js
var VxeInput = Object.assign(input_default, {
  install(app) {
    app.component(input_default.name, input_default);
  }
});
dynamicApp.use(VxeInput);
VxeUI.component(input_default);
var Input = VxeInput;
var input_default2 = VxeInput;

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-input/index.js
var vxe_input_default = input_default2;
export {
  Input,
  VxeInput,
  vxe_input_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-input_index__js.js.map
