{"version": 3, "sources": ["../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/_common.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/dark.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/src/styles/rtl.cssr.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/rtl.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/space/styles/light.mjs"], "sourcesContent": ["export default {\n  gapSmall: '4px 8px',\n  gapMedium: '8px 12px',\n  gapLarge: '12px 16px'\n};", "import commonVars from \"./_common.mjs\";\nconst spaceDark = {\n  name: 'Space',\n  self() {\n    return commonVars;\n  }\n};\nexport default spaceDark;", "import { cB, cM } from \"../../../_utils/cssr/index.mjs\";\nexport default cB('space', [cM('rtl', `\n direction: rtl;\n `)]);", "import rtlStyle from \"../src/styles/rtl.cssr.mjs\";\nexport const spaceRtl = {\n  name: 'Space',\n  style: rtlStyle\n};", "import commonVars from \"./_common.mjs\";\nfunction self() {\n  return commonVars;\n}\nconst spaceLight = {\n  name: 'Space',\n  self\n};\nexport default spaceLight;"], "mappings": ";;;;;;AAAA,IAAO,iBAAQ;AAAA,EACb,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AACZ;;;ACHA,IAAM,YAAY;AAAA,EAChB,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAO,eAAQ;;;ACNf,IAAO,mBAAQ,GAAG,SAAS,CAAC,GAAG,OAAO;AAAA;AAAA,EAEpC,CAAC,CAAC;;;ACFG,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AACT;;;ACHA,SAAS,OAAO;AACd,SAAO;AACT;AACA,IAAM,aAAa;AAAA,EACjB,MAAM;AAAA,EACN;AACF;AACA,IAAO,gBAAQ;", "names": []}