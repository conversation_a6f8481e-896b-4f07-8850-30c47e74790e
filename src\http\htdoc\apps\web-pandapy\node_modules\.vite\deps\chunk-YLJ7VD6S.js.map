{"version": 3, "sources": ["../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/divider/src/styles/index.cssr.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/divider/src/Divider.mjs"], "sourcesContent": ["import { cB, cE, cM, cNotM } from \"../../../_utils/cssr/index.mjs\";\n// vars:\n// --n-bezier\n// --n-color\n// --n-text-color\n// --n-font-weight\nexport default cB('divider', `\n position: relative;\n display: flex;\n width: 100%;\n box-sizing: border-box;\n font-size: 16px;\n color: var(--n-text-color);\n transition:\n color .3s var(--n-bezier),\n background-color .3s var(--n-bezier);\n`, [cNotM('vertical', `\n margin-top: 24px;\n margin-bottom: 24px;\n `, [cNotM('no-title', `\n display: flex;\n align-items: center;\n `)]), cE('title', `\n display: flex;\n align-items: center;\n margin-left: 12px;\n margin-right: 12px;\n white-space: nowrap;\n font-weight: var(--n-font-weight);\n `), cM('title-position-left', [cE('line', [cM('left', {\n  width: '28px'\n})])]), cM('title-position-right', [cE('line', [cM('right', {\n  width: '28px'\n})])]), cM('dashed', [cE('line', `\n background-color: #0000;\n height: 0px;\n width: 100%;\n border-style: dashed;\n border-width: 1px 0 0;\n `)]), cM('vertical', `\n display: inline-block;\n height: 1em;\n margin: 0 8px;\n vertical-align: middle;\n width: 1px;\n `), cE('line', `\n border: none;\n transition: background-color .3s var(--n-bezier), border-color .3s var(--n-bezier);\n height: 1px;\n width: 100%;\n margin: 0;\n `), cNotM('dashed', [cE('line', {\n  backgroundColor: 'var(--n-color)'\n})]), cM('dashed', [cE('line', {\n  borderColor: 'var(--n-color)'\n})]), cM('vertical', {\n  backgroundColor: 'var(--n-color)'\n})]);", "import { computed, defineComponent, Fragment, h } from 'vue';\nimport { useConfig, useTheme, useThemeClass } from \"../../_mixins/index.mjs\";\nimport { dividerLight } from \"../styles/index.mjs\";\nimport style from \"./styles/index.cssr.mjs\";\nexport const dividerProps = Object.assign(Object.assign({}, useTheme.props), {\n  titlePlacement: {\n    type: String,\n    default: 'center'\n  },\n  dashed: Boolean,\n  vertical: Boolean\n});\nexport default defineComponent({\n  name: 'Divider',\n  props: dividerProps,\n  setup(props) {\n    const {\n      mergedClsPrefixRef,\n      inlineThemeDisabled\n    } = useConfig(props);\n    const themeRef = useTheme('Divider', '-divider', style, dividerLight, props, mergedClsPrefixRef);\n    const cssVarsRef = computed(() => {\n      const {\n        common: {\n          cubicBezierEaseInOut\n        },\n        self: {\n          color,\n          textColor,\n          fontWeight\n        }\n      } = themeRef.value;\n      return {\n        '--n-bezier': cubicBezierEaseInOut,\n        '--n-color': color,\n        '--n-text-color': textColor,\n        '--n-font-weight': fontWeight\n      };\n    });\n    const themeClassHandle = inlineThemeDisabled ? useThemeClass('divider', undefined, cssVarsRef, props) : undefined;\n    return {\n      mergedClsPrefix: mergedClsPrefixRef,\n      cssVars: inlineThemeDisabled ? undefined : cssVarsRef,\n      themeClass: themeClassHandle === null || themeClassHandle === void 0 ? void 0 : themeClassHandle.themeClass,\n      onRender: themeClassHandle === null || themeClassHandle === void 0 ? void 0 : themeClassHandle.onRender\n    };\n  },\n  render() {\n    var _a;\n    const {\n      $slots,\n      titlePlacement,\n      vertical,\n      dashed,\n      cssVars,\n      mergedClsPrefix\n    } = this;\n    (_a = this.onRender) === null || _a === void 0 ? void 0 : _a.call(this);\n    return h(\"div\", {\n      role: \"separator\",\n      class: [`${mergedClsPrefix}-divider`, this.themeClass, {\n        [`${mergedClsPrefix}-divider--vertical`]: vertical,\n        [`${mergedClsPrefix}-divider--no-title`]: !$slots.default,\n        [`${mergedClsPrefix}-divider--dashed`]: dashed,\n        [`${mergedClsPrefix}-divider--title-position-${titlePlacement}`]: $slots.default && titlePlacement\n      }],\n      style: cssVars\n    }, !vertical ? h(\"div\", {\n      class: `${mergedClsPrefix}-divider__line ${mergedClsPrefix}-divider__line--left`\n    }) : null, !vertical && $slots.default ? h(Fragment, null, h(\"div\", {\n      class: `${mergedClsPrefix}-divider__title`\n    }, this.$slots), h(\"div\", {\n      class: `${mergedClsPrefix}-divider__line ${mergedClsPrefix}-divider__line--right`\n    })) : null);\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAMA,IAAO,qBAAQ,GAAG,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAU1B,CAAC,MAAM,YAAY;AAAA;AAAA;AAAA,IAGlB,CAAC,MAAM,YAAY;AAAA;AAAA;AAAA,EAGrB,CAAC,CAAC,GAAG,GAAG,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,GAAG,GAAG,uBAAuB,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ;AAAA,EACrD,OAAO;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,wBAAwB,CAAC,GAAG,QAAQ,CAAC,GAAG,SAAS;AAAA,EAC1D,OAAO;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,CAAC,CAAC,GAAG,GAAG,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,GAAG,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,GAAG,MAAM,UAAU,CAAC,GAAG,QAAQ;AAAA,EAC/B,iBAAiB;AACnB,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,QAAQ;AAAA,EAC7B,aAAa;AACf,CAAC,CAAC,CAAC,GAAG,GAAG,YAAY;AAAA,EACnB,iBAAiB;AACnB,CAAC,CAAC,CAAC;;;ACrDI,IAAM,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAS,KAAK,GAAG;AAAA,EAC3E,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC;AACD,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,UAAU,KAAK;AACnB,UAAM,WAAW,kBAAS,WAAW,YAAY,oBAAO,eAAc,OAAO,kBAAkB;AAC/F,UAAM,aAAa,SAAS,MAAM;AAChC,YAAM;AAAA,QACJ,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,IAAI,SAAS;AACb,aAAO;AAAA,QACL,cAAc;AAAA,QACd,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,MACrB;AAAA,IACF,CAAC;AACD,UAAM,mBAAmB,sBAAsB,cAAc,WAAW,QAAW,YAAY,KAAK,IAAI;AACxG,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,SAAS,sBAAsB,SAAY;AAAA,MAC3C,YAAY,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAAA,MACjG,UAAU,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAAA,IACjG;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,KAAC,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AACtE,WAAO,EAAE,OAAO;AAAA,MACd,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,eAAe,YAAY,KAAK,YAAY;AAAA,QACrD,CAAC,GAAG,eAAe,oBAAoB,GAAG;AAAA,QAC1C,CAAC,GAAG,eAAe,oBAAoB,GAAG,CAAC,OAAO;AAAA,QAClD,CAAC,GAAG,eAAe,kBAAkB,GAAG;AAAA,QACxC,CAAC,GAAG,eAAe,4BAA4B,cAAc,EAAE,GAAG,OAAO,WAAW;AAAA,MACtF,CAAC;AAAA,MACD,OAAO;AAAA,IACT,GAAG,CAAC,WAAW,EAAE,OAAO;AAAA,MACtB,OAAO,GAAG,eAAe,kBAAkB,eAAe;AAAA,IAC5D,CAAC,IAAI,MAAM,CAAC,YAAY,OAAO,UAAU,EAAE,UAAU,MAAM,EAAE,OAAO;AAAA,MAClE,OAAO,GAAG,eAAe;AAAA,IAC3B,GAAG,KAAK,MAAM,GAAG,EAAE,OAAO;AAAA,MACxB,OAAO,GAAG,eAAe,kBAAkB,eAAe;AAAA,IAC5D,CAAC,CAAC,IAAI,IAAI;AAAA,EACZ;AACF,CAAC;", "names": []}