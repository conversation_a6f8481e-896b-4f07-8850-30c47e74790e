const o="欢迎回来",t="开箱即用的大型中后台管理系统",c="工程化、高性能、跨组件库的前端模版",n="登录成功",s="欢迎回来",e="请输入您的帐户信息以开始管理您的项目",i="快速选择账号",r="账号",a="密码",l="请输入用户名",g="请输入密码",d="请先完成验证",p="密码错误",u="记住账号",m="创建一个账号",T="创建账号",b="已经有账号了?",w="还没有账号?",S="注册",f="让您的应用程序管理变得简单而有趣",A="确认密码",P="两次输入的密码不一致",y="我同意",L="隐私政策",h="条款",q="请同意隐私政策和条款",v="去登录",E="使用 8 个或更多字符，混合字母、数字和符号",R="忘记密码?",k="输入您的电子邮件，我们将向您发送重置密码的连接",x="请输入邮箱",D="你输入的邮箱格式不正确",U="发送重置链接",B="邮箱",C="请用手机扫描二维码登录",H="扫码后点击 '确认'，即可完成登录",M="扫码登录",V="请输入您的手机号码以开始管理您的项目",j="验证码",z="请输入{0}位验证码",F="手机号码",G="请输入手机号",I="手机号码格式错误",J="手机号登录",K="获取验证码",N="{0}秒后重新获取",O="其他登录方式",Q="重新登录",W="您的登录状态已过期，请重新登录以继续。",X={center:"居中",alignLeft:"居左",alignRight:"居右"},Y={welcomeBack:o,pageTitle:t,pageDesc:c,loginSuccess:n,loginSuccessDesc:s,loginSubtitle:e,selectAccount:i,username:r,password:a,usernameTip:l,passwordTip:g,verifyRequiredTip:d,passwordErrorTip:p,rememberMe:u,createAnAccount:m,createAccount:T,alreadyHaveAccount:b,accountTip:w,signUp:S,signUpSubtitle:f,confirmPassword:A,confirmPasswordTip:P,agree:y,privacyPolicy:L,terms:h,agreeTip:q,goToLogin:v,passwordStrength:E,forgetPassword:R,forgetPasswordSubtitle:k,emailTip:x,emailValidErrorTip:D,sendResetLink:U,email:B,qrcodeSubtitle:C,qrcodePrompt:H,qrcodeLogin:M,codeSubtitle:V,code:j,codeTip:z,mobile:F,mobileTip:G,mobileErrortip:I,mobileLogin:J,sendCode:K,sendText:N,thirdPartyLogin:O,loginAgainTitle:Q,loginAgainSubTitle:W,layout:X};export{w as accountTip,y as agree,q as agreeTip,b as alreadyHaveAccount,j as code,V as codeSubtitle,z as codeTip,A as confirmPassword,P as confirmPasswordTip,T as createAccount,m as createAnAccount,Y as default,B as email,x as emailTip,D as emailValidErrorTip,R as forgetPassword,k as forgetPasswordSubtitle,v as goToLogin,X as layout,W as loginAgainSubTitle,Q as loginAgainTitle,e as loginSubtitle,n as loginSuccess,s as loginSuccessDesc,F as mobile,I as mobileErrortip,J as mobileLogin,G as mobileTip,c as pageDesc,t as pageTitle,a as password,p as passwordErrorTip,E as passwordStrength,g as passwordTip,L as privacyPolicy,M as qrcodeLogin,H as qrcodePrompt,C as qrcodeSubtitle,u as rememberMe,i as selectAccount,K as sendCode,U as sendResetLink,N as sendText,S as signUp,f as signUpSubtitle,h as terms,O as thirdPartyLogin,r as username,l as usernameTip,d as verifyRequiredTip,o as welcomeBack};
