import {
  columnProps
} from "./chunk-MCGBUOKS.js";
import {
  assembleColumn,
  cell_default,
  destroyColumn,
  watchColumn
} from "./chunk-R73ZCAIJ.js";
import "./chunk-GW2WQRXE.js";
import "./chunk-RFZDZ2Z5.js";
import {
  VxeUI
} from "./chunk-TV7URO3H.js";
import "./chunk-JUL7CFXM.js";
import {
  createCommentVNode,
  defineComponent,
  h,
  inject,
  onMounted,
  onUnmounted,
  provide,
  ref
} from "./chunk-ZLVVKZUX.js";
import "./chunk-CMAVT37G.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/group.js
var group_default = defineComponent({
  name: "VxeColgroup",
  props: columnProps,
  setup(props, { slots }) {
    const refElem = ref();
    const $xeTable = inject("$xeTable", null);
    const $xeParentColgroup = inject("$xeColgroup", null);
    if (!$xeTable) {
      return () => createCommentVNode();
    }
    const columnConfig = cell_default.createColumn($xeTable, props);
    const columnSlots = {};
    if (slots.header) {
      columnSlots.header = slots.header;
    }
    columnConfig.slots = columnSlots;
    columnConfig.children = [];
    watchColumn($xeTable, props, columnConfig);
    onMounted(() => {
      const elem = refElem.value;
      if (elem) {
        assembleColumn($xeTable, elem, columnConfig, $xeParentColgroup);
      }
    });
    onUnmounted(() => {
      destroyColumn($xeTable, columnConfig);
    });
    const renderVN = () => {
      return h("div", {
        ref: refElem
      }, slots.default ? slots.default() : []);
    };
    const $xeColgroup = { columnConfig };
    provide("$xeColgroup", $xeColgroup);
    provide("$xeGrid", null);
    return renderVN;
  }
});

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/colgroup/index.js
var VxeColgroup = Object.assign({}, group_default, {
  install(app) {
    app.component(group_default.name, group_default);
    app.component("VxeTableColgroup", group_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(group_default.name, group_default);
  VxeUI.dynamicApp.component("VxeTableColgroup", group_default);
}
VxeUI.component(group_default);
var Colgroup = VxeColgroup;
var colgroup_default = VxeColgroup;

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-colgroup/index.js
var vxe_colgroup_default = colgroup_default;
export {
  Colgroup,
  VxeColgroup,
  vxe_colgroup_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-colgroup_index__js.js.map
