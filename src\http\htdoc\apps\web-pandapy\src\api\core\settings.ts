import { requestClient } from '#/api/request';

export async function getLocalSerialDevices() {
  return requestClient.get('/settings/general/getLocalSerialDevices');
}

export async function getSerialConfig() {
  return requestClient.get('/settings/general/getSerialConfig');
}

export async function setSerialConfig(values) {
  return requestClient.post('/settings/general/setSerialConfig', values);
}

export async function getJoystickKeyBindings() {
  return requestClient.get('/settings/general/getJoystickKeyBindings');
}

export async function setJoystickKeyBindings(values) {
  return requestClient.post('/settings/general/setJoystickKeyBindings', values);
}

export async function getJoystickKeyFunctionList() {
  return requestClient.get('/settings/general/getJoystickKeyFunctionList');
}

export async function getInputDevices() {
  return requestClient.get('/settings/general/getInputDevices');
}

export async function getInitialGCode() {
  return requestClient.get('/settings/general/getInitialGCode');
}

export async function setInitialGCode(values) {
  return requestClient.post('/settings/general/setInitialGCode', values);
}
export async function getStepperArgs() {
  return requestClient.get(`/settings/general/getStepperArgs`);
}

export async function setStepperArgs(values) {
  return requestClient.post(`/settings/general/setStepperArgs`, values);
}

export async function getGPIOBinding() {
  return requestClient.get('/settings/general/getGPIOBinding');
}

export async function setGPIOBinding(values) {
  return requestClient.post('/settings/general/setGPIOBinding', values);
}

export async function getNotificationSettings() {
  return requestClient.get('/settings/general/getNotificationSettings');
}

export async function setNotificationSettings(values) {
  return requestClient.post('/settings/general/setNotificationSettings', values);
}
