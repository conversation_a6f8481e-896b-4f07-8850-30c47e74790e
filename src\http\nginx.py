import os
import time
import subprocess
from src.service import code
from src.service import service
from src.http.nginx_cfggen import cfggen
from src.utils import utils

class nginx(service):

    _nginx_bin: str = None
    _nginx_conf: str = None
    _workdir: str = None
    _nginx_process: subprocess.Popen = None

    _stop_monitor = False

    def __ng_validate(self, path):
        # run command nginx validation
        _nginx_cmd = f"{self._nginx_bin} -t -c {path}"
        return os.system(_nginx_cmd)

    def __ng_start(self):
        try:
            # 使用 subprocess.Popen 启动 Nginx 作为新进程
            self._nginx_process = subprocess.Popen(
                [self._nginx_bin, "-c", self._nginx_conf, "-g", "daemon off;"],
                stdout = subprocess.PIPE,
                stderr = subprocess.PIPE,
                cwd = self._workdir,
                text = True
            )
            self.log_info(f"nginx started with pid: {self._nginx_process.pid}")
            return 0
        except Exception as e:
            self.log_fatal(f"failed to start nginx: {e}")
            return -1

    def init(self):

        from src.http.backend import backend
        if not self.context.get_service(backend):
            return code.DEFERRED

        # set path
        _workdir = utils.get_workdir()
        self._workdir = _workdir
        self._nginx_bin = f"{_workdir}/bin/nginx"
        self._nginx_conf = f"{_workdir}/nginx.conf"

        self.log_info("generating nginx config")

        if not os.path.exists(self._nginx_bin):
            fallbackBin = [f"{_workdir}/bin/nginx.exe", "/usr/sbin/nginx", "/usr/bin/nginx"]
            for b in fallbackBin:
                if os.path.exists(b):
                    self._nginx_bin = b

        # write nginx config
        _conf_file = cfggen.generate(self.context.get_config(), self._workdir)
        with open(self._nginx_conf, "w") as f:
            f.write(_conf_file)

        # run command for nginx validation
        _ret = self.__ng_validate(self._nginx_conf)
        if _ret != 0:
            self.log_fatal(f"nginx config validation failed: -{_ret}")
            # os.remove(_nginx_conf)
            return code.FAILURE

        self.log_info(f"nginx config written to `{self._nginx_conf}`")
        return code.SUCCESS

    def start(self):
        
        self._stop_monitor = False
        self.__ng_start()

        while self._stop_monitor is False:
            
            try:
                if self._nginx_process:
                    # 只调用一次 poll()
                    exit_code = self._nginx_process.poll()
                    if exit_code is not None:
                        self.log_warning(f"nginx exited with code: {exit_code}, restarting...")
                        self.__ng_start()
            except Exception as e:
                self.log_fatal(f"error monitoring nginx: {e}")
            
            time.sleep(5)  # 每5秒检查一次

    def stop(self):

        # 设置停止标志
        self._stop_monitor = True
        
        # 终止 Nginx 进程
        if self._nginx_process:
            try:
                # 发送终止信号
                self._nginx_process.terminate()
                
                # 等待进程终止，最多等待10秒
                try:
                    self._nginx_process.wait(timeout=10)
                    self.log_info("nginx stopped gracefully")
                except subprocess.TimeoutExpired:
                    # 如果超时，强制终止
                    self._nginx_process.kill()
                    self.log_warning("nginx terminated forcefully")
                
            except Exception as e:
                self.log_fatal(f"error stopping nginx: {e}")
            finally:
                self._nginx_process = None

    def get_name(self):
        return "http-nginx"

    def get_status(self):
        return code.SUCCESS
