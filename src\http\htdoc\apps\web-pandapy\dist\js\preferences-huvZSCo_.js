import{u as p,_ as N}from"./form-1Qpdj_eE.js";import{E as P,F as i,_}from"./bootstrap-MyT3sENS.js";import{g as x,f as m,h as c}from"./job-CyTXo-tT.js";import{d as y,r as h,j as z,e as v,o as d,s as r,v as a,g as t,u as o}from"../jse/index-index-Y3_OtjO-.js";import"./index-DGcxnQ4T.js";const F={key:1,class:"non-loaded"},w=y({__name:"preferences",setup(A){const l=P(),s=h(!1);x().then(e=>{if(e==null){s.value=!1;return}s.value=!0,m("plannerArgs").then(n=>{g.setValues(n)}),m("filmArgs").then(n=>{f.setValues(n)})});const[u,f]=p({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{c("filmArgs",e).then(()=>{l.success("设置已保存")}).catch(n=>{l.error(`保存失败: ${n.message}`)})},resetButtonOptions:{show:!1},schema:[{component:"InputNumber",fieldName:"repeat",label:"循环次数",componentProps:{min:1}},{component:"InputNumber",fieldName:"filmCount",label:"单点拍照次数",componentProps:{min:1}},{component:"InputNumber",fieldName:"beforeFilmDelay",label:"触发前等待时间",componentProps:{min:0}},{component:"InputNumber",fieldName:"afterFilmDelay",label:"触发后等待时间",componentProps:{min:0}},{component:"InputNumber",fieldName:"filmDuration",label:"拍照触发时长",componentProps:{min:0}}]}),[b,g]=p({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{c("plannerArgs",e).then(()=>{l.success("设置已保存")}).catch(n=>{l.error(`保存失败: ${n.message}`)})},resetButtonOptions:{show:!1},schema:[{component:"Select",fieldName:"scanMode",label:"扫描模式",componentProps:{options:[{label:"垂直扫描 (Z 轴)",value:"z_only"},{label:"平面扫描 (XY 轴)",value:"xy_only"},{label:"立体扫描 (XYZ 轴)",value:"xyz"}]}},{component:"RadioGroup",fieldName:"preferredAxis",label:"优先轴",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"x",label:"X 轴优先"},{value:"y",label:"Y 轴优先"}]},dependencies:{disabled(e){return e.scanMode=="z_only"},triggerFields:["scanMode"]}},{component:"RadioGroup",fieldName:"xPlannerPath",label:"X轴行进方式",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"zigzag",label:"Zig-Zag"},{value:"snake",label:"蛇形"}]},dependencies:{triggerFields:["scanMode","preferredAxis"],disabled(e){return e.scanMode=="z_only"||e.preferredAxis=="y"}}},{component:"RadioGroup",fieldName:"yPlannerPath",label:"Y轴行进方式",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"zigzag",label:"Zig-Zag"},{value:"snake",label:"蛇形"}]},dependencies:{triggerFields:["scanMode","preferredAxis"],disabled(e){return e.scanMode=="z_only"||e.preferredAxis=="x"}}},{component:"RadioGroup",fieldName:"zPlannerPath",label:"Z轴行进方式",componentProps:{isButton:!0,class:"flex flex-wrap",options:[{value:"zigzag",label:"Zig-Zag"},{value:"snake",label:"蛇形"}]},dependencies:{triggerFields:["scanMode"],disabled(e){return e.scanMode=="z_only"||e.scanMode=="xy_only"}}},{component:"InputNumber",fieldName:"scanXStep",label:"X轴扫描步长",componentProps:{min:0}},{component:"InputNumber",fieldName:"scanYStep",label:"Y轴扫描步长",componentProps:{min:0}},{component:"InputNumber",fieldName:"scanZStep",label:"Z轴扫描步长",componentProps:{min:0}}]});return(e,n)=>s.value?(d(),z(o(N),{key:0,title:"偏好设置",description:"本页面用于设定当前工程的参数"},{default:r(()=>[a(o(i),{title:"拍照参数"},{default:r(()=>[a(o(u))]),_:1}),n[0]||(n[0]=t("br",null,null,-1)),a(o(i),{title:"路径规划器"},{default:r(()=>[a(o(b))]),_:1})]),_:1})):(d(),v("div",F,n[1]||(n[1]=[t("span",null,"工程尚未加载",-1),t("span",null,"请先打开或创建一个工程文件",-1)])))}}),k=_(w,[["__scopeId","data-v-c6eb27b7"]]);export{k as default};
