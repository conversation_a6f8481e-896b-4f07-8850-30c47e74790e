import {
  VxeUI,
  index_esm_default,
  require_xe_utils
} from "./chunk-TV7URO3H.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/utils.js
var import_xe_utils = __toESM(require_xe_utils());
function isEnableConf(conf) {
  return conf && conf.enabled !== false;
}
function isEmptyValue(cellValue) {
  return cellValue === null || cellValue === void 0 || cellValue === "";
}
function parseFile(file) {
  const name = file.name;
  const tIndex = import_xe_utils.default.lastIndexOf(name, ".");
  const type = name.substring(tIndex + 1, name.length).toLowerCase();
  const filename = name.substring(0, tIndex);
  return { filename, type };
}
function nextZIndex() {
  return index_esm_default.getNext();
}
function getLastZIndex() {
  return index_esm_default.getCurrent();
}
function hasChildrenList(item) {
  return item && item.children && item.children.length > 0;
}
function getFuncText(content, args) {
  if (content) {
    const translate = VxeUI.getConfig().translate;
    return import_xe_utils.default.toValueString(translate ? translate("" + content, args) : content);
  }
  return "";
}
function formatText(value, placeholder) {
  return "" + (isEmptyValue(value) ? placeholder ? VxeUI.getConfig().emptyCell : "" : value);
}
function eqEmptyValue(cellValue) {
  return cellValue === "" || import_xe_utils.default.eqNull(cellValue);
}

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/ui/index.js
var version = "4.13.16";
VxeUI.version = version;
VxeUI.tableVersion = version;
VxeUI.setConfig({
  emptyCell: "　",
  table: {
    fit: true,
    showHeader: true,
    animat: true,
    delayHover: 250,
    autoResize: true,
    minHeight: 144,
    // keepSource: false,
    // showOverflow: null,
    // showHeaderOverflow: null,
    // showFooterOverflow: null,
    // resizeInterval: 500,
    // size: null,
    // zIndex: null,
    // stripe: false,
    // border: false,
    // round: false,
    // emptyText: '暂无数据',
    // emptyRender: {
    //   name: ''
    // },
    // rowConfig: {
    //   keyField: '_X_ROW_KEY' // 行数据的唯一主键字段名
    // },
    resizeConfig: {
      // refreshDelay: 20
    },
    resizableConfig: {
      dragMode: "auto",
      showDragTip: true,
      isSyncAutoHeight: true,
      isSyncAutoWidth: true,
      minHeight: 18
    },
    radioConfig: {
      // trigger: 'default'
      strict: true
    },
    rowDragConfig: {
      showIcon: true,
      animation: true,
      showGuidesStatus: true,
      showDragTip: true
    },
    columnDragConfig: {
      showIcon: true,
      animation: true,
      showGuidesStatus: true,
      showDragTip: true
    },
    checkboxConfig: {
      // trigger: 'default',
      strict: true
    },
    tooltipConfig: {
      enterable: true
    },
    validConfig: {
      showMessage: true,
      autoClear: true,
      autoPos: true,
      message: "inline",
      msgMode: "single",
      theme: "beautify"
    },
    columnConfig: {
      maxFixedSize: 4
    },
    cellConfig: {
      padding: true
    },
    headerCellConfig: {
      height: "unset"
    },
    footerCellConfig: {
      height: "unset"
    },
    // menuConfig: {
    //   visibleMethod () {}
    // },
    customConfig: {
      // enabled: false,
      allowVisible: true,
      allowResizable: true,
      allowFixed: true,
      allowSort: true,
      showFooter: true,
      placement: "top-right",
      //  storage: false,
      //  checkMethod () {},
      modalOptions: {
        showMaximize: true,
        mask: true,
        lockView: true,
        resize: true,
        escClosable: true
      },
      drawerOptions: {
        mask: true,
        lockView: true,
        escClosable: true,
        resize: true
      }
    },
    sortConfig: {
      // remote: false,
      // trigger: 'default',
      // orders: ['asc', 'desc', null],
      // sortMethod: null,
      showIcon: true,
      allowClear: true,
      allowBtn: true,
      iconLayout: "vertical"
    },
    filterConfig: {
      // remote: false,
      // filterMethod: null,
      // destroyOnClose: false,
      // isEvery: false,
      showIcon: true
    },
    rowGroupConfig: {
      padding: true,
      rowField: "id",
      parentField: "_X_ROW_PARENT_KEY",
      childrenField: "_X_ROW_CHILDREN",
      mapChildrenField: "_X_ROW_CHILD_LIST",
      indent: 20,
      showIcon: true
    },
    treeConfig: {
      padding: true,
      rowField: "id",
      parentField: "parentId",
      childrenField: "children",
      hasChildField: "hasChild",
      mapChildrenField: "_X_ROW_CHILD",
      indent: 20,
      showIcon: true
    },
    expandConfig: {
      // trigger: 'default',
      showIcon: true,
      mode: "fixed"
    },
    editConfig: {
      // mode: 'cell',
      showIcon: true,
      showAsterisk: true,
      autoFocus: true
    },
    importConfig: {
      _typeMaps: {
        csv: 1,
        html: 1,
        xml: 1,
        txt: 1
      }
    },
    exportConfig: {
      _typeMaps: {
        csv: 1,
        html: 1,
        xml: 1,
        txt: 1
      }
    },
    printConfig: {},
    mouseConfig: {
      extension: true
    },
    keyboardConfig: {
      isEsc: true
    },
    areaConfig: {
      autoClear: true,
      selectCellByHeader: true,
      selectCellByBody: true,
      extendDirection: {
        top: true,
        left: true,
        bottom: true,
        right: true
      }
    },
    clipConfig: {
      isCopy: true,
      isCut: true,
      isPaste: true
    },
    fnrConfig: {
      isFind: true,
      isReplace: true
    },
    virtualXConfig: {
      enabled: false,
      gt: 24,
      preSize: 1,
      oSize: 0
    },
    virtualYConfig: {
      enabled: false,
      gt: 100,
      preSize: 1,
      oSize: 0
    },
    scrollbarConfig: {
      // width: 14,
      // height: 14
    }
  },
  // export: {
  //   types: {}
  // },
  grid: {
    // size: null,
    // zoomConfig: {
    //   escRestore: true
    // },
    formConfig: {
      enabled: true
    },
    pagerConfig: {
      enabled: true
      // perfect: false
    },
    toolbarConfig: {
      enabled: true
      // perfect: false
    },
    proxyConfig: {
      enabled: true,
      autoLoad: true,
      showResponseMsg: true,
      showActiveMsg: true,
      props: {
        list: null,
        result: "result",
        total: "page.total",
        message: "message"
      }
      // beforeItem: null,
      // beforeColumn: null,
      // beforeQuery: null,
      // afterQuery: null,
      // beforeDelete: null,
      // afterDelete: null,
      // beforeSave: null,
      // afterSave: null
    }
  },
  toolbar: {
    // size: null,
    // import: {
    //   mode: 'covering'
    // },
    // export: {
    //   types: ['csv', 'html', 'xml', 'txt']
    // },
    // buttons: []
  }
});
var iconPrefix = "vxe-table-icon-";
VxeUI.setIcon({
  // table
  TABLE_SORT_ASC: iconPrefix + "caret-up",
  TABLE_SORT_DESC: iconPrefix + "caret-down",
  TABLE_FILTER_NONE: iconPrefix + "funnel",
  TABLE_FILTER_MATCH: iconPrefix + "funnel",
  TABLE_EDIT: iconPrefix + "edit",
  TABLE_TITLE_PREFIX: iconPrefix + "question-circle-fill",
  TABLE_TITLE_SUFFIX: iconPrefix + "question-circle-fill",
  TABLE_TREE_LOADED: iconPrefix + "spinner roll",
  TABLE_TREE_OPEN: iconPrefix + "caret-right rotate90",
  TABLE_TREE_CLOSE: iconPrefix + "caret-right",
  TABLE_EXPAND_LOADED: iconPrefix + "spinner roll",
  TABLE_EXPAND_OPEN: iconPrefix + "arrow-right rotate90",
  TABLE_EXPAND_CLOSE: iconPrefix + "arrow-right",
  TABLE_CHECKBOX_CHECKED: iconPrefix + "checkbox-checked-fill",
  TABLE_CHECKBOX_UNCHECKED: iconPrefix + "checkbox-unchecked",
  TABLE_CHECKBOX_INDETERMINATE: iconPrefix + "checkbox-indeterminate-fill",
  TABLE_RADIO_CHECKED: iconPrefix + "radio-checked-fill",
  TABLE_RADIO_UNCHECKED: iconPrefix + "radio-unchecked",
  TABLE_CUSTOM_SORT: iconPrefix + "drag-handle",
  TABLE_MENU_OPTIONS: iconPrefix + "arrow-right",
  TABLE_DRAG_ROW: iconPrefix + "drag-handle",
  TABLE_DRAG_COLUMN: iconPrefix + "drag-handle",
  TABLE_DRAG_STATUS_ROW: iconPrefix + "sort",
  TABLE_DRAG_STATUS_SUB_ROW: iconPrefix + "add-sub",
  TABLE_DRAG_STATUS_COLUMN: iconPrefix + "swap",
  TABLE_DRAG_DISABLED: iconPrefix + "no-drop",
  TABLE_ROW_GROUP_OPEN: iconPrefix + "arrow-right rotate90",
  TABLE_ROW_GROUP_CLOSE: iconPrefix + "arrow-right",
  // toolbar
  TOOLBAR_TOOLS_REFRESH: iconPrefix + "repeat",
  TOOLBAR_TOOLS_REFRESH_LOADING: iconPrefix + "repeat roll",
  TOOLBAR_TOOLS_IMPORT: iconPrefix + "upload",
  TOOLBAR_TOOLS_EXPORT: iconPrefix + "download",
  TOOLBAR_TOOLS_PRINT: iconPrefix + "print",
  TOOLBAR_TOOLS_FULLSCREEN: iconPrefix + "fullscreen",
  TOOLBAR_TOOLS_MINIMIZE: iconPrefix + "minimize",
  TOOLBAR_TOOLS_CUSTOM: iconPrefix + "custom-column",
  TOOLBAR_TOOLS_FIXED_LEFT: iconPrefix + "fixed-left",
  TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE: iconPrefix + "fixed-left-fill",
  TOOLBAR_TOOLS_FIXED_RIGHT: iconPrefix + "fixed-right",
  TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE: iconPrefix + "fixed-right-fill"
});
var setTheme = VxeUI.setTheme;
var getTheme = VxeUI.getTheme;
var setConfig = VxeUI.setConfig;
var getConfig = VxeUI.getConfig;
var setIcon = VxeUI.setIcon;
var getIcon = VxeUI.getIcon;
var setLanguage = VxeUI.setLanguage;
var setI18n = VxeUI.setI18n;
var getI18n = VxeUI.getI18n;
var globalEvents = VxeUI.globalEvents;
var globalResize = VxeUI.globalResize;
var renderer = VxeUI.renderer;
var validators = VxeUI.validators;
var menus = VxeUI.menus;
var formats = VxeUI.formats;
var commands = VxeUI.commands;
var interceptor = VxeUI.interceptor;
var clipboard = VxeUI.clipboard;
var log = VxeUI.log;
var hooks = VxeUI.hooks;
var use = VxeUI.use;
var setup = (options) => {
  return VxeUI.setConfig(options);
};
VxeUI.setup = setup;
var config = (options) => {
  return VxeUI.setConfig(options);
};
VxeUI.config = config;
var t = (key, args) => {
  return VxeUI.getI18n(key, args);
};
VxeUI.t = t;
var _t = (content, args) => {
  return getFuncText(content, args);
};
VxeUI._t = _t;
var VXETable = VxeUI;
var saveFile = (options) => {
  return VxeUI.saveFile(options);
};
var readFile = (options) => {
  return VxeUI.readFile(options);
};
var print = (options) => {
  return VxeUI.print(options);
};
var modal = {
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  get(id) {
    return VxeUI.modal.get(id);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  close(id) {
    return VxeUI.modal.close(id);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  open(options) {
    return VxeUI.modal.open(options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  alert(content, title, options) {
    return VxeUI.modal.alert(content, title, options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  confirm(content, title, options) {
    return VxeUI.modal.confirm(content, title, options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  message(content, options) {
    return VxeUI.modal.message(content, options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  notification(content, title, options) {
    return VxeUI.modal.notification(content, title, options);
  }
};
var ui_default = VxeUI;

export {
  isEnableConf,
  isEmptyValue,
  parseFile,
  nextZIndex,
  getLastZIndex,
  hasChildrenList,
  getFuncText,
  formatText,
  eqEmptyValue,
  version,
  setTheme,
  getTheme,
  setConfig,
  getConfig,
  setIcon,
  getIcon,
  setLanguage,
  setI18n,
  getI18n,
  globalEvents,
  globalResize,
  renderer,
  validators,
  menus,
  formats,
  commands,
  interceptor,
  clipboard,
  log,
  hooks,
  use,
  setup,
  config,
  t,
  _t,
  VXETable,
  saveFile,
  readFile,
  print,
  modal,
  ui_default
};
//# sourceMappingURL=chunk-RFZDZ2Z5.js.map
