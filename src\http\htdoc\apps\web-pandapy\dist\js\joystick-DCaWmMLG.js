import{u as l,_ as y}from"./form-1Qpdj_eE.js";import{E as b,F as u}from"./bootstrap-MyT3sENS.js";import{c as p,d as w,e as k,f as v}from"./settings-BETc9HZJ.js";import{NDivider as d}from"./index-DoPwuNMq.js";import{d as S,j as _,o as B,s as a,v as n,g as r,u as o}from"../jse/index-index-Y3_OtjO-.js";import"./index-DGcxnQ4T.js";const z=S({__name:"joystick",setup(C){const t=b(),[f,F]=l({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{t.success(`表单数据：${JSON.stringify(e)}`)},resetButtonOptions:{show:!1},schema:[{component:"ApiSelect",fieldName:"joystickDevice",label:"摇杆设备",rules:"selectRequired",componentProps:{api:p,afterFetch:e=>e.devices.map(s=>({label:s.device,value:s.device}))}}]});l({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{t.success(`表单数据：${JSON.stringify(e)}`)},schema:[]});const[g,h]=l({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1",handleSubmit:e=>{w({bindings:e}).then(()=>{t.success("按键绑定已保存")}).catch(s=>{console.error(`保存失败: ${s.message}`)})},resetButtonOptions:{show:!1},schema:[]});return k().then(e=>!e.functions||e.functions.length<=0?(t.warning("没有找到摇杆按键绑定信息"),!1):e.functions).then(e=>{v().then(s=>{if(!s.bindings||s.bindings.length<=0){t.warning("没有找到摇杆按键绑定信息");return}h.setState(N=>{let c=[];return s.bindings.forEach(i=>{c.push({field:i.name,label:i.option.name,value:i.option.bind_func,component:"Select",componentProps:{options:e.map(m=>({label:m.name,value:m.value})),placeholder:"请选择绑定功能",clearable:!0},fieldName:`${i.name}`})}),{schema:c}})})}),p().then(e=>{console.log("获取到的输入设备",e)}),(e,s)=>(B(),_(o(y),{description:"本页面包含手柄和摇杆的配置项目。可以完成包括摇杆死区、按键功能、绑定选项等设定",title:"手柄和摇杆"},{default:a(()=>[n(o(u),{title:"摇杆设备"},{default:a(()=>[s[0]||(s[0]=r("p",null,"设置系统使用的摇杆设备",-1)),n(o(d),{class:"my-4"}),n(o(f))]),_:1}),s[2]||(s[2]=r("br",null,null,-1)),n(o(u),{title:"功能键绑定"},{default:a(()=>[s[1]||(s[1]=r("p",null,"绑定按键的功能可以在当前的工作流中立即生效",-1)),n(o(d),{class:"my-4"}),n(o(g))]),_:1})]),_:1}))}});export{z as default};
