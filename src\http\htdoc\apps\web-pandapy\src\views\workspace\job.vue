<template><root>

  <jobpage v-show="jobLoaded">
    <div class="container">
      <div class="job-cube">
        <JobCube bkgColor="#14161a" :animate="isRunning" :zoom="isRunning ? 4.5: 5.5" />
      </div>
      <div class="job-information">
        <h1>{{ statusText }}</h1>
        <NProgress v-if="isRunning" style="margin-bottom: 24px;" :showIndicator="false" :percentage="progress" />
        <div class="buttons">
          <NButton strong round type="primary" v-if="!isRunning && !isPause" @click="startRunClicked">一键开始</NButton>
          <NButton strong round type="primary" v-if="isRunning && !isPause" @click="pauseRunClicked">暂停执行</NButton>
          <NButton strong round type="primary" v-if="isPause" @click="resumeClicked">恢复执行</NButton>
          <NButton strong round type="error" v-if="isRunning || isPause" @click="stopRunClicked">停止执行</NButton>
        </div>
      </div>
    </div>
    <StatBar :message="messageText"></statbar>
    <Page id="page-control" contentClass="p-6" title="控制选项" :description="pageControlHint" >
      <!-- <div style="position: absolute;top: 2rem;right: 0px;margin-right: 1rem;">
        <NCheckbox v-model="isUnlockedManually" @update:checked="handleCheckboxChange">解锁控制</NCheckbox>
      </div> -->
      <NButton :disabled="isRunning" @click="jobMachineControl('homming')" type="primary">回到零位置</NButton>
      <NButton :disabled="isRunning" @click="jobMachineControl('go_scan_start')" type="primary">运行到扫描起点</NButton>
      <NButton :disabled="isRunning" @click="jobMachineControl('go_scan_end')" type="primary">运行到扫描终点</NButton>
      <NButton :disabled="isRunning" @click="jobMachineControl('set_scan_start')" type="primary">设置当前位置为扫描起点</NButton>
      <NButton :disabled="isRunning" @click="jobMachineControl('set_scan_end')" type="primary">设置当前位置为扫描终点</NButton>
      <NButton :disabled="isRunning" @click="jobMachineControl('swap_scan')" type="info">交换起点和终点</NButton>
    </Page>
    <Page id="page-info" contentClass="p-0" class="job-page">
      <p>任务名称：{{ jobName }}</p>
      <p>执行状态：{{ statusText }}</p>
      <NButton :disabled="isRunning" type="primary" @click="saveJobFile">保存</NButton>
      <NButton :disabled="isRunning" type="error" @click="closeJobFile">关闭工程文件</NButton>
    </Page>
  </jobpage>

  <addjob v-show="!jobLoaded">
    <h1>工程尚未加载</h1>
    <div class="divider"></div>
    <span>你可以</span>
    <NButton type="primary" @click="openJobSelectionModal">打开现有工程文件</NButton>
    <span>或者</span>
    <NButton type="primary" @click="createJobModalApi.open()">新建一个工程</NButton>

    <JobSelectModal
      :title="'最近打开的工程'"
      :close-on-press-escape="false"
      :fullscreen-button="false"
      :overlayBlur="10"
      :footer="false"
      :draggable="false">
      <ul class="job-select-list">
        <li v-for="job in jobList" @click="jobSelection(job)">
          <span>{{ job.name }}</span>
          <span>{{ job.date }}</span>
        </li>
      </ul>
    </JobSelectModal>
    <CreateJobModal
      :title="'新建工程'"
      :close-on-press-escape="false"
      :fullscreen-button="false"
      :overlayBlur="10"
      :confirmText="'提交'"
      :draggable="false"
    >
    <CreateJobForm></CreateJobForm>
    </CreateJobModal>
  </addjob>

</root></template>

<style lang="scss" scoped>
.container {
  padding-left: 10%;
  padding-right: 10%;
}

.job-cube {
  width: 300px;
  height: 300px;
  display: table-cell;
}

.job-information {
  display: table-cell;
  padding-left: 10%;;
  vertical-align: middle;;
  width: 60%;
}

.job-information h1 {
  margin-top: 10px;
  margin-bottom: 24px;
  font-size: 30px;
  text-align: center;
}

.buttons {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.n-button {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

#page-info p {
  margin-bottom: 0.5rem;
  font-size: small;
}

addjob {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
addjob h1 {
  font-size: 48px;
  font-weight: bold;
  font-family: 'Arial', sans-serif;
  font-weight: 600;
  margin-bottom: 16px;
}

addjob span {
  margin-bottom: 16px;
}

addjob .divider {
  width: 30%;
  height: 1px;
  border: #ffffff22 solid;
  border-width: 1px;
  border-bottom: 1px;
  margin: 2rem;
}

.job-select-list {
  list-style: none;
  li {
    display: flex;
    flex-direction: row;
    padding: 0.5rem 1rem 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.1s ease;
      &:hover {
        background-color: #14161a;
      }

      span:last-child {
        margin-left: auto;
        opacity: 0.5;
        font-size: smaller;
      }
  }
}

.job-page {
  padding: 0rem 1.5rem 1rem 1.5rem;
}
</style>

<script lang="ts" setup>
import { Page, useVbenModal } from '@vben/common-ui';
import { NButton, NProgress, NCheckbox, useMessage } from 'naive-ui';
import { JobCube, StatBar } from './control/common';
import { onMounted, onUnmounted, ref } from 'vue';
import { getJobList, openJob, closeJob, saveJob, getCurrentJob,
  createNewJob, getJobStatus, jobControl, jobMachineControl } from '#/api/index';
import { useVbenForm } from '#/adapter/form';

const messageBox = useMessage();

const jobName = ref('');
const jobLoaded = ref(false);
getCurrentJob().then(r => {

  if(r == null) {
    jobLoaded.value = false;
    jobName.value = '';
    return;
  }

  jobLoaded.value = true;
  jobName.value = r;
});

const [JobSelectModal, jobSelectModalApi] = useVbenModal();
const jobList = ref([]);
const openJobSelectionModal = () => {
  jobList.value = [];
  getJobList().then((res) => {
    res.forEach(i=>jobList.value.push(i));
    jobSelectModalApi.open();
  }).catch((error) => {
    console.error('获取工程列表失败:', error);
  });
};

const jobSelection = (job: any) => {
  console.log('选择的工程:', job.name);
  openJob(job.name).then((res) => {
    if(!res) {
      messageBox.error("打开工程失败")
      return;
    }
    jobName.value = job.name;
    jobSelectModalApi.close();
    jobLoaded.value = true;
    messageBox.success(`工程 '${job.name}' 已加载`);
  }).catch((error) => {
    console.error('打开工程失败:', error);
  });
}

const closeJobFile = () => {
  closeJob().then(() => {
    jobName.value = '';
    jobLoaded.value = false;
    messageBox.success('工程文件已关闭');
  }).catch((error) => {
    console.error('关闭工程失败:', error);
  });
};

const saveJobFile = () => {
  saveJob().then(() => {
    messageBox.success('工程文件已保存');
  }).catch((error) => {
    console.error(error.message);
  });
};

const [CreateJobModal, createJobModalApi] = useVbenModal();
const [CreateJobForm, createJobFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {

    createNewJob(values.jobName).then((res) => {
      if(!res) {
        messageBox.error("创建工程失败 -1");
        return;
      }

      openJob(values.jobName).then((res) => {
        if(!res) {
          messageBox.error("创建工程失败 -2");
          return;
        }
        jobName.value = values.jobName;
        jobLoaded.value = true;
        createJobModalApi.close();
        messageBox.success(`工程 '${values.jobName}' 已创建`);

      });

    }).catch((error) => {
      console.error('创建工程失败:', error);
    });

  },
  resetButtonOptions: {
    show: false
  },
  submitButtonOptions: {
    show: false
  },
  schema: [
    {
      component: "Input",
      fieldName: "jobName",
      label: "工程名",
      rules: "required",
      componentProps:{
        showButtons: false,
        placeholder: '未命名工程',
      }
    },
  ],
});

createJobModalApi.onConfirm = () => {
  createJobFormApi.validateAndSubmitForm();
};

const statusText = ref('加载数据...');
const progress = ref(0);

const pageControlHint = ref('加载数据...');

const isRunning = ref(false);
const isPause= ref(false);
const startRunClicked = () => {
  jobControl('start');
};
const pauseRunClicked = () => {
  jobControl('pause');
};

const resumeClicked = () => {
  jobControl('resume');
};

const stopRunClicked = () => {
  jobControl('stop');
};

let intervalId;

onMounted(() => {
  intervalId = setInterval(() => {
    if(jobLoaded.value) {
      updateJobStatus();
    }
  }, 500);
});

onUnmounted(() => {
  clearInterval(intervalId);
});

const messageText = ref('加载数据...');

const updateJobStatus = () => {
  getJobStatus().then((res) => {

    console.log('获取任务状态:', res);

    messageText.value = res.message || '';
    if(res.status === 'running') {
      isRunning.value = true;
      isPause.value = false;
      statusText.value = `正在扫描 ${res.progress}%`;
      progress.value = res.progress;
    } else if(res.status === 'paused') {
      isRunning.value = false;
      isPause.value = true;
      statusText.value = `已暂停 ${res.progress}%`;
      progress.value = res.progress;
    } else if(res.status === 'planning') {
      isRunning.value = true;
      isPause.value = false;
      statusText.value = `规划路径中`;
      progress.value = res.progress;
    } else {
      isRunning.value = false;
      isPause.value = false;
      statusText.value = '等待中';
      progress.value = 0;
    }
  }).catch((error) => {
    console.error('获取任务状态失败:', error);
  });

  if(!isRunning.value) {
    pageControlHint.value = '已解锁控制按钮，请谨慎操作';
  } else {
    pageControlHint.value = '任务运行中不可操作';
  }
};

</script>
