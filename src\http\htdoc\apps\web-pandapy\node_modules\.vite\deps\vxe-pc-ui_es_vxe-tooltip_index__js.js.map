{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-tooltip/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeTooltipComponent from './src/tooltip';\nimport { dynamicApp } from '../dynamics';\nexport const VxeTooltip = Object.assign({}, VxeTooltipComponent, {\n    install(app) {\n        app.component(VxeTooltipComponent.name, VxeTooltipComponent);\n    }\n});\ndynamicApp.use(VxeTooltip);\nVxeUI.component(VxeTooltipComponent);\nexport const Tooltip = VxeTooltip;\nexport default VxeTooltip;\n", "import VxeTooltip from '../tooltip';\nexport * from '../tooltip';\nexport default VxeTooltip;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGO,IAAM,aAAa,OAAO,OAAO,CAAC,GAAG,iBAAqB;AAAA,EAC7D,QAAQ,KAAK;AACT,QAAI,UAAU,gBAAoB,MAAM,eAAmB;AAAA,EAC/D;AACJ,CAAC;AACD,WAAW,IAAI,UAAU;AACzB,MAAM,UAAU,eAAmB;AAC5B,IAAM,UAAU;AACvB,IAAOA,mBAAQ;;;ACTf,IAAO,sBAAQC;", "names": ["tooltip_default", "tooltip_default"]}