{"version": 3, "sources": ["../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/switch/styles/_common.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/switch/styles/dark.mjs", "../../../../../node_modules/.pnpm/naive-ui@2.41.0_vue@3.5.13_typescript@5.8.3_/node_modules/naive-ui/es/switch/styles/light.mjs"], "sourcesContent": ["export default {\n  buttonHeightSmall: '14px',\n  buttonHeightMedium: '18px',\n  buttonHeightLarge: '22px',\n  buttonWidthSmall: '14px',\n  buttonWidthMedium: '18px',\n  buttonWidthLarge: '22px',\n  buttonWidthPressedSmall: '20px',\n  buttonWidthPressedMedium: '24px',\n  buttonWidthPressedLarge: '28px',\n  railHeightSmall: '18px',\n  railHeightMedium: '22px',\n  railHeightLarge: '26px',\n  railWidthSmall: '32px',\n  railWidthMedium: '40px',\n  railWidthLarge: '48px'\n};", "import { changeColor } from 'seemly';\nimport { commonDark } from \"../../_styles/common/index.mjs\";\nimport commonVars from \"./_common.mjs\";\nconst switchDark = {\n  name: 'Switch',\n  common: commonDark,\n  self(vars) {\n    const {\n      primaryColorSuppl,\n      opacityDisabled,\n      borderRadius,\n      primaryColor,\n      textColor2,\n      baseColor\n    } = vars;\n    const railOverlayColor = 'rgba(255, 255, 255, .20)';\n    return Object.assign(Object.assign({}, commonVars), {\n      iconColor: baseColor,\n      textColor: textColor2,\n      loadingColor: primaryColorSuppl,\n      opacityDisabled,\n      railColor: railOverlayColor,\n      railColorActive: primaryColorSuppl,\n      buttonBoxShadow: '0px 2px 4px 0 rgba(0, 0, 0, 0.4)',\n      buttonColor: '#FFF',\n      railBorderRadiusSmall: borderRadius,\n      railBorderRadiusMedium: borderRadius,\n      railBorderRadiusLarge: borderRadius,\n      buttonBorderRadiusSmall: borderRadius,\n      buttonBorderRadiusMedium: borderRadius,\n      buttonBorderRadiusLarge: borderRadius,\n      boxShadowFocus: `0 0 8px 0 ${changeColor(primaryColor, {\n        alpha: 0.3\n      })}`\n    });\n  }\n};\nexport default switchDark;", "import { changeColor } from 'seemly';\nimport { commonLight } from \"../../_styles/common/index.mjs\";\nimport commonVars from \"./_common.mjs\";\nfunction self(vars) {\n  const {\n    primaryColor,\n    opacityDisabled,\n    borderRadius,\n    textColor3\n  } = vars;\n  const railOverlayColor = 'rgba(0, 0, 0, .14)';\n  return Object.assign(Object.assign({}, commonVars), {\n    iconColor: textColor3,\n    textColor: 'white',\n    loadingColor: primaryColor,\n    opacityDisabled,\n    railColor: railOverlayColor,\n    railColorActive: primaryColor,\n    buttonBoxShadow: '0 1px 4px 0 rgba(0, 0, 0, 0.3), inset 0 0 1px 0 rgba(0, 0, 0, 0.05)',\n    buttonColor: '#FFF',\n    railBorderRadiusSmall: borderRadius,\n    railBorderRadiusMedium: borderRadius,\n    railBorderRadiusLarge: borderRadius,\n    buttonBorderRadiusSmall: borderRadius,\n    buttonBorderRadiusMedium: borderRadius,\n    buttonBorderRadiusLarge: borderRadius,\n    boxShadowFocus: `0 0 0 2px ${changeColor(primaryColor, {\n      alpha: 0.2\n    })}`\n  });\n}\nconst switchLight = {\n  name: 'Switch',\n  common: commonLight,\n  self\n};\nexport default switchLight;"], "mappings": ";;;;;;;;;AAAA,IAAO,iBAAQ;AAAA,EACb,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,gBAAgB;AAClB;;;ACbA,IAAM,aAAa;AAAA,EACjB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,KAAK,MAAM;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,mBAAmB;AACzB,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAU,GAAG;AAAA,MAClD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA,MACd;AAAA,MACA,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,uBAAuB;AAAA,MACvB,yBAAyB;AAAA,MACzB,0BAA0B;AAAA,MAC1B,yBAAyB;AAAA,MACzB,gBAAgB,aAAa,YAAY,cAAc;AAAA,QACrD,OAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AACF;AACA,IAAOA,gBAAQ;;;AClCf,SAAS,KAAK,MAAM;AAClB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,mBAAmB;AACzB,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAU,GAAG;AAAA,IAClD,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gBAAgB,aAAa,YAAY,cAAc;AAAA,MACrD,OAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR;AACF;AACA,IAAOC,iBAAQ;", "names": ["dark_default", "light_default"]}