import time

class timecalc:

    _time: float = 0.0
    _duration: float = 0.0
    # 存储每次 feed 的离散时长（毫秒）
    _durations: list[float] = []

    def feed(self):
        t = time.time()
        if t > self._time:
            # 计算本次间隔，单位秒
            self._duration = t - self._time
            # 记录为毫秒
            self._durations.append(self._duration * 1000)
            self._time = t
        else:
            self._duration = 0.0

            # 如果超过100个记录，清空头部
        if len(self._durations) > 100:
            self._durations.pop(0)

    def get_average_duration(self) -> float:
        """
        根据存储的离散时长（ms）计算平均值
        """
        if not self._durations:
            return 0.0
        return sum(self._durations) / len(self._durations)

    def reset(self):
        """
        重置计时器和时长记录
        """
        self._time = time.time()
        self._duration = 0.0
        self._durations.clear()
