<template>
  <div class="statbar-container">

    <!-- <led class="statbar-led">
      <span>下发</span>
      <diode class="led-green"></diode>
    </led>

    <led class="statbar-led">
      <span>应答</span>
      <diode class="led-yellow"></diode>
    </led> -->

    <coord>
      <span>{{ props.message }}</span>
    </coord>

    <!-- <inlinegcode>
      <gcode>M81M81M81M81M81M81M81M81M81M81M81M81M81M81M81</gcode>
    </inlinegcode> -->
  </div>
</template>

<style scoped>
.statbar-container {
  padding:0.5rem 1.5rem 0.5rem 1.5rem;
  background-color: hsl(var(--card));
  border-top: 1px solid hsl(var(--border));
  border-bottom: 1px solid hsl(var(--border));
}

.statbar-container > * {
  margin-right: 1.5rem;
}

.statbar-led {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: hsl(var(--text));
  font-size: 0.8rem;
}

.statbar-led span {
  opacity: 0.8;
}

.statbar-led diode {
  width: 1.0rem;
  height: 0.25rem;
  border-radius: 10%;
  display: inline-block;
  opacity: 0.3;
  transition: opacity 0.3s ease-in-out;
}

.led-on {
  opacity: 1!important;
}

.led-green {
  background-color: hsl(var(--success));
}

.led-red {
  background-color: hsl(var(--red-400));
}

.led-yellow {
  background-color: hsl(42.1, 100%, 63.9%);
}

coord {
  display: inline-flex;
  align-items: center;
  color: hsl(var(--text));
  font-size: 0.8rem;
  opacity: 0.8;
}

inlinegcode {
  display: inline-flex;
  align-items: center;
  color: hsl(var(--text));
  font-size: 0.8rem;
  opacity: 0.8;
  margin-right:0rem!important;
  float: right;
  max-width: 35%;
  overflow: hidden;
}

</style>

<script lang="ts" setup>
import { onUnmounted, ref, watch, computed } from 'vue';

const props = defineProps({
  message: { type: String, default: '' },
});

// watch(

//   () => props.message,
//   (newMessage) => {
//     if (newMessage) {
//       // 可以在这里添加一些逻辑，比如发送通知或更新状态
//       console.log('新消息:', newMessage);
//     }
//   }
// );

</script>
