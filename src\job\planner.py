
from src.config import config
from src.controller.geval import geval
from math import ceil, fabs
from typing import Tuple, List

class planner:

    class generator:

        # 定义三维点的类型别名，三元组 (x, y, z)
        Point = Tuple[int, int, int]

        # 轴对应的索引映射，方便通过字符访问对应的坐标值
        axes = {"X":0, "Y":1, "Z": 2}

        # 两种走线模式标识
        SerpentineMode = 'S'  # 蛇形走线
        ZahapeMode = 'Z'      # Z形走线

        # 记录每个轴是否反向（起点>终点时为True）
        pathReverseOrder = [False, False, False]

        def __init__(self, start: Point, end: Point, step: Point, order: str = "XYZ", mode: str = "SSZ"):
            """
            初始化路径规划器
            
            参数：
            start: 起点坐标 (x,y,z)
            end: 终点坐标 (x,y,z)
            step: 三轴步长 (sx, sy, sz)
            order: 轴移动优先顺序，如"XYZ"
            mode: 走线模式字符串，每个轴对应一种模式，长度应为3，如"SSZ"
            """
            self.start = start
            self.end = end
            self.step = step
            self.order = order
            self.mode = mode

            # 判断各轴是否需要倒序遍历（起点坐标 > 终点坐标时需要）
            for i in range(len(start)):
                if start[i] > end[i]:
                    self.pathReverseOrder[i] = True

            # 根据order字符串得到轴索引列表，方便按顺序处理
            self.idx = []
            for i in self.order:
                self.idx.append(planner.generator.axes[i])

            # 计算每个轴上总共要走的步数
            self.totalSetps = [0, 0, 0]
            for i in range(len(step)):  # 这里改成step，不是setp
                d = abs(self.start[i] - self.end[i] )
                # 整除时步数是d/step，否则要加1步保证覆盖终点
                self.totalSetps[i] = int(d / self.step[i]) + 1 #+ (1 if d % self.step[i] else 0)

            print(self.idx)
            print(self.pathReverseOrder)
            print(self.totalSetps)

        def pathGeneration(self) -> List[Point]:
            axes_idx = [planner.generator.axes[ch] for ch in self.order]
            starts = [self.start[i] for i in axes_idx]
            ends = [self.end[i] for i in axes_idx]
            steps = [self.step[i] for i in axes_idx]
            modes = [self.mode[i] for i in range(3)]
            totalSteps = [self.totalSetps[i] for i in axes_idx]
            
            path_points = []
        
            def generate_axis(level: int, current_point: List[int], reverse: bool):
                if level == 3:
                    point_xyz = [0, 0, 0]
                    for i, axis_i in enumerate(axes_idx):
                        point_xyz[axis_i] = current_point[i]
                    path_points.append(tuple(point_xyz))
                    return
        
                axis_start = starts[level]
                axis_end = ends[level]
                axis_step = steps[level]
                axis_total = totalSteps[level]
                axis_mode = modes[level]
        
                # 方向标志：起点小于终点为 +1，否则 -1
                step_dir = 1 if axis_end >= axis_start else -1
        
                indices = list(range(axis_total))
                if reverse:
                    indices.reverse()
        
                for idx_i in indices:
                    # 计算当前点坐标
                    coord = axis_start + idx_i * axis_step * step_dir
                    current_point[level] = coord
        
                    next_reverse = reverse
                    if axis_mode == planner.generator.SerpentineMode and idx_i % 2 == 1:
                        next_reverse = not reverse
        
                    generate_axis(level + 1, current_point, next_reverse)
        
            generate_axis(0, [0, 0, 0], False)
            return path_points

    _cfg: config
    _geval: geval
    _geval_domain: dict = {}
    _path: List[generator.Point] = []

    # progress control
    _total_steps: int = 0
    _current_step: int = 0

    def __init__(self, cfg: config):
        self._cfg = cfg
        self._geval = geval()
        self._geval.reg_domain("job", self._cfg.job)
        self._geval.reg_domain("machine", self._cfg.machine)
        self._geval.reg_domain("joystick", self._cfg.joystick)
        self._geval.reg_domain("planner", self._geval_domain)

    def __generate_metadata(self) -> list[str]:
        return [
            ";FLAVOR:Marlin",
            ";TIME:${planner.total_time}",
            ";Generated by Pandapy Job Planner",
            ";Version: 1.0",
            ";Job Arguments:",
            f";  - repeat: {self._cfg.job['job']['repeat']}",
            f";  - scan_mode: {self._cfg.job['job']['scan_mode']}",
            f";  - first_axis: {self._cfg.job['job']['first_axis']}",
            f";  - planner_path: X={self._cfg.job['job']['planner_path']['x']}, Y={self._cfg.job['job']['planner_path']['y']}, Z={self._cfg.job['job']['planner_path']['z']}",
            f";  - step_distance: X={self._cfg.job['job']['step_distance']['x']}, Y={self._cfg.job['job']['step_distance']['y']}, Z={self._cfg.job['job']['step_distance']['z']}",
            f";  - working_area: Start={self._cfg.job['job']['working_area']['start']}, End={self._cfg.job['job']['working_area']['end']}",
            f";  - trig_attr: "
            f"post_delay_ms={self._cfg.job['job']['trig_attr']['post_delay_ms']}ms, "
            f"active_duration_ms={self._cfg.job['job']['trig_attr']['active_duration_ms']}ms, "
            f"after_delay_ms={self._cfg.job['job']['trig_attr']['after_delay_ms']}ms, "
            f"repeat={self._cfg.job['job']['trig_attr']['repeat']}",
            ";End of Job Args",
        ]

    def __generate_initial_code(self) -> list[str]:
        return [

        ]

    def __generate_steps(self):

        def __one_step(x: float, y: float, z: float):

            # go to the next point
            yield f"G0 X{float(x)} Y{float(y)} Z{float(z)}"
            # delay before film
            yield f"G4 P${{job.job.trig_attr.post_delay_ms}}"
            # take film N 次
            repeat = self._cfg.job["job"]["trig_attr"]["repeat"]
            for _ in range(repeat):
                yield f"G4 P${{job.job.trig_attr.after_delay_ms}}"
                yield f"M42 I P${{machine.gpio.cam_trigger}} S0"
                self._current_step += 1
                yield f"; STEP: {self._current_step}"
                yield f"G4 P${{job.job.trig_attr.active_duration_ms}}"
                yield f"M42 I P${{machine.gpio.cam_trigger}} S255"
                yield f"G4 P${{job.job.trig_attr.after_delay_ms}}"

            yield f"; PROGRESS: {self._current_step / self._total_steps }"

        repeat = self._cfg.job["job"]["repeat"]
        for i in range(repeat):
            yield f"; REPEAT: {i}"
            for x, y, z in self._path:
                yield from __one_step(x, y, z)
    
    def __generate_end_code(self) -> list[str]:
        return []

    def prepare(self):

        self._export_state = 0
        self._geval_domain["total_time"] = 0

        order: str[3]
        if self._cfg.job["job"]["scan_mode"] == "z_only":
            order = "ZXY" # Z--
        elif self._cfg.job["job"]["scan_mode"] == "xy_only":
            if self._cfg.job["job"]["first_axis"] == "x":
                order = "XYZ"
            else:
                order = "YXZ"
        elif self._cfg.job["job"]["scan_mode"] == "xyz":
            if self._cfg.job["job"]["first_axis"] == "x":
                order = "ZXY"
            else:
                order = "ZYX"

        table = {
            "X": { "mode": "Z" },
            "Y": { "mode": "Z" },
            "Z": { "mode": "Z" }
        }

        if self._cfg.job["job"]["planner_path"]["x"] == "snake":
            table["X"]["mode"] = "S"
        if self._cfg.job["job"]["planner_path"]["y"] == "snake":
            table["Y"]["mode"] = "S"
        if self._cfg.job["job"]["planner_path"]["z"] == "snake":
            table["Z"]["mode"] = "S"

        # 按 order 顺序抽取选项
        planner_order = order
        planner_mode = [table[i]["mode"] for i in order]

        # full path generation
        pt_start = self._cfg.job["job"]["working_area"]["start"]
        pt_end = self._cfg.job["job"]["working_area"]["end"]
        step = (self._cfg.job["job"]["step_distance"]["x"],
                self._cfg.job["job"]["step_distance"]["y"],
                self._cfg.job["job"]["step_distance"]["z"],
        )

        generator = planner.generator(pt_start, pt_end, step, planner_order, planner_mode)
        self._path = generator.pathGeneration()
        self._total_steps = len(self._path) * self._cfg.job["job"]["repeat"] * self._cfg.job["job"]["trig_attr"]["repeat"]
        self._current_step = 0

    def get_point_count(self) -> int:
        return len(self._path)

    def get_total_steps(self) -> int:
        return self._total_steps

    def export_gcode(self):

        def __raw_code_generator():
            # header
            if self._export_state == 0:
                yield from self.__generate_metadata()
                yield from self.__generate_initial_code()
                self._export_state = 1

            # main steps
            if self._export_state == 1:
                # 假设 steps 是每批次要生成的行数，也可以去掉参数直接 yield 全部
                yield from self.__generate_steps()
                self._export_state = 2

            # footer
            if self._export_state == 2:
                yield from self.__generate_end_code()

        # 真正对外的 generator：在产出每行前做 eval/错误处理
        for raw in __raw_code_generator():
            try:
                line = self._geval.eval(raw)
                if line is not None:
                    yield line
            except Exception as e:
                err = f"; Error evaluating G-code expression '{raw}': {e}"
                print(err)
                yield err
