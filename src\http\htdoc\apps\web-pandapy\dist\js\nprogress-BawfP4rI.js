import{E as w}from"../jse/index-index-Y3_OtjO-.js";function E(m,b){for(var t=0;t<b.length;t++){const s=b[t];if(typeof s!="string"&&!Array.isArray(s)){for(const f in s)if(f!=="default"&&!(f in m)){const l=Object.getOwnPropertyDescriptor(s,f);l&&Object.defineProperty(m,f,l.get?l:{enumerable:!0,get:()=>s[f]})}}}return Object.freeze(Object.defineProperty(m,Symbol.toStringTag,{value:"Module"}))}var y={exports:{}};var q=y.exports,x;function M(){return x||(x=1,function(m,b){(function(t,s){m.exports=s()})(q,function(){var t={};t.version="0.2.0";var s=t.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};t.configure=function(r){var e,n;for(e in r)n=r[e],n!==void 0&&r.hasOwnProperty(e)&&(s[e]=n);return this},t.status=null,t.set=function(r){var e=t.isStarted();r=f(r,s.minimum,1),t.status=r===1?null:r;var n=t.render(!e),o=n.querySelector(s.barSelector),a=s.speed,p=s.easing;return n.offsetWidth,T(function(i){s.positionUsing===""&&(s.positionUsing=t.getPositioningCSS()),v(o,O(r,a,p)),r===1?(v(n,{transition:"none",opacity:1}),n.offsetWidth,setTimeout(function(){v(n,{transition:"all "+a+"ms linear",opacity:0}),setTimeout(function(){t.remove(),i()},a)},a)):setTimeout(i,a)}),this},t.isStarted=function(){return typeof t.status=="number"},t.start=function(){t.status||t.set(0);var r=function(){setTimeout(function(){t.status&&(t.trickle(),r())},s.trickleSpeed)};return s.trickle&&r(),this},t.done=function(r){return!r&&!t.status?this:t.inc(.3+.5*Math.random()).set(1)},t.inc=function(r){var e=t.status;return e?(typeof r!="number"&&(r=(1-e)*f(Math.random()*e,.1,.95)),e=f(e+r,0,.994),t.set(e)):t.start()},t.trickle=function(){return t.inc(Math.random()*s.trickleRate)},function(){var r=0,e=0;t.promise=function(n){return!n||n.state()==="resolved"?this:(e===0&&t.start(),r++,e++,n.always(function(){e--,e===0?(r=0,t.done()):t.set((r-e)/r)}),this)}}(),t.render=function(r){if(t.isRendered())return document.getElementById("nprogress");P(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=s.template;var n=e.querySelector(s.barSelector),o=r?"-100":l(t.status||0),a=document.querySelector(s.parent),p;return v(n,{transition:"all 0 linear",transform:"translate3d("+o+"%,0,0)"}),s.showSpinner||(p=e.querySelector(s.spinnerSelector),p&&C(p)),a!=document.body&&P(a,"nprogress-custom-parent"),a.appendChild(e),e},t.remove=function(){k(document.documentElement,"nprogress-busy"),k(document.querySelector(s.parent),"nprogress-custom-parent");var r=document.getElementById("nprogress");r&&C(r)},t.isRendered=function(){return!!document.getElementById("nprogress")},t.getPositioningCSS=function(){var r=document.body.style,e="WebkitTransform"in r?"Webkit":"MozTransform"in r?"Moz":"msTransform"in r?"ms":"OTransform"in r?"O":"";return e+"Perspective"in r?"translate3d":e+"Transform"in r?"translate":"margin"};function f(r,e,n){return r<e?e:r>n?n:r}function l(r){return(-1+r)*100}function O(r,e,n){var o;return s.positionUsing==="translate3d"?o={transform:"translate3d("+l(r)+"%,0,0)"}:s.positionUsing==="translate"?o={transform:"translate("+l(r)+"%,0)"}:o={"margin-left":l(r)+"%"},o.transition="all "+e+"ms "+n,o}var T=function(){var r=[];function e(){var n=r.shift();n&&n(e)}return function(n){r.push(n),r.length==1&&e()}}(),v=function(){var r=["Webkit","O","Moz","ms"],e={};function n(i){return i.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(u,c){return c.toUpperCase()})}function o(i){var u=document.body.style;if(i in u)return i;for(var c=r.length,g=i.charAt(0).toUpperCase()+i.slice(1),d;c--;)if(d=r[c]+g,d in u)return d;return i}function a(i){return i=n(i),e[i]||(e[i]=o(i))}function p(i,u,c){u=a(u),i.style[u]=c}return function(i,u){var c=arguments,g,d;if(c.length==2)for(g in u)d=u[g],d!==void 0&&u.hasOwnProperty(g)&&p(i,g,d);else p(i,c[1],c[2])}}();function S(r,e){var n=typeof r=="string"?r:h(r);return n.indexOf(" "+e+" ")>=0}function P(r,e){var n=h(r),o=n+e;S(n,e)||(r.className=o.substring(1))}function k(r,e){var n=h(r),o;S(r,e)&&(o=n.replace(" "+e+" "," "),r.className=o.substring(1,o.length-1))}function h(r){return(" "+(r.className||"")+" ").replace(/\s+/gi," ")}function C(r){r&&r.parentNode&&r.parentNode.removeChild(r)}return t})}(y)),y.exports}var N=M();const U=w(N),j=E({__proto__:null,default:U},[N]);export{j as n};
