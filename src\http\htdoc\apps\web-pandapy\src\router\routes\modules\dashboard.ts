import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.workspace.title'),
    },
    name: 'Workspace',
    path: '/workspace',
    children: [
      {
        name: 'Job',
        path: '/workspace/job',
        component: () => import('#/views/workspace/job.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.workspace.workspace'),
          ignoreAccess: true,
        },
      },
      {
        name: 'Preferences',
        path: '/workspace/prefrences',
        component: () => import('#/views/workspace/preferences.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.workspace.preferences'),
          ignoreAccess: true,
        },
      },
      // {
      //   name: 'GCodeController',
      //   path: '/workspace/gcode-controller',
      //   component: () => import('#/views/workspace/gcode-controller.vue'),
      //   meta: {
      //     icon: 'carbon:workspace',
      //     title: $t('page.workspace.gcodeController'),
      //     ignoreAccess: true,
      //   },
      // },
    ],
  },
];

export default routes;
